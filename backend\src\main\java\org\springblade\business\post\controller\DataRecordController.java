package org.springblade.business.post.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.business.post.service.IDataRecordService;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据记录 控制器
 * 用于查询浏览记录、点赞记录、反馈记录
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/datarecord")
@io.swagger.v3.oas.annotations.tags.Tag(name = "数据记录", description = "数据记录查询接口")
public class DataRecordController {

	private IDataRecordService dataRecordService;

	/**
	 * 分页查询浏览记录
	 */
	@GetMapping("/view/list")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "分页查询浏览记录", description = "根据关联ID和类型查询浏览记录")
	public R<IPage<Map<String, Object>>> getViewRecords(
			@Parameter(description = "关联ID") @RequestParam Long relevancyId,
			@Parameter(description = "类型") @RequestParam(defaultValue = "0") String type,
			Query query) {

		IPage<Map<String, Object>> pages = dataRecordService.getViewRecords(
			new Page<>(query.getCurrent(), query.getSize()), relevancyId, type);
		return R.data(pages);
	}

	/**
	 * 分页查询点赞记录
	 */
	@GetMapping("/like/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页查询点赞记录", description = "根据关联ID和类型查询点赞记录")
	public R<IPage<Map<String, Object>>> getLikeRecords(
			@Parameter(description = "关联ID") @RequestParam Long relevancyId,
			@Parameter(description = "类型") @RequestParam(defaultValue = "0") String type,
			Query query) {

		IPage<Map<String, Object>> pages = dataRecordService.getLikeRecords(
			new Page<>(query.getCurrent(), query.getSize()), relevancyId, type);
		return R.data(pages);
	}

	/**
	 * 分页查询反馈记录
	 */
	@GetMapping("/feedback/list")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页查询反馈记录", description = "根据关联ID查询反馈记录")
	public R<IPage<Map<String, Object>>> getFeedbackRecords(
			@Parameter(description = "关联ID") @RequestParam Long relevancyId,
			Query query) {

		IPage<Map<String, Object>> pages = dataRecordService.getFeedbackRecords(
			new Page<>(query.getCurrent(), query.getSize()), relevancyId);
		return R.data(pages);
	}

	/**
	 * 分页查询收藏记录
	 */
	@GetMapping("/favorite/list")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "分页查询收藏记录", description = "根据关联ID和类型查询收藏记录")
	public R<IPage<Map<String, Object>>> getFavoriteRecords(
			@Parameter(description = "关联ID") @RequestParam Long relevancyId,
			@Parameter(description = "类型") @RequestParam String type,
			Query query) {

		IPage<Map<String, Object>> pages = dataRecordService.getFavoriteRecords(
			new Page<>(query.getCurrent(), query.getSize()), relevancyId, type);
		return R.data(pages);
	}

}
