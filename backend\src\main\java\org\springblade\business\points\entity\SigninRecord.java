package org.springblade.business.points.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 签到记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("urb_signin_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "签到记录对象")
public class SigninRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 用户userID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 签到日期
     */
    @Schema(description = "签到日期")
    private LocalDate signinDate;

    /**
     * 签到时间
     */
    @Schema(description = "签到时间")
    private LocalDateTime signinTime;

    /**
     * 签到年份
     */
    @Schema(description = "签到年份")
    private Integer year;

    /**
     * 签到月份
     */
    @Schema(description = "签到月份")
    private Integer month;

    /**
     * 签到日
     */
    @Schema(description = "签到日")
    private Integer day;

    /**
     * 签到星期（1-7，对应周一到周日）
     */
    @Schema(description = "签到星期")
    private Integer weekDay;

    /**
     * 获得积分
     */
    @Schema(description = "获得积分")
    private Integer points;

    /**
     * 连续签到奖励积分
     */
    @Schema(description = "连续签到奖励积分")
    private Integer continuousReward;

    /**
     * 连续签到天数
     */
    @Schema(description = "连续签到天数")
    private Integer continuousDays;

    /**
     * 签到类型（NORMAL：正常签到，MAKEUP：补签）
     */
    @Schema(description = "签到类型")
    private String signinType;

    /**
     * 签到IP
     */
    @Schema(description = "签到IP")
    private String signinIp;

    /**
     * 签到设备信息
     */
    @Schema(description = "签到设备信息")
    private String deviceInfo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 状态（0：无效，1：有效）
     */
    @Schema(description = "状态（0：无效，1：有效）")
    private Integer status;
}
