/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@TableName("urb_job_seeking")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "JobSeeking对象")
public class JobSeeking extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 关联帖子表ID
     */
    @Schema(description = "关联帖子表ID")
    private Long postId;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 求职类型
     */
    @Schema(description = "求职类型")
    private String seekingType;
    /**
     * 期望岗位
     */
    @Schema(description = "期望岗位")
    private String expectedPosition;
    /**
     * 薪资要求
     */
    @Schema(description = "薪资要求")
    private String salaryExpectation;
    /**
     * 职位偏好
     */
    @Schema(description = "职位偏好")
    private String positionPreference;


}
