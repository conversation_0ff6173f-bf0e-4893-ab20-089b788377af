/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service;

import com.baomidou.mybatisplus.core.metadata.IPage;

import jakarta.validation.Valid;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.AdminTagCreateRequest;
import org.springblade.business.post.vo.TagVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 信息标签 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ITagService extends BaseService<Tag> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param tag
	 * @return
	 */
	IPage<TagVO> selectTagPage(IPage<TagVO> page, TagVO tag);

	/**
	 * 根据分类获取标签
	 *
	 * @param categoryId 分类ID
	 * @return 标签列表
	 */
	List<Tag> getTagsByCategory(Long categoryId);

	/**
	 * 创建标签
	 *
	 * @param tagName    标签名称
	 * @param color
	 * @param categoryId 分类ID
	 * @return 标签信息
	 */
	Tag createTag(String tagName, String color, Long categoryId);

	/**
	 * 启用/禁用标签
	 *
	 * @param id 标签ID
	 * @param enabled 是否启用
	 * @return 是否成功
	 */
	Boolean enableTag(Long id, Boolean enabled);

	/**
	 * 获取热门标签
	 *
	 * @param limit 限制数量
	 * @return 标签列表
	 */
	List<Tag> getHotTags(Integer limit);

	/**
	 * 获取所有可用标签
	 *
	 * @return 可用标签列表
	 */
	List<Tag> getAvailableTags();

	/**
	 * 根据标签名称搜索标签
	 *
	 * @param tagName 标签名称
	 * @return 标签列表
	 */
	List<Tag> searchTagsByName(String tagName);

	/**
	 * 更新标签使用次数
	 *
	 * @param tagIds 标签ID列表
	 */
	void updateTagUseCount(List<String> tagIds);

	Tag createTag(AdminTagCreateRequest tagReq);

    List<Tag> getTagsByCategoryAndType(Long categoryId, int i);

	List<Tag> getTagsByType(int i);

    boolean removeClearBind(List<Long> ids);

    boolean saveTag(@Valid Tag tag);

	boolean updateTagById(@Valid Tag tag);

	boolean saveOrUpdateTag(@Valid Tag tag);
}
