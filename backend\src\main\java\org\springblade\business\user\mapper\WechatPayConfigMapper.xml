<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.WechatPayConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wechatPayConfigResultMap" type="org.springblade.business.user.entity.WechatPayConfig">
        <id column="id" property="id"/>
        <result column="config_name" property="configName"/>
        <result column="app_id" property="appId"/>
        <result column="mch_id" property="mchId"/>
        <result column="api_key" property="apiKey"/>
        <result column="api_v3_key" property="apiV3Key"/>
        <result column="cert_path" property="certPath"/>
        <result column="cert_serial_no" property="certSerialNo"/>
        <result column="private_key_path" property="privateKeyPath"/>
        <result column="notify_url" property="notifyUrl"/>
        <result column="is_sandbox" property="isSandbox"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_default" property="isDefault"/>
        <result column="priority" property="priority"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, config_name, app_id, mch_id, api_key, api_v3_key, cert_path, cert_serial_no,
        private_key_path, notify_url, is_sandbox, is_enabled, is_default, priority, remark,
        create_time, create_user, update_time, update_user, tenant_id,is_deleted
    </sql>

    <!-- 根据配置名称查询配置 -->
    <select id="selectByConfigName" resultMap="wechatPayConfigResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM urb_wechat_pay_config
        WHERE config_name = #{configName}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据商户号查询配置 -->
    <select id="selectByMchId" resultMap="wechatPayConfigResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM urb_wechat_pay_config
        WHERE mch_id = #{mchId}
        AND is_deleted = 0
        AND is_enabled = 1
        ORDER BY priority DESC, create_time DESC
        LIMIT 1
    </select>

    <!-- 查询默认配置 -->
    <select id="selectDefaultConfig" resultMap="wechatPayConfigResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM urb_wechat_pay_config
        WHERE is_default = 1
        AND is_enabled = 1
        AND is_deleted = 0
        ORDER BY priority DESC, create_time DESC
        LIMIT 1
    </select>

    <!-- 查询所有启用的配置（按优先级排序） -->
    <select id="selectEnabledConfigs" resultMap="wechatPayConfigResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM urb_wechat_pay_config
        WHERE is_enabled = 1
        AND is_deleted = 0
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 查询最佳配置（优先级最高的启用配置） -->
    <select id="selectBestConfig" resultMap="wechatPayConfigResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM urb_wechat_pay_config
        WHERE is_enabled = 1
        AND is_deleted = 0
        ORDER BY
            CASE WHEN is_default = 1 THEN 1 ELSE 2 END,
            priority DESC,
            create_time DESC
        LIMIT 1
    </select>

    <!-- 设置默认配置 -->
    <update id="setDefaultConfig">
        UPDATE urb_wechat_pay_config
        SET is_default = CASE WHEN id = #{configId} THEN 1 ELSE 0 END,
            update_time = NOW()
        WHERE is_deleted = 0
    </update>

    <!-- 清除所有默认配置标记 -->
    <update id="clearDefaultConfig">
        UPDATE urb_wechat_pay_config
        SET is_default = 0,
            update_time = NOW()
        WHERE is_deleted = 0
    </update>

</mapper>
