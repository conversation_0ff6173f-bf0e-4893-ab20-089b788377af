/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 订阅计划表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("urb_subscription_plan")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订阅计划表")
public class SubscriptionPlan extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 计划名称
     */
    @Schema(description = "计划名称")
    private String planName;
    /**
     * 计划描述
     */
    @Schema(description = "计划描述")
    private String planDescription;
    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;
    /**
     * 有效期（天）
     */
    @Schema(description = "有效期（天）")
    private Integer duration;
    /**
     * 计划类型（MONTHLY：月付，YEARLY：年付，CUSTOM：自定义）
     */
    @Schema(description = "计划类型（MONTHLY：月付，YEARLY：年付，CUSTOM：自定义）")
    private String planType;
    /**
     * 最大功能数量限制
     */
    @Schema(description = "最大功能数量限制")
    private Integer maxFeatures;
    /**
     * 是否热门推荐
     */
    @Schema(description = "是否热门推荐")
    private Byte isPopular;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;
    /**
     * 计划图标
     */
    @Schema(description = "计划图标")
    private String icon;

	/**
     * 功能信息
     */
	@Schema(description = "功能信息")
	@TableField(exist = false)
	private Feature feature;


}
