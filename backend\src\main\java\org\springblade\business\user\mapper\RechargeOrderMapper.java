/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.business.user.entity.RechargeOrder;
import org.springblade.business.user.vo.RechargeOrderVO;

import java.util.List;
import java.util.Map;

/**
 * 充值订单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface RechargeOrderMapper extends BaseMapper<RechargeOrder> {




	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rechargeOrder
	 * @return
	 */
	List<RechargeOrderVO> selectRechargeOrderPage(IPage page, RechargeOrderVO rechargeOrder);

    /**
     * 分页查询用户充值订单
     *
     * @param page   分页对象
     * @param userId 用户ID
     * @return 充值订单列表
     */
    IPage<RechargeOrder> selectUserRechargeOrderPage(IPage<RechargeOrder> page, @Param("userId") Long userId);

    /**
     * 根据订单号查询充值订单
     *
     * @param orderNo 订单号
     * @return 充值订单
     */
    RechargeOrder selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据微信交易号查询充值订单
     *
     * @param transactionId 微信交易号
     * @return 充值订单
     */
    RechargeOrder selectByWxTransactionId(@Param("transactionId") String transactionId);

    /**
     * 查询用户充值统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserRechargeStats(@Param("userId") Long userId);

    /**
     * 查询过期未支付的订单
     *
     * @param expireMinutes 过期分钟数
     * @param limit         限制条数
     * @return 过期订单列表
     */
    List<RechargeOrder> selectExpiredOrders(@Param("expireMinutes") Integer expireMinutes,
                                           @Param("limit") Integer limit);

    /**
     * 批量更新订单状态
     *
     * @param orderIds  订单ID列表
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @return 更新条数
     */
    int batchUpdateOrderStatus(@Param("orderIds") List<Long> orderIds,
                              @Param("oldStatus") String oldStatus,
                              @Param("newStatus") String newStatus);

    /**
     * 查询待同步的订单（最近创建但状态为PENDING或PAID的订单）
     *
     * @param minutes 查询最近多少分钟内的订单
     * @param limit   限制条数
     * @return 待同步订单列表
     */
    List<RechargeOrder> selectPendingOrdersForSync(@Param("minutes") Integer minutes,
                                                  @Param("limit") Integer limit);

    /**
     * 查询今日充值统计
     *
     * @return 今日统计信息
     */
    Map<String, Object> selectTodayRechargeStats();

    /**
     * 查询指定时间范围内的充值记录
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param userId        用户ID（可选）
     * @param orderStatus   订单状态（可选）
     * @param paymentStatus 支付状态（可选）
     * @return 充值记录列表
     */
    List<RechargeOrder> selectRechargeOrdersByDateRange(@Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("userId") Long userId,
                                                       @Param("orderStatus") String orderStatus,
                                                       @Param("paymentStatus") String paymentStatus);

    /**
     * 查询用户最近的充值订单
     *
     * @param userId 用户ID
     * @param limit  限制条数
     * @return 最近充值订单列表
     */
    List<RechargeOrder> selectUserRecentOrders(@Param("userId") Long userId,
                                              @Param("limit") Integer limit);

    /**
     * 根据微信商户订单号查询
     *
     * @param wxOutTradeNo 微信商户订单号
     * @return 充值订单
     */
    RechargeOrder selectByWxOutTradeNo(@Param("wxOutTradeNo") String wxOutTradeNo);

    /**
     * 统计充值成功率
     *
     * @param days 统计最近多少天
     * @return 成功率统计信息
     */
    Map<String, Object> selectRechargeSuccessRate(@Param("days") Integer days);
}
