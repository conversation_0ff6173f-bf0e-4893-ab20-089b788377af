package org.springblade.business.points.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.business.points.dto.SigninDTO;
import org.springblade.business.points.entity.SigninRecord;
import org.springblade.business.points.vo.SigninRecordVO;
import org.springblade.business.points.vo.SigninVO;

import java.util.List;
import java.util.Map;

/**
 * 签到服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ISigninService extends IService<SigninRecord> {

    /**
     * 分页查询签到记录
     *
     * @param page 分页参数
     * @param signinRecord 查询条件
     * @return 分页结果
     */
    IPage<SigninRecordVO> selectSigninRecordPage(IPage<SigninRecordVO> page, SigninRecord signinRecord);

    /**
     * 获取用户签到信息
     *
     * @param userId 用户userId
     * @return 签到信息
     */
    List<SigninRecordVO> getSigninInfo(String userId);

    /**
     * 执行签到
     *
     * @param signinDTO 签到参数
     * @return 签到结果
     */
    SigninVO doSignin(SigninDTO signinDTO);

    /**
     * 补签
     *
     * @param userId 用户userId
     * @param date 补签日期
     * @return 补签结果
     */
    SigninVO makeUpSignin(String userId, String date, String deviceInfo,String signinIp);

    /**
     * 获取用户某月签到记录
     *
     * @param userId 用户userId
     * @param year 年份
     * @param month 月份
     * @return 签到记录列表
     */
    List<Map<String, Object>> getMonthSigninRecord(String userId, Integer year, Integer month);

    /**
     * 获取连续签到奖励配置
     *
     * @return 奖励配置
     */
    List<Map<String, Object>> getContinuousRewards();

    /**
     * 获取签到统计
     *
     * @param userId 用户userId
     * @return 统计信息
     */
    Map<String, Object> getSigninStats(String userId);

    /**
     * 检查用户今日是否已签到
     *
     * @param userId 用户userId
     * @return 是否已签到
     */
    boolean isTodaySigned(String userId);

	Integer getContinuousDays(String userId);

	Map<String, Object> getLottery(String startDate, String endDate);

	Boolean IsWin();

	List<Map<String, Object>> getWinRate(String startDate, String endDate);

	/**
	 * 分页获取签到记录列表
	 *
	 * @param userId 用户ID
	 * @param page 页码
	 * @param size 每页大小
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 分页签到记录
	 */
	Map<String, Object> getSigninRecords(String userId, Integer page, Integer size, String startDate, String endDate);

	/**
	 * 获取签到统计汇总
	 *
	 * @param userId 用户ID
	 * @return 统计汇总信息
	 */
	Map<String, Object> getSigninSummary(String userId);
}
