/**
 * Copyright (c) 2018-2099, Chill <PERSON>ang 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

import org.springblade.business.user.entity.UserBalanceLog;

import java.math.BigDecimal;

/**
 * 用户余额服务接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IUserBalanceService {

    /**
     * 获取用户钱包余额
     *
     * @param userId 用户ID
     * @return 用户钱包余额（单位：元）
     */
    BigDecimal getUserWalletBalance(Long userId);

    /**
     * 增加用户钱包余额
     *
     * @param userId        用户ID
     * @param amount        增加金额（单位：元）
     * @param changeType    变动类型
     * @param businessType  业务类型
     * @param businessId    业务关联ID
     * @param orderId       关联订单ID
     * @param remark        备注
     * @return 操作是否成功
     */
    boolean addWalletBalance(Long userId, BigDecimal amount, String changeType, String businessType,
                            String businessId, String orderId, String remark);

    /**
     * 扣减用户钱包余额
     *
     * @param userId        用户ID
     * @param amount        扣减金额（单位：元）
     * @param changeType    变动类型
     * @param businessType  业务类型
     * @param businessId    业务关联ID
     * @param orderId       关联订单ID
     * @param remark        备注
     * @return 操作是否成功
     */
    boolean deductWalletBalance(Long userId, BigDecimal amount, String changeType, String businessType,
                               String businessId, String orderId, String remark);

    /**
     * 检查用户钱包余额是否足够
     *
     * @param userId 用户ID
     * @param amount 需要的金额（单位：元）
     * @return 余额是否足够
     */
    boolean checkWalletBalance(Long userId, BigDecimal amount);

    /**
     * 冻结用户钱包余额
     *
     * @param userId 用户ID
     * @param amount 冻结金额（单位：元）
     * @param orderId 关联订单ID
     * @param remark 备注
     * @return 操作是否成功
     */
    boolean freezeWalletBalance(Long userId, BigDecimal amount, String orderId, String remark);

    /**
     * 解冻用户钱包余额
     *
     * @param userId 用户ID
     * @param amount 解冻金额（单位：元）
     * @param orderId 关联订单ID
     * @param remark 备注
     * @return 操作是否成功
     */
    boolean unfreezeWalletBalance(Long userId, BigDecimal amount, String orderId, String remark);

    /**
     * 记录钱包余额变动日志
     *
     * @param userId        用户ID
     * @param changeAmount  变动金额
     * @param balanceBefore 变动前余额
     * @param balanceAfter  变动后余额
     * @param changeType    变动类型
     * @param businessType  业务类型
     * @param businessId    业务关联ID
     * @param orderId       关联订单ID
     * @param remark        备注
     * @return 日志记录是否成功
     */
    boolean logWalletBalanceChange(Long userId, BigDecimal changeAmount, BigDecimal balanceBefore, BigDecimal balanceAfter,
                                  String changeType, String businessType, String businessId,
                                  String orderId, String remark);
}
