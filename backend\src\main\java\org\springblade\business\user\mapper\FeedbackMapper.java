/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.vo.FeedbackVO;

import java.util.List;
import java.util.Map;

/**
 * 用户反馈 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Mapper
public interface FeedbackMapper extends BaseMapper<Feedback> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param feedback
	 * @return
	 */
	IPage<FeedbackVO> selectFeedbackPage(IPage page,@Param("model")FeedbackVO feedback);

	IPage<FeedbackVO> selectFeedbackPageByUserId(Page<FeedbackVO> page, @Param("userId") Long userId);

	boolean InsertFeedbackHelpful(@Param("model") Map<String, Object> model);

	Boolean addTag(@Param("model") Map<String, Object> model);

	List<Map<String, Object>> getTagsByCategory(Long categoryId);

	List<String> getHotTags();

	/**
	 * 移除反馈标签
	 * @param categoryId 分类ID
	 * @param tagId 标签ID
	 * @return 是否成功
	 */
	boolean removeTag(@Param("categoryId") Long categoryId, @Param("tagId") Long tagId);

	/**
	 * 插入评论点赞记录
	 * @param model 点赞数据
	 * @return 是否成功
	 */
	boolean insertCommentLike(@Param("model") Map<String, Object> model);

	/**
	 * 删除评论点赞记录
	 * @param model 点赞数据
	 * @return 是否成功
	 */
	boolean deleteCommentLike(@Param("model") Map<String, Object> model);

	/**
	 * 获取所有反馈标签
	 * @return 反馈标签列表
	 */
	List<Map<String, Object>> getAllFeedbackTags();

	/**
	 * 获取反馈详情（包含帖子信息）
	 * @param feedback 反馈查询条件
	 * @return 反馈详情
	 */
	FeedbackVO getFeedbackDetail(@Param("model") Feedback feedback);

	/**
	 * 获取用户反馈数量
	 * @param id 用户ID
	 * @return 用户反馈数量
	 */
    Long getFeedbackCountByUserId(Long id);
}
