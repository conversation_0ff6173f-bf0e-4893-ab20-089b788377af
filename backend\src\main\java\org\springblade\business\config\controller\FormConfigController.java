/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.config.entity.FormConfig;
import org.springblade.business.config.service.IFormConfigService;
import org.springblade.business.config.vo.FormConfigVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.config.wrapper.FormConfigWrapper;
import org.springblade.core.boot.ctrl.BladeController;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/formconfig")
@io.swagger.v3.oas.annotations.tags.Tag(name = "", description = "接口")
public class FormConfigController extends BladeController {

	private IFormConfigService formConfigService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入formConfig")
	public R<FormConfigVO> detail(FormConfig formConfig) {
		FormConfig detail = formConfigService.getOne(Condition.getQueryWrapper(formConfig));
		return R.data(FormConfigWrapper.build().entityVO(detail));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入formConfig")
	public R<IPage<FormConfigVO>> list(FormConfig formConfig, Query query) {
		IPage<FormConfig> pages = formConfigService.page(Condition.getPage(query), Condition.getQueryWrapper(formConfig));
		return R.data(FormConfigWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入formConfig")
	public R<IPage<FormConfigVO>> page(FormConfigVO formConfig, Query query) {
		IPage<FormConfigVO> pages = formConfigService.selectFormConfigPage(Condition.getPage(query), formConfig);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入formConfig")
	public R save(@Valid @RequestBody FormConfig formConfig) {
		boolean result = false;
		try {
			result = formConfigService.saveFromConfig(formConfig);
		} catch (DuplicateKeyException e) {
			throw new ServiceException("表单已存在");
		}
		return R.data(result);
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入formConfig")
	public R update(@Valid @RequestBody FormConfig formConfig) {
		boolean result = false;
		try {
			result = formConfigService.updateFromConfigById(formConfig);
		} catch (DuplicateKeyException e) {
			throw new ServiceException("表单已存在");
		}
		return R.status(result);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入formConfig")
	public R submit(@Valid @RequestBody FormConfig formConfig) {
		boolean result = false;
		try {
			result = formConfigService.saveOrUpdateFromConfig(formConfig);
		} catch (DuplicateKeyException e) {
			throw new ServiceException("表单已存在");
		}
		return R.status(result);
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = formConfigService.deleteLogicFromConfig(ids);
		return R.status(result);
	}


}
