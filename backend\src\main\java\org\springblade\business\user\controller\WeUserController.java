/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.points.service.ISigninService;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.business.user.vo.WeUserVO;
import org.springblade.business.user.wrapper.UserWrapper;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户信息 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/user")
@io.swagger.v3.oas.annotations.tags.Tag(name = "用户信息", description = "用户信息接口")
public class WeUserController extends BladeController {

	private IWeUserService userService;

	private ISigninService signinService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入user")
	public R<WeUserVO> detail(WeUserVO user) {
		return R.data(userService.getDetail(user));
	}

	/**
	 * 分页 用户信息
	 */
	@GetMapping("/dict")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "查所有用户")
	public R<List<WeUser>> list() {
		return R.data(userService.list());
	}

	/**
	 * 自定义分页 用户信息
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入user")
	public R<IPage<WeUserVO>> page(WeUserVO user, Query query) {
		IPage<WeUserVO> pages = userService.selectUserPage(Condition.getPage(query), user);
		return R.data(pages);
	}

	/**
	 * 新增 用户信息
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入user")
	public R save(@Valid @RequestBody WeUser user) {
		return R.status(userService.save(user));
	}

	/**
	 * 修改 用户信息
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入user")
	public R update(@Valid @RequestBody WeUser user) {
		return R.status(userService.updateById(user));
	}

	/**
	 * 新增或修改 用户信息
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入user")
	public R submit(@Valid @RequestBody WeUser user) {
		return R.status(userService.saveOrUpdate(user));
	}


	/**
	 * 删除 用户信息
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 抽奖
	 */
	@GetMapping("/lottery")
	@Operation(summary = "签到抽奖", description = "根据用户签到次数进行抽奖，签到越多中奖概率越高")
	public R<Map<String, Object>> lottery(@RequestParam String startDate,@RequestParam String endDate) {
		return R.data(signinService.getLottery(startDate,endDate));
	}

	/**
	 * 获取每个人的获奖概率
	 */
	@GetMapping("/winRate")
	@Operation(summary = "获取每个人的获奖概率", description = "根据用户签到次数进行抽奖，签到越多中奖概率越高")
	public R<List<Map<String, Object>>> winRate(@RequestParam String startDate, @RequestParam String endDate) {
		return R.data(signinService.getWinRate(startDate,endDate));
	}
}
