/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.business.user.entity.WechatPayConfig;

import java.util.List;

/**
 * 微信支付配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface WechatPayConfigMapper extends BaseMapper<WechatPayConfig> {

    /**
     * 根据配置名称查询配置
     *
     * @param configName 配置名称
     * @return 微信支付配置
     */
    WechatPayConfig selectByConfigName(@Param("configName") String configName);

    /**
     * 根据商户号查询配置
     *
     * @param mchId 商户号
     * @return 微信支付配置
     */
    WechatPayConfig selectByMchId(@Param("mchId") String mchId);

    /**
     * 查询默认配置
     *
     * @return 默认微信支付配置
     */
    WechatPayConfig selectDefaultConfig();

    /**
     * 查询所有启用的配置（按优先级排序）
     *
     * @return 启用的配置列表
     */
    List<WechatPayConfig> selectEnabledConfigs();

    /**
     * 查询最佳配置（优先级最高的启用配置）
     *
     * @return 最佳配置
     */
    WechatPayConfig selectBestConfig();

    /**
     * 设置默认配置（先清除所有默认标记，再设置指定配置为默认）
     *
     * @param configId 配置ID
     * @return 更新条数
     */
    int setDefaultConfig(@Param("configId") Long configId);

    /**
     * 清除所有默认配置标记
     *
     * @return 更新条数
     */
    int clearDefaultConfig();
}
