/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.business.post.dto.PostStatsDTO;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.post.entity.JobSeeking;
import org.springblade.business.post.entity.PostCarpool;
import org.springblade.business.post.entity.JobOffer;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 百事通信息贴视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "百事通信息贴")
public class SupPostVO extends SupPost {
	@Serial
	private static final long serialVersionUID = 1L;
	private LocalDateTime viewTime;

	private Long viewId;

	/**
	 * 距离
	 */
	@Schema(description = "距离 单位为km")
	private BigDecimal distance;

	//顺风车
	private PostCarpool carpool;

	//招聘
	private JobOffer jobOffer;

	//求职
	private JobSeeking jobSeeking;

	//用户信息
	private WeUser user;

	/**
	 * 分类信息（非数据库字段）
	 */
	private Category category;
	/**
	 * 标签列表（非数据库字段）
	 */
	private List<Tag> tagList;

	private PostStatsDTO stats;

	/**
	 * 查询范围
	 */
	@Schema(description = "查询范围")
	private Integer scope;

	@Schema(description = "搜索经度")
	private BigDecimal searchLongitude;

	@Schema(description = "搜索纬度")
	private BigDecimal searchLatitude;

	/**
	 * 排序模式
	 * time: 按时间排序（P1模式）
	 * distance: 按距离排序（P2模式）
	 */
	@Schema(description = "排序模式: time-按时间排序, distance-按距离排序")
	private String sortMode;

	/**
	 * 地图搜索下的帖子距离用户距离
	 */
	@Schema(description = "地图搜索下的帖子距离用户距离")
	private BigDecimal mapDistance;

	/**
	 * 搜索关键词
	 */
	@Schema(description = "搜索关键词")
	private String searchKeyWord;

}
