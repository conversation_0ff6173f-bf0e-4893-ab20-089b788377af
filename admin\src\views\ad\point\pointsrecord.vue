<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.pointsrecord_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
      </template>

      <!-- 用户插槽 -->
      <template #createUser="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.createUser)"
          class="user-link">
          {{ getUserNickname(row.createUser) }}
        </el-button>
      </template>
    </avue-crud>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model="userDetailVisible"
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/pointsrecord";
  import {getUserDict} from "@/api/ad/user";
  import {mapGetters} from "vuex";
  import userInfoMixin from '@/mixins/userInfoMixin';

  export default {
    mixins: [userInfoMixin],
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          defaultSort: {
            prop: 'operateTime',
            order: 'descending'
          },
          column: [
            {
              label: "用户",
              prop: "createUser",
              type: "select",
              width: 150,
              dicData: [],
              props: {
                label: "nickname",
                value: "id"
              },
              search: true,
              slot: true,
              addDisplay: false,
              editDisplay: false
            },
            {
              label: "积分变动数量（1数为获得，0数为消费）",
              prop: "points",
              type: "number",
              sortable: true,
              rules: [{
                required: true,
                message: "请输入积分变动数量（1数为获得，0数为消费）",
                trigger: "blur"
              }]
            },
            {
              label: "变动前积分",
              prop: "beforePoints",
              type: "number",
              sortable: true,
              rules: [{
                required: true,
                message: "请输入变动前积分",
                trigger: "blur"
              }]
            },
            {
              label: "变动后积分",
              prop: "afterPoints",
              type: "number",
              sortable: true,
              rules: [{
                required: true,
                message: "请输入变动后积分",
                trigger: "blur"
              }]
            },
            {
              label: "积分类型（0：签到，1：兑换，2：分享，3：邀请，4：管理员操作）",
              prop: "type",
              rules: [{
                required: true,
                message: "请输入积分类型（0：签到，1：兑换，2：分享，3：邀请，4：管理员操作）",
                trigger: "blur"
              }]
            },
            {
              label: "积分类型名称",
              prop: "typeName",
              rules: [{
                required: true,
                message: "请输入积分类型名称",
                trigger: "blur"
              }]
            },
            {
              label: "关联业务ID",
              prop: "businessId",
              rules: [{
                required: true,
                message: "请输入关联业务ID",
                trigger: "blur"
              }]
            },
            {
              label: "关联业务类型",
              prop: "businessType",
              rules: [{
                required: true,
                message: "请输入关联业务类型",
                trigger: "blur"
              }]
            },
            {
              label: "描述",
              prop: "description",
              rules: [{
                required: true,
                message: "请输入描述",
                trigger: "blur"
              }]
            },
            {
              label: "备注",
              prop: "remark",
              rules: [{
                required: true,
                message: "请输入备注",
                trigger: "blur"
              }]
            },
            {
              label: "操作时间",
              prop: "operateTime",
              type: "datetimerange",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              search: true,
              searchSpan: 12,
              searchRange: true,
              
              sortable: true,
              rules: [{
                required: true,
                message: "请输入操作时间",
                trigger: "blur"
              }]
            },
            {
              label: "操作人",
              prop: "operator",
              rules: [{
                required: true,
                message: "请输入操作人",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.pointsrecord_add, false),
          viewBtn: this.validData(this.permission.pointsrecord_view, false),
          delBtn: this.validData(this.permission.pointsrecord_delete, false),
          editBtn: this.validData(this.permission.pointsrecord_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        // 动态加载用户字典
        getUserDict().then(res => {
          const users = res.data.data || [];
          const userCol = this.option.column.find(c => c.prop === 'createUser');
          if (userCol) userCol.dicData = users;
        }).catch(error => {
          console.error('加载用户字典失败:', error);
        });

        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
