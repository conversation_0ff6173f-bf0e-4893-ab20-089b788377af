<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.points.mapper.PointsGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pointsGoodsResultMap" type="org.springblade.business.points.entity.PointsGoods">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_desc" property="goodsDesc"/>
        <result column="goods_image" property="goodsImage"/>
        <result column="points_price" property="pointsPrice"/>
        <result column="original_price" property="originalPrice"/>
        <result column="stock" property="stock"/>
        <result column="exchange_limit" property="exchangeLimit"/>
        <result column="category" property="category"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectPointsGoodsPage" resultMap="pointsGoodsResultMap">
        select * from urb_points_goods where is_deleted = 0
    </select>
    <select id="getById" resultType="org.springblade.business.points.entity.PointsGoods">
            select * from urb_points_goods where id = #{id} and is_deleted = 0
    </select>

</mapper>
