<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   
                   icon="el-icon-delete"
                   plain
                   v-if="permission.user_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {page, getDetail, add, update, remove} from "@/api/ad/user";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "昵称",
              prop: "nickname",
              rules: [{
                required: true,
                message: "请输入昵称",
                trigger: "blur"
              }]
            },
            {
              label: "手机号",
              prop: "mobile",
              rules: [{
                required: true,
                message: "请输入手机号",
                trigger: "blur"
              }]
            },
            {
              label: "性别",
              prop: "gender",
              type: "select",
              dicData: [
                {
                  label: "男",
                  value: "MALE"
                },
                {
                  label: "女",
                  value: "FEMALE"
                }
              ],
              rules: [{
                required: true,
                message: "请选择性别",
                trigger: "blur"
              }]
            },
            {
              label: "个性签名",
              prop: "signature",
              rules: [{
                required: true,
                message: "请输入个性签名",
                trigger: "blur"
              }]
            },
            {
              label: "发帖数量",
              prop: "postCount",
              addDisplay: false,
              editDisplay: false,
              search: false,
              width: 100,
              formatter: (_, __, cellValue) => {
                return cellValue || 0;
              }
            },
            {
              label: "最后活跃时间",
              prop: "lastActiveTime",
              addDisplay: false,
              editDisplay: false,
              search: false,
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              formatter: (_, __, cellValue) => {
                if (!cellValue || cellValue === 'null') return '-';
                // 如果已经是格式化的字符串，直接返回
                if (typeof cellValue === 'string' && cellValue.includes('-')) {
                  return cellValue;
                }
                // 否则尝试格式化
                try {
                  return new Date(cellValue).toLocaleString('zh-CN');
                } catch (e) {
                  return cellValue || '-';
                }
              }
            },
            {
              label: "签到天数",
              prop: "signInDays",
              addDisplay: false,
              editDisplay: false,
              search: false,
              width: 100,
              formatter: (_, __, cellValue) => {
                return cellValue || 0;
              }
            },
            {
              label: "积分数量",
              prop: "points",
              addDisplay: false,
              editDisplay: false,
              search: false,
              width: 100,
              formatter: (_, __, cellValue) => {
                return cellValue || 0;
              }
            },
            {
              label: "头像",
              prop: "avatar",
              addDisplay: false,
              editDisplay: false,
              search: false,
              width: 80,
              type: "upload",
              listType: "picture-img",
              span: 24,
              tip: "只能上传jpg/png文件，且不超过2MB"
            },
            {
              label: "邮箱",
              prop: "email",
              search: false,
              rules: [{
                type: "email",
                message: "请输入正确的邮箱格式",
                trigger: "blur"
              }]
            },
            {
              label: "地区",
              prop: "region",
              search: false
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.user_add, false),
          viewBtn: this.validData(this.permission.user_view, false),
          delBtn: this.validData(this.permission.user_delete, false),
          editBtn: this.validData(this.permission.user_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        page(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
