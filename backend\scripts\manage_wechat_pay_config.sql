-- 微信支付配置管理脚本
-- 提供配置的增删改查操作

-- ========================================
-- 1. 查询现有配置
-- ========================================

-- 查看所有配置概览
SELECT
    id,
    config_name,
    app_id,
    mch_id,
    CONCAT(LEFT(api_key, 4), '****', RIGHT(api_key, 4)) as api_key_masked,
    CONCAT(LEFT(api_v3_key, 4), '****', RIGHT(api_v3_key, 4)) as api_v3_key_masked,
    cert_serial_no,
    is_sandbox,
    is_enabled,
    is_default,
    priority,
    create_time
FROM urb_wechat_pay_config
ORDER BY priority DESC, create_time DESC;

-- ========================================
-- 2. 更新配置操作
-- ========================================

-- 更新API密钥（替换为实际的32位密钥）
-- UPDATE urb_wechat_pay_config 
-- SET api_key = 'your_actual_32_character_api_key_here',
--     update_time = NOW(),
--     update_user = 1
-- WHERE config_name = '默认商户配置';

-- 更新回调地址
-- UPDATE urb_wechat_pay_config 
-- SET notify_url = 'https://your-actual-domain.com/blade-chat/pay/notify',
--     update_time = NOW(),
--     update_user = 1
-- WHERE config_name = '默认商户配置';

-- 启用/禁用配置
-- UPDATE urb_wechat_pay_config 
-- SET is_enabled = 1,  -- 1启用，0禁用
--     update_time = NOW(),
--     update_user = 1
-- WHERE config_name = '默认商户配置';

-- 设置默认配置（会自动清除其他配置的默认标记）
-- UPDATE urb_wechat_pay_config 
-- SET is_default = CASE WHEN config_name = '默认商户配置' THEN 1 ELSE 0 END,
--     update_time = NOW(),
--     update_user = 1;

-- 更新优先级
-- UPDATE urb_wechat_pay_config 
-- SET priority = 200,
--     update_time = NOW(),
--     update_user = 1
-- WHERE config_name = '默认商户配置';

-- ========================================
-- 3. 添加新配置
-- ========================================

-- 添加新的商户配置示例
-- INSERT INTO `urb_wechat_pay_config` (
--   `config_name`,
--   `app_id`,
--   `mch_id`,
--   `api_key`,
--   `api_v3_key`,
--   `cert_path`,
--   `cert_serial_no`,
--   `private_key_path`,
--   `notify_url`,
--   `is_sandbox`,
--   `is_enabled`,
--   `is_default`,
--   `priority`,
--   `remark`,
--   `create_time`,
--   `create_user`,
--   `update_time`,
--   `update_user`,
--   `tenant_id`
-- ) VALUES (
--   '备用商户配置',
--   'wx5f0591468a438c48',
--   'backup_merchant_id',
--   'backup_api_key_32_characters_here',
--   'backup_api_v3_key_32_characters_here',
--   'classpath:cert/wechat/backup_apiclient_cert.pem',
--   'BACKUP_CERT_SERIAL_NUMBER_40_CHARS_HERE',
--   'classpath:cert/wechat/backup_apiclient_key.pem',
--   'https://wechat.langchuanxinxi.cn/blade-chat/pay/notify',
--   0,  -- 生产环境
--   0,  -- 默认不启用
--   0,  -- 非默认配置
--   80, -- 较低优先级
--   '备用商户配置 - 主商户故障时使用',
--   NOW(),
--   1,
--   NOW(),
--   1,
--   '000000'
-- );

-- ========================================
-- 4. 删除配置（软删除）
-- ========================================

-- 软删除配置（推荐）
-- UPDATE urb_wechat_pay_config 
-- SET is_deleted = 1,
--     update_time = NOW(),
--     update_user = 1
-- WHERE config_name = '要删除的配置名称';

-- 物理删除配置（谨慎使用）
-- DELETE FROM urb_wechat_pay_config 
-- WHERE config_name = '要删除的配置名称';

-- ========================================
-- 5. 配置验证和诊断
-- ========================================

-- 检查配置完整性
SELECT
    config_name,
    CASE
        WHEN app_id IS NULL OR app_id = '' THEN 'Missing app_id'
        WHEN mch_id IS NULL OR mch_id = '' THEN 'Missing mch_id'
        WHEN api_key IS NULL OR api_key = '' OR api_key = 'YOUR_API_KEY_32_CHARACTERS_HERE' THEN 'Missing or placeholder api_key'
        WHEN api_v3_key IS NULL OR api_v3_key = '' THEN 'Missing api_v3_key'
        WHEN cert_path IS NULL OR cert_path = '' THEN 'Missing cert_path'
        WHEN cert_serial_no IS NULL OR cert_serial_no = '' THEN 'Missing cert_serial_no'
        WHEN private_key_path IS NULL OR private_key_path = '' THEN 'Missing private_key_path'
        WHEN notify_url IS NULL OR notify_url = '' OR notify_url LIKE '%your-domain.com%' THEN 'Missing or placeholder notify_url'
        ELSE 'OK'
    END as config_status,
    is_enabled,
    is_default
FROM urb_wechat_pay_config
WHERE is_deleted = 0
ORDER BY priority DESC;

-- 检查默认配置数量（应该只有一个）
SELECT
    COUNT(*) as default_config_count,
    GROUP_CONCAT(config_name) as default_configs
FROM urb_wechat_pay_config
WHERE is_default = 1 AND is_enabled = 1 AND is_deleted = 0;

-- 检查启用配置数量
SELECT
    COUNT(*) as enabled_config_count,
    GROUP_CONCAT(config_name ORDER BY priority DESC) as enabled_configs
FROM urb_wechat_pay_config
WHERE is_enabled = 1 AND is_deleted = 0;

-- ========================================
-- 6. 常用查询
-- ========================================

-- 获取最佳配置（优先级最高的启用配置）
SELECT
    config_name,
    app_id,
    mch_id,
    priority,
    is_default,
    is_sandbox
FROM urb_wechat_pay_config
WHERE is_enabled = 1 AND is_deleted = 0
ORDER BY 
    CASE WHEN is_default = 1 THEN 1 ELSE 2 END,
    priority DESC, 
    create_time DESC
LIMIT 1;

-- 获取指定商户号的配置
-- SELECT * FROM urb_wechat_pay_config 
-- WHERE mch_id = '1722243412' 
-- AND is_enabled = 1 
-- AND is_deleted = 0;

-- 获取指定配置名称的配置
-- SELECT * FROM urb_wechat_pay_config 
-- WHERE config_name = '默认商户配置' 
-- AND is_deleted = 0;

-- ========================================
-- 7. 配置报告
-- ========================================

SELECT '=== 微信支付配置管理报告 ===' as report_title, NOW() as report_time;

SELECT
    CONCAT('总配置数: ', COUNT(*)) as summary
FROM urb_wechat_pay_config WHERE is_deleted = 0
UNION ALL
SELECT
    CONCAT('启用配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_enabled = 1 AND is_deleted = 0
UNION ALL
SELECT
    CONCAT('默认配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_default = 1 AND is_enabled = 1 AND is_deleted = 0
UNION ALL
SELECT
    CONCAT('沙箱配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_sandbox = 1 AND is_deleted = 0
UNION ALL
SELECT
    CONCAT('生产配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_sandbox = 0 AND is_deleted = 0;
