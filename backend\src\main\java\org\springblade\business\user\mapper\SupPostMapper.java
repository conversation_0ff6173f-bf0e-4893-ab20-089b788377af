/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Collection;

import org.apache.ibatis.annotations.Param;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.business.post.entity.AuditPost;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.vo.SupPostVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 百事通信息贴 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface SupPostMapper extends BaseMapper<SupPost> {

	/**
	 * 自定义分页
	 */
	List<SupPostVO> selectPostPage(IPage page, SupPostVO post);

	/**
	 * 查询我的帖子
	 */
	IPage<SupPostVO> selectMyPosts(IPage<SupPostVO> page, @Param("model") Map<String, Long> model);

	/**
	 * 查询用户浏览记录
	 */
	IPage<SupPostVO> selectViewHistory(IPage<SupPostVO> page, @Param("model") Map<String, Object> model);

	/**
	 * 清空用户浏览记录
	 */
	int clearViewHistory(@Param("model") Map<String, Object> model);

	/**
	 * 添加浏览记录
	 */
	int insertViewHistory(@Param("model") Map<String, Object> model);

	/**
	 * 查询拨号记录
	 */
	IPage<SupPostVO> selectCallHistory(IPage<SupPostVO> page, @Param("model") Map<String, Object> model);

	/**
	 * 清空拨号记录
	 */
	int clearCallHistory(@Param("model") Map<String, Object> model);

	/**
	 * 添加拨号记录
	 */
	int insertCallHistory(@Param("model") Map<String, Object> model);

	/**
	 * 查询草稿列表
	 */
	IPage<SupPostVO> selectDraftList(IPage<SupPostVO> page, @Param("model") Map<String, Object> model);

	/**
	 * 查询草稿详情
	 */
	SupPostVO selectDraftDetail(@Param("model") Map<String, Object> model);

	/**
	 * 删除草稿
	 */
	int replyDraft(@Param("model") Map<String, Object> model);

	/**
	 * 获取帖子详情
	 */
	SupPostVO selectPostDetail(@Param("model") Map<String, Object> model);

	/**
	 * 检查用户是否点赞过帖子
	 */
	int checkUserLiked(@Param("model") Map<String, Object> model);

	/**
	 * 检查用户是否收藏过帖子
	 */
	int checkUserFavorited(@Param("model") Map<String, Object> model);

	/**
	 * 保存帖子分类
	 */
	void savePostCategory(@Param("model") Map<String, Object> model);

	/**
	 * 查询帖子列表
	 */
	IPage<SupPostVO> selectPostList(IPage<SupPostVO> page, @Param("model") Map<String, Object> model);

	/**
	 * 添加点赞记录
	 */
	int insertLike(@Param("model") Map<String, Object> model);

	/**
	 * 取消点赞
	 */
	int deleteLike(@Param("model") Map<String, Object> model);

	/**
	 * 添加收藏记录
	 */
	int insertFavorite(@Param("model") Map<String, Object> model);

	/**
	 * 取消收藏
	 */
	int deleteFavorite(@Param("model") Map<String, Object> model);

	/**
	 * 清空用户所有收藏
	 */
	int clearFavorites(@Param("model") Map<String, Object> model);

	/**
	 * 获取用户收藏的标签列表
	 */
	List<String> selectFavoriteTags(@Param("userId") Long userId);

	IPage<SupPostVO> selectViewHistoryList(IPage page, @Param("model") Map<String, Object> model);

	/**
	 * 删除单条浏览记录
	 */
	int deleteViewHistory(Map<String, Object> params);

	/**
	 * 根据分类id条件分页查询帖子,并且置顶帖子第一个,其它的默认按创建时间排序
	 */
	IPage<SupPost> getByCategoryId(IPage<?> page, Long postCategoryId);

	//统计帖子浏览信息
	Long countView(String location);

	Long countShare(String location);

	int insertShareHistory(@Param("model")Map<String, Long> postId);

	void addAuditRecord(@Param("model")AuditPost audit);


	void insertContactAccess(@Param("model")Map<String,Long> model);

	Integer getUserContactAccessCount(@Param("model")Map<String,Object> model);

	List<SupPostVO> selectPostsByInstitutionId(@Param("model") Map<String, Object> params);

	Integer getPostCountByInstitutionId(Long id);

	/**
	 * 批量查询机构帖子数量
	 */
	List<Map<String, Object>> getPostCountsByInstitutionIds(@Param("institutionIds") Collection<Long> institutionIds);

	Long getPostCountByUserIdAndDate(Long userId, LocalDate start, LocalDate end);

    Integer getPostCountByCategoryId(Long id);
}
