<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.inviterecord_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
        <el-button type="warning"
                   icon="el-icon-money"
                   plain
                   @click="handleBatchReward">批量发放奖励
        </el-button>
      </template>

      <!-- 邀请人ID插槽 -->
      <template #inviterUserId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.inviterUserId)"
          class="user-link">
          {{ row.inviterUserId }}
        </el-button>
      </template>

      <!-- 被邀请人ID插槽 -->
      <template #inviteeUserId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.inviteeUserId)"
          class="user-link">
          {{ row.inviteeUserId }}
        </el-button>
      </template>

      <!-- 邀请类型插槽 -->
      <template #inviteType="{ row }">
        <el-tag :type="getInviteTypeColor(row.inviteType)">
          {{ getInviteTypeText(row.inviteType) }}
        </el-tag>
      </template>

      <!-- 注册状态插槽 -->
      <template #registerStatus="{ row }">
        <el-tag :type="getRegisterStatusColor(row.registerStatus)">
          {{ getRegisterStatusText(row.registerStatus) }}
        </el-tag>
      </template>

      <!-- 奖励状态插槽 -->
      <template #rewardStatus="{ row }">
        <el-tag :type="getRewardStatusColor(row.rewardStatus)">
          {{ getRewardStatusText(row.rewardStatus) }}
        </el-tag>
      </template>

      <!-- 奖励金额插槽 -->
      <template #rewardAmount="{ row }">
        <span v-if="row.rewardAmount" class="reward-text">
          {{ formatRewardAmount(row) }}
        </span>
        <span v-else style="color: #999;">-</span>
      </template>

      <!-- 操作菜单插槽 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewInviteDetail(row)">
          详情
        </el-button>
        <el-button
          v-if="row.registerStatus === 1 && row.rewardStatus === 0"
          type="warning"
          size="small"
          @click="handleSendReward(row)">
          发放奖励
        </el-button>
      </template>
    </avue-crud>

    <!-- 邀请详情对话框 -->
    <el-dialog v-model="inviteDetailVisible" title="邀请记录详情" width="800px">
      <div v-if="selectedInvite">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="邀请人">{{ selectedInvite.inviterUserId }}</el-descriptions-item>
          <el-descriptions-item label="被邀请人">{{ selectedInvite.inviteeUserId }}</el-descriptions-item>
          <el-descriptions-item label="邀请码">{{ selectedInvite.inviteCode }}</el-descriptions-item>
          <el-descriptions-item label="邀请类型">
            <el-tag :type="getInviteTypeColor(selectedInvite.inviteType)">
              {{ getInviteTypeText(selectedInvite.inviteType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邀请来源">{{ selectedInvite.inviteSource }}</el-descriptions-item>
          <el-descriptions-item label="注册状态">
            <el-tag :type="getRegisterStatusColor(selectedInvite.registerStatus)">
              {{ getRegisterStatusText(selectedInvite.registerStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ selectedInvite.registerTime || '未注册' }}</el-descriptions-item>
          <el-descriptions-item label="奖励状态">
            <el-tag :type="getRewardStatusColor(selectedInvite.rewardStatus)">
              {{ getRewardStatusText(selectedInvite.rewardStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="奖励类型">{{ selectedInvite.rewardType || '无' }}</el-descriptions-item>
          <el-descriptions-item label="奖励金额">{{ formatRewardAmount(selectedInvite) || '无' }}</el-descriptions-item>
          <el-descriptions-item label="奖励发放时间">{{ selectedInvite.rewardTime || '未发放' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedInvite.createTime }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedInvite.remark">
          <el-divider />
          <h4>备注：</h4>
          <p>{{ selectedInvite.remark }}</p>
        </div>

        <div v-if="selectedInvite.extInfo">
          <el-divider />
          <h4>扩展信息：</h4>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ formatExtInfo(selectedInvite.extInfo) }}</pre>
        </div>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/inviterecord";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        inviteDetailVisible: false,
        selectedInvite: null,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "邀请人",
              prop: "inviterUserId",
              width: 120,
              search: true,
              slot: true,
              rules: [{ required: true, message: "请输入邀请人ID", trigger: "blur" }]
            },
            {
              label: "被邀请人",
              prop: "inviteeUserId",
              width: 120,
              search: true,
              slot: true,
              rules: [{ required: true, message: "请输入被邀请人ID", trigger: "blur" }]
            },
            {
              label: "邀请码",
              prop: "inviteCode",
              width: 150,
              search: true,
              rules: [{ required: true, message: "请输入邀请码", trigger: "blur" }]
            },
            {
              label: "邀请类型",
              prop: "inviteType",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: "二维码邀请", value: "qrcode" },
                { label: "链接邀请", value: "link" },
                { label: "分享邀请", value: "share" }
              ],
              rules: [{ required: true, message: "请选择邀请类型", trigger: "blur" }]
            },
            {
              label: "邀请来源",
              prop: "inviteSource",
              type: "select",
              width: 120,
              search: true,
              dicData: [
                { label: "小程序", value: "miniapp" },
                { label: "H5页面", value: "h5" },
                { label: "APP", value: "app" }
              ],
              rules: [{ required: true, message: "请选择邀请来源", trigger: "blur" }]
            },
            {
              label: "注册状态",
              prop: "registerStatus",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: "未注册", value: 0 },
                { label: "已注册", value: 1 },
                { label: "注册失败", value: 2 }
              ],
              rules: [{ required: true, message: "请选择注册状态", trigger: "blur" }]
            },
            {
              label: "奖励状态",
              prop: "rewardStatus",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: "未发放", value: 0 },
                { label: "已发放", value: 1 },
                { label: "发放失败", value: 2 }
              ],
              rules: [{ required: true, message: "请选择奖励状态", trigger: "blur" }]
            },
            {
              label: "奖励类型",
              prop: "rewardType",
              type: "select",
              width: 120,
              search: true,
              dicData: [
                { label: "积分", value: "points" },
                { label: "优惠券", value: "coupon" },
                { label: "现金", value: "cash" }
              ],
              rules: [{ required: true, message: "请选择奖励类型", trigger: "blur" }]
            },
            {
              label: "奖励金额",
              prop: "rewardAmount",
              type: "number",
              width: 120,
              slot: true,
              rules: [{ required: true, message: "请输入奖励金额/数量", trigger: "blur" }]
            },
            {
              label: "注册时间",
              prop: "registerTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "奖励发放时间",
              prop: "rewardTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              hide: true
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              hide: true
            },
            {
              label: "扩展信息",
              prop: "extInfo",
              type: "textarea",
              hide: true
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.inviterecord_add, false),
          viewBtn: this.validData(this.permission.inviterecord_view, false),
          delBtn: this.validData(this.permission.inviterecord_delete, false),
          editBtn: this.validData(this.permission.inviterecord_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 排序处理
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      // 邀请类型相关方法
      getInviteTypeColor(type) {
        const typeMap = {
          'qrcode': 'primary',
          'link': 'success',
          'share': 'warning'
        };
        return typeMap[type] || 'info';
      },

      getInviteTypeText(type) {
        const typeMap = {
          'qrcode': '二维码邀请',
          'link': '链接邀请',
          'share': '分享邀请'
        };
        return typeMap[type] || '未知';
      },

      // 注册状态相关方法
      getRegisterStatusColor(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger'
        };
        return statusMap[status] || 'info';
      },

      getRegisterStatusText(status) {
        const statusMap = {
          0: '未注册',
          1: '已注册',
          2: '注册失败'
        };
        return statusMap[status] || '未知';
      },

      // 奖励状态相关方法
      getRewardStatusColor(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger'
        };
        return statusMap[status] || 'info';
      },

      getRewardStatusText(status) {
        const statusMap = {
          0: '未发放',
          1: '已发放',
          2: '发放失败'
        };
        return statusMap[status] || '未知';
      },

      // 格式化奖励金额
      formatRewardAmount(row) {
        if (!row.rewardAmount) return '';
        const typeMap = {
          'points': '积分',
          'coupon': '张',
          'cash': '元'
        };
        const unit = typeMap[row.rewardType] || '';
        return `${row.rewardAmount}${unit}`;
      },

      // 格式化扩展信息
      formatExtInfo(extInfo) {
        try {
          return JSON.stringify(JSON.parse(extInfo), null, 2);
        } catch (e) {
          return extInfo;
        }
      },

      // 查看邀请详情
      viewInviteDetail(row) {
        this.selectedInvite = row;
        this.inviteDetailVisible = true;
      },

      // 显示用户详情
      showUserDetail(userId) {
        if (!userId) {
          this.$message.warning('用户信息不存在');
          return;
        }
        this.$message.info(`查看用户详情: ${userId}`);
        // 这里可以跳转到用户详情页面
      },

      // 发放奖励
      handleSendReward(row) {
        this.$confirm('确定要为此邀请记录发放奖励吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用发放奖励的API
          this.$message.success('奖励发放成功');
          this.onLoad(this.page);
        });
      },

      // 批量发放奖励
      handleBatchReward() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        const validRecords = this.selectionList.filter(item =>
          item.registerStatus === 1 && item.rewardStatus === 0
        );
        if (validRecords.length === 0) {
          this.$message.warning("选中的记录中没有可发放奖励的数据");
          return;
        }
        this.$confirm(`确定要为选中的${validRecords.length}条记录批量发放奖励吗？`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用批量发放奖励的API
          this.$message.success('批量发放奖励成功');
          this.onLoad(this.page);
          this.$refs.crud.toggleSelection();
        });
      },

      // 刷新数据
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },

      // 导出数据
      handleExport() {
        this.$message.info('导出功能开发中...');
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error('加载数据失败');
        });
      }
    }
  };
</script>

<style scoped>
/* 用户链接样式 */
.user-link {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
}

.user-link:hover {
  color: #66b1ff;
}

/* 奖励金额样式 */
.reward-text {
  color: #f56c6c;
  font-weight: bold;
}

/* 状态标签样式 */
.el-tag {
  font-size: 12px;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 搜索区域样式优化 */
:deep(.avue-crud__search) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
}

/* 扩展信息样式 */
pre {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}
</style>
