/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.points.entity.PointsRecord;
import org.springblade.business.points.vo.PointsRecordVO;
import org.springblade.business.points.wrapper.PointsRecordWrapper;
import org.springblade.business.points.service.IPointsRecordService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 积分记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/pointsrecord")
@io.swagger.v3.oas.annotations.tags.Tag(name = "积分记录表", description = "积分记录表接口")
public class PointsRecordController extends BladeController {

	private IPointsRecordService pointsRecordService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入pointsRecord")
	public R<PointsRecordVO> detail(PointsRecord pointsRecord) {
		PointsRecord detail = pointsRecordService.getOne(Condition.getQueryWrapper(pointsRecord));
		return R.data(PointsRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 积分记录表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入pointsRecord")
	public R<IPage<PointsRecordVO>> list(PointsRecord pointsRecord, Query query) {
		IPage<PointsRecord> pages = pointsRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(pointsRecord));
		return R.data(PointsRecordWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 积分记录表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入pointsRecord")
	public R<IPage<PointsRecordVO>> page(PointsRecordVO pointsRecord, Query query) {
		IPage<PointsRecordVO> pages = pointsRecordService.selectPointsRecordPage(Condition.getPage(query), pointsRecord);
		return R.data(pages);
	}

	/**
	 * 新增 积分记录表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入pointsRecord")
	public R save(@Valid @RequestBody PointsRecord pointsRecord) {
		return R.status(pointsRecordService.save(pointsRecord));
	}

	/**
	 * 修改 积分记录表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入pointsRecord")
	public R update(@Valid @RequestBody PointsRecord pointsRecord) {
		return R.status(pointsRecordService.updateById(pointsRecord));
	}

	/**
	 * 新增或修改 积分记录表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入pointsRecord")
	public R submit(@Valid @RequestBody PointsRecord pointsRecord) {
		return R.status(pointsRecordService.saveOrUpdate(pointsRecord));
	}

	
	/**
	 * 删除 积分记录表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(pointsRecordService.deleteLogic(Func.toLongList(ids)));
	}

	
}
