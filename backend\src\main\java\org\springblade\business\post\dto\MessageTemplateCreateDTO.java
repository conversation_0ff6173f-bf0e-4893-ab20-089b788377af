package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

/**
 * 新增消息模板DTO
 */
@Data
@Schema(description = "新增消息模板DTO")
public class MessageTemplateCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息类型编码", required = true)
    private String typeCode;

    @Schema(description = "消息类型名称", required = true)
    private String typeName;

    @Schema(description = "消息标题模板", required = true)
    private String titleTemplate;

    @Schema(description = "消息内容模板", required = true)
    private String contentTemplate;



}
