/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.report.entity.Report;
import org.springblade.business.report.service.IReportService;
import org.springblade.business.report.vo.ReportVO;
import org.springblade.business.report.wrapper.ReportWrapper;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.service.WeChatReportService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 举报记录 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/report")
@io.swagger.v3.oas.annotations.tags.Tag(name = "举报记录", description = "举报记录接口")
public class ReportController extends BladeController {

	private IReportService reportService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")

	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入report")
	public R<ReportVO> detail(Report report) {
//		Report detail = reportService.getOne(Condition.getQueryWrapper(report));
		Report detail = reportService.getReport(report);
		return R.data(ReportWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 举报记录
	 */
	@GetMapping("/list")

	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入report")
	public R<IPage<ReportVO>> list(Report report, Query query) {
		IPage<Report> pages = reportService.page(Condition.getPage(query), Condition.getQueryWrapper(report));
		return R.data(ReportWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 举报记录
	 */
	@GetMapping("/page")

	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入report")
	public R<IPage<ReportVO>> page(ReportVO report, Query query) {
		IPage<ReportVO> pages = reportService.selectReportPage(Condition.getPage(query), report);
		return R.data(pages);
	}


	/**
	 * 修改 举报记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入report")
	public R update(@Valid @RequestBody Report report) {
		return R.status(reportService.updateById(report));
	}

	/**
	 * 新增或修改 举报记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入report")
	public R submit(@Valid @RequestBody Report report) {
		return R.status(reportService.saveOrUpdate(report));
	}


	/**
	 * 删除 举报记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(reportService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 审核举报记录
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "审核举报记录", description = "审核举报记录")
	public R audit(@RequestBody AuditReportRequest request) {
		try {
			List<Long> reportIds = Func.toLongList(request.getReportIds());
			String auditStatus = request.getAuditStatus();
			String auditRemark = request.getAuditRemark();
			Integer rewardPoints = request.getRewardPoints() != null ? request.getRewardPoints() : 10; // 默认10积分

			// 获取当前审核人
			Long auditUser = AuthUtil.getUserId();
			String auditTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

			for (Long reportId : reportIds) {
				Report report = reportService.getById(reportId);
				if (report != null) {
					report.setAuditStatus(auditStatus);
					report.setAuditRemark(auditRemark);
					report.setAuditTime(auditTime);
					report.setAuditUser(auditUser);

					reportService.updateById(report);

					// 如果审核通过，给举报用户增加积分
					if ("1".equals(auditStatus) && report.getUserId() != null) {
						reportService.addUserPoints(report.getUserId(), rewardPoints, "举报审核通过奖励");
					}
				}
			}

			return R.success("审核操作成功");
		} catch (Exception e) {
			return R.fail("审核操作失败：" + e.getMessage());
		}
	}

	/**
	 * 批量审核举报记录
	 */
	@PostMapping("/batch-audit")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "批量审核举报记录", description = "批量审核举报记录")
	public R batchAudit(@RequestBody AuditReportRequest request) {
		return audit(request); // 复用单个审核逻辑
	}

	/**
	 * 审核请求DTO
	 */
	public static class AuditReportRequest {
		private String reportIds;
		private String auditStatus; // 1-通过 2-拒绝
		private String auditRemark;
		private Integer rewardPoints; // 奖励积分，默认10

		// Getters and Setters
		public String getReportIds() { return reportIds; }
		public void setReportIds(String reportIds) { this.reportIds = reportIds; }

		public String getAuditStatus() { return auditStatus; }
		public void setAuditStatus(String auditStatus) { this.auditStatus = auditStatus; }

		public String getAuditRemark() { return auditRemark; }
		public void setAuditRemark(String auditRemark) { this.auditRemark = auditRemark; }

		public Integer getRewardPoints() { return rewardPoints; }
		public void setRewardPoints(Integer rewardPoints) { this.rewardPoints = rewardPoints; }
	}

}
