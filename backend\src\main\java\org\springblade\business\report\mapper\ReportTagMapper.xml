<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.report.mapper.ReportTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="reportTagResultMap" type="org.springblade.business.report.entity.ReportTag">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="label" property="label"/>
        <result column="description" property="description"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectReportTagPage" resultMap="reportTagResultMap">
        select * from urb_report_tag where is_deleted = 0
    </select>

</mapper>
