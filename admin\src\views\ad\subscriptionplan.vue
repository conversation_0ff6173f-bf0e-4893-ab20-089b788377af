<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.subscriptionplan_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-star-on"
                   plain
                   @click="handleBatchSetPopular">批量设为热门
        </el-button>
        <el-button type="warning"
                   icon="el-icon-star-off"
                   plain
                   @click="handleBatchCancelPopular">取消热门
        </el-button>
      </template>

      <!-- 价格插槽 -->
      <template #price="{ row }">
        <span class="price-text">
          ¥{{ row.price }}
        </span>
      </template>

      <!-- 计划类型插槽 -->
      <template #planType="{ row }">
        <el-tag :type="getPlanTypeColor(row.planType)">
          {{ getPlanTypeText(row.planType) }}
        </el-tag>
      </template>

      <!-- 是否热门插槽 -->
      <template #isPopular="{ row }">
        <el-tag :type="row.isPopular ? 'danger' : 'info'">
          {{ row.isPopular ? '热门' : '普通' }}
        </el-tag>
      </template>

      <!-- 计划图标插槽 -->
      <template #icon="{ row }">
        <el-avatar v-if="row.icon" :src="row.icon" :size="40" shape="square"></el-avatar>
        <span v-else style="color: #999;">无图标</span>
      </template>

      <!-- 操作菜单插槽 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewPlanDetail(row)">
          详情
        </el-button>
        <el-button
          v-if="!row.isPopular"
          type="warning"
          size="small"
          @click="handleSetPopular(row)">
          设为热门
        </el-button>
        <el-button
          v-if="row.isPopular"
          type="info"
          size="small"
          @click="handleCancelPopular(row)">
          取消热门
        </el-button>
      </template>
    </avue-crud>

    <!-- 计划详情对话框 -->
    <el-dialog v-model="planDetailVisible" title="订阅计划详情" width="700px">
      <div v-if="selectedPlan">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="计划名称">{{ selectedPlan.planName }}</el-descriptions-item>
          <el-descriptions-item label="价格">
            <span class="price-text">¥{{ selectedPlan.price }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="计划类型">
            <el-tag :type="getPlanTypeColor(selectedPlan.planType)">
              {{ getPlanTypeText(selectedPlan.planType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="有效期">{{ selectedPlan.duration }}天</el-descriptions-item>
          <el-descriptions-item label="是否热门">
            <el-tag :type="selectedPlan.isPopular ? 'danger' : 'info'">
              {{ selectedPlan.isPopular ? '热门推荐' : '普通计划' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="功能限制">{{ selectedPlan.maxFeatures }}</el-descriptions-item>
          <el-descriptions-item label="排序">{{ selectedPlan.sortOrder }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedPlan.createTime }}</el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <div v-if="selectedPlan.planDescription">
          <h4>计划描述：</h4>
          <p style="white-space: pre-wrap; line-height: 1.6;">{{ selectedPlan.planDescription }}</p>
        </div>

        <div v-if="selectedPlan.icon">
          <h4>计划图标：</h4>
          <el-image
            :src="selectedPlan.icon"
            style="width: 100px; height: 100px;"
            :preview-src-list="[selectedPlan.icon]">
          </el-image>
        </div>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/subscriptionplan";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        planDetailVisible: false,
        selectedPlan: null,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: true,
          editBtn: true,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'sortOrder',
            order: 'ascending'
          },
          column: [
            {
              label: "计划名称",
              prop: "planName",
              width: 150,
              search: true,
              rules: [{
                required: true,
                message: "请输入计划名称",
                trigger: "blur"
              }]
            },
            {
              label: "价格",
              prop: "price",
              type: "number",
              width: 100,
              sortable: true,
              slot: true,
              rules: [{
                required: true,
                message: "请输入价格",
                trigger: "blur"
              }]
            },
            {
              label: "计划类型",
              prop: "planType",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '月付', value: 'MONTHLY' },
                { label: '年付', value: 'YEARLY' },
                { label: '自定义', value: 'CUSTOM' }
              ],
              rules: [{
                required: true,
                message: "请选择计划类型",
                trigger: "blur"
              }]
            },
            {
              label: "有效期",
              prop: "duration",
              type: "number",
              width: 100,
              sortable: true,
              formatter: (row) => `${row.duration}天`,
              rules: [{
                required: true,
                message: "请输入有效期",
                trigger: "blur"
              }]
            },
            {
              label: "是否热门",
              prop: "isPopular",
              type: "switch",
              width: 100,
              search: true,
              slot: true,
              dicData: [
                { label: '是', value: true },
                { label: '否', value: false }
              ],
              rules: [{
                required: true,
                message: "请选择是否热门推荐",
                trigger: "blur"
              }]
            },
            {
              label: "功能限制",
              prop: "maxFeatures",
              type: "number",
              width: 100,
              rules: [{
                required: true,
                message: "请输入功能限制",
                trigger: "blur"
              }]
            },
            {
              label: "排序",
              prop: "sortOrder",
              type: "number",
              width: 80,
              sortable: true,
              rules: [{
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }]
            },
            {
              label: "计划图标",
              prop: "icon",
              type: "upload",
              width: 100,
              slot: true,
              hide: false
            },
            {
              label: "计划描述",
              prop: "planDescription",
              type: "textarea",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入计划描述",
                trigger: "blur"
              }]
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.subscriptionplan_add, false),
          viewBtn: this.validData(this.permission.subscriptionplan_view, false),
          delBtn: this.validData(this.permission.subscriptionplan_delete, false),
          editBtn: this.validData(this.permission.subscriptionplan_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 排序处理
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      // 计划类型相关方法
      getPlanTypeColor(type) {
        const typeMap = {
          'MONTHLY': 'primary',
          'YEARLY': 'success',
          'CUSTOM': 'warning'
        };
        return typeMap[type] || 'info';
      },

      getPlanTypeText(type) {
        const typeMap = {
          'MONTHLY': '月付',
          'YEARLY': '年付',
          'CUSTOM': '自定义'
        };
        return typeMap[type] || '未知';
      },

      // 查看计划详情
      viewPlanDetail(row) {
        this.selectedPlan = row;
        this.planDetailVisible = true;
      },

      // 设为热门
      handleSetPopular(row) {
        this.$confirm('确定要将此计划设为热门推荐吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用设为热门的API
          this.$message.success('设置成功');
          this.onLoad(this.page);
        });
      },

      // 取消热门
      handleCancelPopular(row) {
        this.$confirm('确定要取消此计划的热门推荐吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用取消热门的API
          this.$message.success('取消成功');
          this.onLoad(this.page);
        });
      },

      // 批量设为热门
      handleBatchSetPopular() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm('确定要将选中的计划设为热门推荐吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用批量设为热门的API
          this.$message.success('批量设置成功');
          this.onLoad(this.page);
          this.$refs.crud.toggleSelection();
        });
      },

      // 批量取消热门
      handleBatchCancelPopular() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm('确定要取消选中计划的热门推荐吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用批量取消热门的API
          this.$message.success('批量取消成功');
          this.onLoad(this.page);
          this.$refs.crud.toggleSelection();
        });
      },

      // 刷新数据
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error('加载数据失败');
        });
      }
    }
  };
</script>

<style scoped>
/* 价格显示样式 */
.price-text {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

/* 状态标签样式 */
.el-tag {
  font-size: 12px;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 搜索区域样式优化 */
:deep(.avue-crud__search) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
}

/* 图标样式 */
.el-avatar {
  border: 1px solid #dcdfe6;
}
</style>
