package org.springblade.business.points.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Select;
import org.springblade.business.points.entity.SigninRecord;
import org.springblade.business.points.vo.SigninRecordVO;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 签到记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface SigninRecordMapper extends BaseMapper<SigninRecord> {

    /**
     * 分页查询签到记录
     *
     * @param page 分页参数
     * @param signinRecord 查询条件
     * @return 分页结果
     */
    IPage<SigninRecordVO> selectSigninRecordPage(IPage<SigninRecordVO> page, @Param("signinRecord") SigninRecord signinRecord);

    /**
     * 查询用户某月签到记录
     *
     * @param userId 用户userId
     * @param year 年份
     * @param month 月份
     * @return 签到记录列表
     */
    List<SigninRecord> selectByMonth(@Param("userId") String userId, @Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询用户某天是否已签到
     *
     * @param userId 用户userId
     * @param signinDate 签到日期
     * @return 签到记录
     */
    SigninRecord selectByDate(@Param("userId") String userId, @Param("signinDate") LocalDate signinDate);

    /**
     * 查询用户最后签到记录
     *
     * @param userId 用户userId
     * @return 最后签到记录
     */
    SigninRecord selectLastSignin(@Param("userId") String userId);

    /**
     * 统计用户签到天数
     *
     * @param userId 用户userId
     * @return 签到天数
     */
    Integer countSigninDays(@Param("userId") String userId);

	/**
     * 统计用户本月签到天数
     */
	Integer countMonthSigninDays(@Param("userId") String userId, @Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询用户连续签到天数
     *
     * @param userId 用户userId
     * @return 连续签到天数
     */
    Integer getContinuousDays(@Param("userId") String userId);

	/**
     * 查询连续签到奖励积分
     */
	Integer getContinuousReward(@Param("days") Integer days);

	List<Map<String, Object>> getAllContinuousReward();

    List<Map<String, Object>> selectByDateRange(String startDate, String endDate);

	void insertWinRecord(@Param("winRecord") Map<String, Object> winRecord);

	/**
	 * 分页查询用户签到记录
	 *
	 * @param userId 用户ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 签到记录列表
	 */
	List<SigninRecord> selectUserRecords(@Param("userId") String userId,
	                                    @Param("startDate") String startDate,
	                                    @Param("endDate") String endDate);

	/**
	 * 统计用户签到记录总数
	 *
	 * @param userId 用户ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 记录总数
	 */
	Integer countUserRecords(@Param("userId") String userId,
	                        @Param("startDate") String startDate,
	                        @Param("endDate") String endDate);

	/**
	 * 获取用户签到统计汇总
	 *
	 * @param userId 用户ID
	 * @return 统计汇总
	 */
	Map<String, Object> selectUserSummary(@Param("userId") String userId);
}
