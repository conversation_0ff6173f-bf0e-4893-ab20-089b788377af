/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.config;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置管理器（支持多商户）
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Configuration
public class WechatPayConfigManager {

    /**
     * 创建微信支付服务工厂
     * 支持根据配置动态创建不同商户的支付服务
     */
    @Bean
    public WechatPayServiceFactory wechatPayServiceFactory() {
        log.info("微信支付服务工厂初始化完成，支持多商户配置");
        return new WechatPayServiceFactory();
    }

    /**
     * 微信支付服务工厂类
     */
    public static class WechatPayServiceFactory {

        /**
         * 根据配置创建微信支付服务
         *
         * @param config 微信支付配置
         * @return 微信支付服务
         */
        public WxPayService createWxPayService(org.springblade.business.user.entity.WechatPayConfig config) {
            WxPayConfig payConfig = new WxPayConfig();
            payConfig.setAppId(config.getAppId());
            payConfig.setMchId(config.getMchId());
            payConfig.setMchKey(config.getApiKey());
            payConfig.setApiV3Key(config.getApiV3Key());
            payConfig.setCertSerialNo(config.getCertSerialNo());
            payConfig.setPrivateKeyPath(config.getPrivateKeyPath());
            payConfig.setPrivateCertPath(config.getCertPath());
            payConfig.setNotifyUrl(config.getNotifyUrl());
            payConfig.setUseSandboxEnv(config.getIsSandbox());

            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(payConfig);

            log.info("创建微信支付服务完成，配置名称：{}，商户号：{}", config.getConfigName(), config.getMchId());
            return wxPayService;
        }

        /**
         * 验证配置是否有效
         *
         * @param config 微信支付配置
         * @return 是否有效
         */
        public boolean validateConfig(org.springblade.business.user.entity.WechatPayConfig config) {
            if (config == null) {
                return false;
            }

            // 检查必要字段
            return config.getAppId() != null && !config.getAppId().trim().isEmpty()
                && config.getMchId() != null && !config.getMchId().trim().isEmpty()
                && config.getApiKey() != null && !config.getApiKey().trim().isEmpty()
                && config.getNotifyUrl() != null && !config.getNotifyUrl().trim().isEmpty()
                && config.getIsEnabled() != null && config.getIsEnabled();
        }
    }
}
