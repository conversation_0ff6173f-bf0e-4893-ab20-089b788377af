-- 邀请码表
CREATE TABLE `urb_invite_code` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `inviter_user_id` varchar(64) NOT NULL COMMENT '邀请人用户ID',
  `code_type` varchar(20) DEFAULT 'general' COMMENT '邀请码类型：general-普通，premium-高级，limited-限量',
  `max_uses` int DEFAULT 0 COMMENT '最大使用次数，0表示无限制',
  `used_count` int DEFAULT 0 COMMENT '已使用次数',
  `registered_count` int DEFAULT 0 COMMENT '成功注册人数',
  `reward_points` int DEFAULT 0 COMMENT '邀请成功奖励积分',
  `invite_status` varchar(20) DEFAULT 'active' COMMENT '邀请状态：active-有效，inactive-无效，expired-过期',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间，NULL表示永不过期',
  `qr_image_url` varchar(500) DEFAULT NULL COMMENT '二维码图片URL',
  `qr_file_id` bigint DEFAULT NULL COMMENT '二维码文件ID',
  `miniapp_path` varchar(200) DEFAULT NULL COMMENT '小程序页面路径',
  `description` varchar(200) DEFAULT NULL COMMENT '邀请码描述',
  `ext_info` text COMMENT '扩展信息（JSON格式）',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `is_permanent` tinyint(1) DEFAULT 1 COMMENT '是否长期有效',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status_flag` int DEFAULT 1 COMMENT '状态标识',
  `is_deleted` int DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_inviter_user_id` (`inviter_user_id`),
  KEY `idx_invite_status` (`invite_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邀请码表';

-- 邀请关系表
CREATE TABLE `urb_invite_relation` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `inviter_user_id` varchar(64) NOT NULL COMMENT '邀请人用户ID',
  `invitee_user_id` varchar(64) NOT NULL COMMENT '被邀请人用户ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `invite_code_id` bigint DEFAULT NULL COMMENT '邀请码ID',
  `invite_type` varchar(20) DEFAULT 'qrcode' COMMENT '邀请类型：qrcode-二维码邀请，link-链接邀请，share-分享邀请',
  `invite_source` varchar(20) DEFAULT 'miniapp' COMMENT '邀请来源：miniapp-小程序，h5-H5页面，app-APP',
  `relation_status` varchar(20) DEFAULT 'pending' COMMENT '关系状态：pending-待确认，active-有效，inactive-无效',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `first_interaction_time` datetime DEFAULT NULL COMMENT '首次互动时间',
  `confirm_time` datetime DEFAULT NULL COMMENT '关系确认时间',
  `reward_status` int DEFAULT 0 COMMENT '奖励状态：0-未发放，1-已发放，2-发放失败',
  `reward_type` varchar(20) DEFAULT 'points' COMMENT '奖励类型：points-积分，coupon-优惠券，cash-现金',
  `reward_amount` int DEFAULT 0 COMMENT '奖励金额/数量',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `channel_details` varchar(200) DEFAULT NULL COMMENT '邀请渠道详情',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `location_info` varchar(200) DEFAULT NULL COMMENT '地理位置信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `ext_info` text COMMENT '扩展信息（JSON格式）',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status_flag` int DEFAULT 1 COMMENT '状态标识',
  `is_deleted` int DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inviter_invitee` (`inviter_user_id`, `invitee_user_id`),
  KEY `idx_inviter_user_id` (`inviter_user_id`),
  KEY `idx_invitee_user_id` (`invitee_user_id`),
  KEY `idx_invite_code` (`invite_code`),
  KEY `idx_invite_code_id` (`invite_code_id`),
  KEY `idx_register_time` (`register_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_invite_relation_code_id` FOREIGN KEY (`invite_code_id`) REFERENCES `urb_invite_code` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邀请关系表';

-- 为现有的邀请记录表添加索引优化
ALTER TABLE `urb_invite_record`
ADD INDEX `idx_inviter_user_id` (`inviter_user_id`),
ADD INDEX `idx_invitee_user_id` (`invitee_user_id`),
ADD INDEX `idx_invite_code` (`invite_code`),
ADD INDEX `idx_register_time` (`register_time`),
ADD INDEX `idx_invite_type` (`invite_type`),
ADD INDEX `idx_invite_source` (`invite_source`);

-- 插入示例数据（可选）
INSERT INTO `urb_invite_code` (
  `id`, `invite_code`, `inviter_user_id`, `code_type`, `max_uses`, `used_count`,
  `registered_count`, `reward_points`, `invite_status`, `expire_time`, `is_permanent`,
  `description`, `create_time`
) VALUES
(1, 'DEMO001', 'user_001', 'general', 0, 0, 0, 100, 'active', NULL, 1, '演示邀请码', NOW()),
(2, 'DEMO002', 'user_002', 'premium', 100, 5, 3, 200, 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), 0, '高级邀请码', NOW()),
(3, 'DEMO003', 'user_003', 'limited', 10, 8, 5, 150, 'active', NULL, 1, '限量邀请码', NOW());

-- 创建视图：邀请统计视图
CREATE VIEW `v_invite_stats` AS
SELECT
  ic.inviter_user_id,
  COUNT(ic.id) as total_codes,
  SUM(ic.used_count) as total_uses,
  SUM(ic.registered_count) as total_registered,
  AVG(CASE WHEN ic.used_count > 0 THEN ic.registered_count * 100.0 / ic.used_count ELSE 0 END) as avg_conversion_rate,
  SUM(CASE WHEN ic.invite_status = 'active' THEN 1 ELSE 0 END) as active_codes,
  MAX(ic.create_time) as last_code_time
FROM urb_invite_code ic
WHERE ic.is_deleted = 0
GROUP BY ic.inviter_user_id;

-- 创建视图：邀请关系统计视图
CREATE VIEW `v_invite_relation_stats` AS
SELECT
  ir.inviter_user_id,
  COUNT(ir.id) as total_invites,
  COUNT(CASE WHEN ir.relation_status = 'active' THEN 1 END) as active_invites,
  COUNT(CASE WHEN ir.register_time IS NOT NULL THEN 1 END) as registered_invites,
  COUNT(CASE WHEN ir.reward_status = 1 THEN 1 END) as rewarded_invites,
  SUM(CASE WHEN ir.reward_status = 1 THEN ir.reward_amount ELSE 0 END) as total_reward_amount,
  MIN(ir.create_time) as first_invite_time,
  MAX(ir.create_time) as last_invite_time
FROM urb_invite_relation ir
WHERE ir.is_deleted = 0
GROUP BY ir.inviter_user_id;

-- 创建存储过程：清理过期邀请码
DELIMITER //
CREATE PROCEDURE `sp_cleanup_expired_invite_codes`()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE code_id BIGINT;
  DECLARE cur CURSOR FOR
    SELECT id FROM urb_invite_code
    WHERE invite_status = 'active'
    AND expire_time IS NOT NULL
    AND expire_time <= NOW()
    AND is_deleted = 0;
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

  START TRANSACTION;

  OPEN cur;
  read_loop: LOOP
    FETCH cur INTO code_id;
    IF done THEN
      LEAVE read_loop;
    END IF;

    UPDATE urb_invite_code
    SET invite_status = 'expired', update_time = NOW()
    WHERE id = code_id;
  END LOOP;
  CLOSE cur;

  COMMIT;

  SELECT ROW_COUNT() as updated_count;
END //
DELIMITER ;

-- 创建定时任务清理过期邀请码（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT `evt_cleanup_expired_codes`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL sp_cleanup_expired_invite_codes();
