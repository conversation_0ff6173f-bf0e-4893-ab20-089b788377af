/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.painpoint.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.painpoint.entity.PainPoint;
import org.springblade.business.painpoint.vo.PainPointVO;
import org.springblade.business.painpoint.wrapper.PainPointWrapper;
import org.springblade.business.painpoint.service.IPainPointService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/painpoint")
@io.swagger.v3.oas.annotations.tags.Tag(name = "痛点反馈", description = "接口")
public class PainPointController extends BladeController {

	private IPainPointService painPointService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")

	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入painPoint")
	public R<PainPointVO> detail(PainPoint painPoint) {
		PainPoint detail = painPointService.getOne(Condition.getQueryWrapper(painPoint));
		return R.data(PainPointWrapper.build().entityVO(detail));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")

	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入painPoint")
	public R<IPage<PainPointVO>> list(PainPoint painPoint, Query query) {
		IPage<PainPoint> pages = painPointService.page(Condition.getPage(query), Condition.getQueryWrapper(painPoint));
		return R.data(PainPointWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页
	 */
	@GetMapping("/page")

	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入painPoint")
	public R<IPage<PainPointVO>> page(PainPointVO painPoint, Query query) {
		IPage<PainPointVO> pages = painPointService.selectPainPointPage(Condition.getPage(query), painPoint);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入painPoint")
	public R save(@Valid @RequestBody PainPoint painPoint) {
		return R.status(painPointService.save(painPoint));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入painPoint")
	public R update(@Valid @RequestBody PainPoint painPoint) {
		return R.status(painPointService.updateById(painPoint));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入painPoint")
	public R submit(@Valid @RequestBody PainPoint painPoint) {
		return R.status(painPointService.saveOrUpdate(painPoint));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(painPointService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 处理反馈
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "处理反馈", description = "传入painPoint")
	public R handle(@Valid @RequestBody PainPoint painPoint) {
		return R.status(painPointService.handle(painPoint));
	}


}
