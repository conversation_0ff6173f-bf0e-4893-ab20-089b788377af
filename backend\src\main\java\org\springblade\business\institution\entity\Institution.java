/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.util.List;

/**
 * 机构主表实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@TableName("urb_institution")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "机构主表")
public class Institution extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    private String name;
    /**
     * 机构Logo URL或Base64
     */
    @Schema(description = "机构Logo URL或Base64")
    private String logo;
    /**
     * 机构分类ID
     */
    @Schema(description = "机构分类ID")
    private Long typeId;
    /**
     * 营业年限
     */
    @Schema(description = "营业年限")
    private Integer yearsInBusiness;
    /**
     * 机构简介
     */
    @Schema(description = "机构简介")
    private String description;
    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactPerson;
    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;
    /**
     * 备用电话
     */
    @Schema(description = "备用电话")
    private String backupPhone;
    /**
     * 电子邮箱
     */
    @Schema(description = "电子邮箱")
    private String email;
    /**
     * 微信
     */
    @Schema(description = "微信")
    private String wechat;
    /**
     * QQ
     */
    @Schema(description = "QQ")
    private String qq;
    /**
     * 营业执照号码
     */
    @Schema(description = "营业执照号码")
    private String licenseNo;
    /**
     * 营业执照照片
     */
    @Schema(description = "营业执照照片")
    private String licenseImage;
    /**
     * 法人代表姓名
     */
    @Schema(description = "法人代表姓名")
    private String legalPerson;
    /**
     * 行业许可证
     */
    @Schema(description = "行业许可证")
    private String industryLicense;
    /**
     * 税务登记证
     */
    @Schema(description = "税务登记证")
    private String taxCertificate;
    /**
     * 省
     */
    @Schema(description = "省")
    private String province;
    /**
     * 市
     */
    @Schema(description = "市")
    private String city;
    /**
     * 区
     */
    @Schema(description = "区")
    private String district;
    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String detailAddress;
    /**
     * 经度
     */
    @Schema(description = "经度")
    private BigDecimal longitude;
    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private BigDecimal latitude;
    /**
     * 是否支持到店(1:是,0:否)
     */
    @Schema(description = "是否支持到店(1:是,0:否)")
    private Boolean isStore;
    /**
     * 服务半径(米)
     */
    @Schema(description = "服务半径(米)")
    private Integer serviceRadius;
    /**
     * 是否支持外卖
     */
    @Schema(description = "是否支持外卖")
    private Boolean hasDelivery;
    /**
     * 支付方式(逗号分隔)
     */
    @Schema(description = "支付方式(逗号分隔)")
    private String paymentMethods;
    /**
     * 特色服务
     */
    @Schema(description = "特色服务")
    private String specialServices;
    /**
     * 登录账号
     */
    @Schema(description = "登录账号")
    private String username;
    /**
     * 密码(加密)
     */
    @Schema(description = "密码(加密)")
    private String password;
    /**
     * 加密盐值
     */
    @Schema(description = "加密盐值")
    private String salt;
    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;
    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String lastLoginIp;

	private String regionCode;

    /**
     * 账号是否锁定
     */
    @Schema(description = "账号是否锁定")
    private Boolean isLocked;
    /**
     * 营业时间(JSON格式: {\"monday\":{\"open\":\"09:00\",\"close\":\"21:00\"},...})
     */
    @Schema(description = "营业时间(JSON格式: {\"monday\":{\"open\":\"09:00\",\"close\":\"21:00\"},...})")
	@TableField(value = "business_hours")
    private String businessHours;
    /**
     * 商家图片(JSON格式: [{\"url\":\"...\",\"type\":1,\"sort\":0},...])
     */
    @Schema(description = "商家图片(JSON格式: [{\"url\":\"...\",\"type\":1,\"sort\":0},...])")
	@TableField(value = "images")
    private String images;
    /**
     * 审核状态（0-待审核，1-通过，2-拒绝）
     */
    @Schema(description = "审核状态（0-待审核，1-通过，2-拒绝）")
    private String auditStatus;
    /**
     * 最后审核备注
     */
    @Schema(description = "最后审核备注")
    private String auditRemark;
    /**
     * 申请人ID
     */
    @Schema(description = "申请人ID")
    private Long applyUserId;

	@Schema(description = "申请状态")
    private String publishStatus;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    private LocalDateTime applyTime;
    /**
     * 最后审核时间
     */
    @Schema(description = "最后审核时间")
    private LocalDateTime lastAuditTime;
    /**
     * 最后审核人ID
     */
    @Schema(description = "最后审核人ID")
    private Long lastAuditUserId;

	/**
	 * 置顶
	 */
	@Schema(description = "置顶")
	private String top;


}
