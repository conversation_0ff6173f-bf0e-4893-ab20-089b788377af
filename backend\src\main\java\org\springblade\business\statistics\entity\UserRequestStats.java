package org.springblade.business.statistics.entity;

import lombok.Data;
import java.util.Date;

@Data
public class UserRequestStats {
    private Long id;             // 主键ID
    private Long userId;       // 用户ID
    private Date lastRequestTime;  // 最后登录时间
    private Integer todayRequestCount; // 今日登录次数
    private Date createTime;     // 创建时间
    private Date updateTime;     // 更新时间
	//同步时间
	private Date syncTime;
}
