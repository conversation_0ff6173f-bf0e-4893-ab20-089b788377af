<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.subscriptionPlan.mapper.UserFeaturePermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userFeaturePermissionResultMap" type="org.springblade.business.subscriptionPlan.entity.UserFeaturePermission">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="feature_id" property="featureId"/>
        <result column="subscription_id" property="subscriptionId"/>
        <result column="is_active" property="isActive"/>
        <result column="expire_time" property="expireTime"/>
        <result column="featureCode" property="featureCode"/>
    </resultMap>


    <select id="selectUserFeaturePermissionPage" resultMap="userFeaturePermissionResultMap">
        select * from urb_user_feature_permission where is_deleted = 0
    </select>
    <select id="selectFeaturesByUser" resultType="org.springblade.business.subscriptionPlan.entity.Feature">
            select * from urb_feature where id in
            (select feature_id from urb_user_feature_permission where user_id = #{userId}  and  is_active = 1)
            and is_deleted = 0
            AND status = 1
            order by sort_order
    </select>

</mapper>
