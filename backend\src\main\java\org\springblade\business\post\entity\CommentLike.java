/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 评论点赞实体类
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
@TableName("urb_comment_like")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "评论点赞")
public class CommentLike extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 评论ID
	 */
	@Schema(description = "评论ID")
	private Long commentId;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 点赞类型：HELPFUL-有帮助，LIKE-点赞
	 */
	@Schema(description = "点赞类型")
	private String likeType;

}
