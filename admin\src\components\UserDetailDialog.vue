<template>
  <el-dialog 
    v-model="visible" 
    title="用户详情" 
    width="800px"
    :before-close="handleClose"
    destroy-on-close>
    <div v-loading="loading">
      <div v-if="userDetail">
        <!-- 用户基本信息 -->
        <el-card class="mb-3">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="user-avatar">
                <el-avatar 
                  :size="80" 
                  :src="userDetail.avatar" 
                  :icon="UserFilled"
                  style="border: 2px solid #f0f0f0;">
                </el-avatar>
              </div>
            </el-col>
            <el-col :span="18">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="用户ID">{{ userDetail.id }}</el-descriptions-item>
                <el-descriptions-item label="昵称">{{ userDetail.nickname || '未设置' }}</el-descriptions-item>
                <el-descriptions-item label="真实姓名">{{ userDetail.realName || '未设置' }}</el-descriptions-item>
                <el-descriptions-item label="性别">{{ getGenderText(userDetail.gender) }}</el-descriptions-item>
                <el-descriptions-item label="手机号">{{ userDetail.phone || '未绑定' }}</el-descriptions-item>
                <el-descriptions-item label="邮箱">{{ userDetail.email || '未绑定' }}</el-descriptions-item>
                <el-descriptions-item label="生日">{{ userDetail.birthday || '未设置' }}</el-descriptions-item>
                <el-descriptions-item label="地区">{{ getLocationText(userDetail) }}</el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-card>

        <!-- 账户状态信息 -->
        <el-card class="mb-3">
          <template #header>
            <div class="card-header">
              <span>账户状态</span>
            </div>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="账户状态">
              <el-tag :type="userDetail.status === 1 ? 'success' : 'danger'">
                {{ userDetail.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="实名认证">
              <el-tag :type="userDetail.isRealNameAuth ? 'success' : 'warning'">
                {{ userDetail.isRealNameAuth ? '已认证' : '未认证' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="VIP状态">
              <el-tag :type="userDetail.isVip ? 'success' : 'info'">
                {{ userDetail.isVip ? 'VIP用户' : '普通用户' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ userDetail.createTime }}</el-descriptions-item>
            <el-descriptions-item label="最后登录">{{ userDetail.lastLoginTime || '从未登录' }}</el-descriptions-item>
            <el-descriptions-item label="登录次数">{{ userDetail.loginCount || 0 }}次</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 统计信息 -->
        <el-card class="mb-3" v-if="userDetail.stats">
          <template #header>
            <div class="card-header">
              <span>活动统计</span>
            </div>
          </template>
          <el-descriptions :column="4" border>
            <el-descriptions-item label="发布帖子">{{ userDetail.stats.postCount || 0 }}条</el-descriptions-item>
            <el-descriptions-item label="发布反馈">{{ userDetail.stats.feedbackCount || 0 }}条</el-descriptions-item>
            <el-descriptions-item label="名片数量">{{ userDetail.stats.businessCardCount || 0 }}张</el-descriptions-item>
            <el-descriptions-item label="收藏数量">{{ userDetail.stats.favoriteCount || 0 }}个</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 个人简介 -->
        <el-card v-if="userDetail.bio">
          <template #header>
            <div class="card-header">
              <span>个人简介</span>
            </div>
          </template>
          <p style="white-space: pre-wrap; margin: 0; line-height: 1.6;">{{ userDetail.bio }}</p>
        </el-card>
      </div>

      <!-- 加载失败提示 -->
      <el-empty v-else-if="!loading" description="用户信息加载失败" :image-size="80">
        <template #description>
          <div style="color: #909399; font-size: 14px;">
            <el-icon style="margin-right: 5px;"><Warning /></el-icon>
            无法获取用户详细信息
          </div>
        </template>
        <el-button type="primary" @click="loadUserDetail">重新加载</el-button>
      </el-empty>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleManageUser" v-if="userDetail">
          管理用户
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { UserFilled, Warning } from '@element-plus/icons-vue'
import { getUserDetail } from '@/api/ad/user'

export default {
  name: 'UserDetailDialog',
  components: {
    UserFilled,
    Warning
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    userId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:modelValue', 'manage-user'],
  data() {
    return {
      loading: false,
      userDetail: null
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.userId) {
        this.loadUserDetail()
      }
    },
    userId(newVal) {
      if (newVal && this.visible) {
        this.loadUserDetail()
      }
    }
  },
  methods: {
    async loadUserDetail() {
      if (!this.userId) return
      
      this.loading = true
      this.userDetail = null
      
      try {
        const response = await getUserDetail(this.userId)
        this.userDetail = response.data.data
      } catch (error) {
        console.error('加载用户详情失败:', error)
        this.$message.error('加载用户详情失败')
      } finally {
        this.loading = false
      }
    },
    
    getGenderText(gender) {
      const genderMap = {
        0: '保密',
        1: '男',
        2: '女'
      }
      return genderMap[gender] || '未设置'
    },
    
    getLocationText(user) {
      const parts = []
      if (user.province) parts.push(user.province)
      if (user.city) parts.push(user.city)
      if (user.district) parts.push(user.district)
      return parts.length > 0 ? parts.join(' ') : '未设置'
    },
    
    handleClose() {
      this.visible = false
      this.userDetail = null
    },
    
    handleManageUser() {
      this.$emit('manage-user', this.userDetail)
    }
  }
}
</script>

<style lang="scss" scoped>
.mb-3 {
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  span {
    font-weight: 600;
    color: #303133;
  }
}

.user-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.dialog-footer {
  text-align: right;
}
</style>
