/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.user.entity.Contact;
import org.springblade.business.user.service.IContactService;
import org.springblade.business.user.vo.ContactVO;
import org.springblade.business.user.wrapper.ContactWrapper;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

/**
 * 联系人 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/contact")
@io.swagger.v3.oas.annotations.tags.Tag(name = "联系人", description = "联系人接口")
public class ContactController extends BladeController {

	private IContactService contactService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")

	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入contact")
	public R<ContactVO> detail(Contact contact) {
		Contact detail = contactService.getOne(Condition.getQueryWrapper(contact));
		return R.data(ContactWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 联系人
	 */
	@GetMapping("/list")

	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入contact")
	public R<IPage<ContactVO>> list(Contact contact, Query query) {
		IPage<Contact> pages = contactService.page(Condition.getPage(query), Condition.getQueryWrapper(contact));
		return R.data(ContactWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 联系人
	 */
	@GetMapping("/page")

	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入contact")
	public R<IPage<ContactVO>> page(ContactVO contact, Query query) {
		IPage<ContactVO> pages = contactService.selectContactPage(Condition.getPage(query), contact);
		return R.data(pages);
	}

	/**
	 * 新增 联系人
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入contact")
	public R save(@Valid @RequestBody Contact contact) {
		return R.status(contactService.save(contact));
	}

	/**
	 * 修改 联系人
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入contact")
	public R update(@Valid @RequestBody Contact contact) {
		return R.status(contactService.updateById(contact));
	}

	/**
	 * 新增或修改 联系人
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入contact")
	public R submit(@Valid @RequestBody Contact contact) {
		return R.status(contactService.saveOrUpdate(contact));
	}


	/**
	 * 删除 联系人
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(contactService.deleteLogic(Func.toLongList(ids)));
	}


}
