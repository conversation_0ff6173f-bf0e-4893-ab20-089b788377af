<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.points.mapper.PointsExchangeMapper">

    <resultMap id="pointsExchangeResultMap" type="org.springblade.business.points.entity.PointsExchange">
        <!-- 兑换记录字段 -->
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="open_id" property="openId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_name" property="goodsName"/>
        <result column="quantity" property="quantity"/>
        <result column="points_cost" property="pointsCost"/>
        <result column="exchange_time" property="exchangeTime"/>
        <result column="remark" property="remark"/>
        <!-- 积分商品信息映射 -->
        <association property="pointsGoods" javaType="org.springblade.business.points.entity.PointsGoods">
            <id column="g_id" property="id"/>
            <result column="g_goods_name" property="goodsName"/>
            <result column="g_goods_desc" property="goodsDesc"/>
            <result column="g_goods_image" property="goodsImage"/>
            <result column="g_points_price" property="pointsPrice"/>
            <result column="g_original_price" property="originalPrice"/>
            <result column="g_stock" property="stock"/>
            <result column="g_exchange_limit" property="exchangeLimit"/>
            <result column="g_category" property="category"/>
            <result column="g_sort_order" property="sortOrder"/>
        </association>
    </resultMap>


    <select id="selectPointsExchangePage" resultMap="pointsExchangeResultMap">
        select * from urb_points_exchange where is_deleted = 0
    </select>

    <select id="getPointsPointsExchangeDetail" resultMap="pointsExchangeResultMap">
        SELECT
            e.*,
            g.id AS g_id,
            g.goods_name AS g_goods_name,
            g.goods_desc AS g_goods_desc,
            g.goods_image AS g_goods_image,
            g.points_price AS g_points_price,
            g.original_price AS g_original_price,
            g.stock AS g_stock,
            g.exchange_limit AS g_exchange_limit,
            g.category AS g_category,
            g.sort_order AS g_sort_order
        FROM urb_points_exchange e
                 LEFT JOIN urb_points_goods g ON e.goods_id = g.id
        WHERE e.id = #{id}
          AND e.is_deleted = 0
            LIMIT 1
    </select>

</mapper>
