/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.service.impl.SupPostServiceImpl;
import org.springblade.business.report.entity.Report;
import org.springblade.business.report.mapper.ReportMapper;
import org.springblade.business.report.service.IReportService;
import org.springblade.business.report.vo.ReportVO;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.modules.system.entity.Post;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.impl.PostServiceImpl;
import org.springblade.modules.system.service.impl.UserServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springblade.business.user.service.IUserPointsService;

/**
 * 举报记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class ReportServiceImpl extends BaseServiceImpl<ReportMapper, Report> implements IReportService {

	private final IWeUserService userServiceImpl;
	private final SupPostServiceImpl postServiceImpl;

	@Autowired(required = false)
	private IUserPointsService userPointsService;

	public ReportServiceImpl(IWeUserService userServiceImpl, SupPostServiceImpl postServiceImpl) {
		this.userServiceImpl = userServiceImpl;
		this.postServiceImpl = postServiceImpl;
	}

	@Override
	public IPage<ReportVO> selectReportPage(IPage<ReportVO> page, ReportVO report) {
		return page.setRecords(baseMapper.selectReportPage(page, report));
	}

	@Override
	public Report getReport(Report report) {
		Report detail = this.getOne(Condition.getQueryWrapper(report));
		//根据帖子id找到被举报的用户名字和手机号
		if (detail != null) {
			//举报用户名字和电话
			detail.setReportUserName(userServiceImpl.getById(detail.getUserId()).getNickname());
			detail.setReportUserPhone(userServiceImpl.getById(detail.getUserId()).getMobile());
			SupPost post = postServiceImpl.getById(detail.getPostId());
			if (post != null) {
				WeUser user = userServiceImpl.getById(post.getCreateUser());
				detail.setUserName(user.getNickname());
				detail.setUserPhone(user.getMobile());
				//帖子被举报的次数（审核通过）
				Integer reportCount = this.baseMapper.getReportCount(detail.getPostId());
				detail.setReportCount(reportCount);
			}
		}
		return detail;
	}

	@Override
	public void addUserPoints(Long userId, Integer points, String reason) {
		try {
			if (userPointsService != null && userId != null && points != null && points > 0) {
				// 调用积分服务给用户增加积分
				userPointsService.addPoints(userId, points, reason);
			}
		} catch (Exception e) {
			// 积分服务调用失败不影响主流程，只记录日志
			System.err.println("给用户增加积分失败: userId=" + userId + ", points=" + points + ", reason=" + reason + ", error=" + e.getMessage());
		}
	}

}
