<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.invite.mapper.InviteCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inviteCodeResultMap" type="org.springblade.miniapp.entity.InviteCode">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="inviter_user_id" property="inviterUserId"/>
        <result column="code_type" property="codeType"/>
        <result column="max_uses" property="maxUses"/>
        <result column="used_count" property="usedCount"/>
        <result column="registered_count" property="registeredCount"/>
        <result column="reward_points" property="rewardPoints"/>
        <result column="invite_status" property="inviteStatus"/>
        <result column="expire_time" property="expireTime"/>
        <result column="qr_image_url" property="qrImageUrl"/>
        <result column="qr_file_id" property="qrFileId"/>
        <result column="miniapp_path" property="miniappPath"/>
        <result column="description" property="description"/>
        <result column="ext_info" property="extInfo"/>
        <result column="last_used_time" property="lastUsedTime"/>
        <result column="is_permanent" property="isPermanent"/>
        <result column="status_flag" property="statusFlag"/>
    </resultMap>


    <select id="selectInviteCodePage" resultMap="inviteCodeResultMap">
        select * from urb_invite_code where is_deleted = 0
    </select>

</mapper>
