/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 顺风车信息表实体类
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@TableName("urb_post_carpool")
@Schema(description = "顺风车信息表")
public class PostCarpool implements Serializable {

    @Serial
	private static final long serialVersionUID = 1L;

	@Schema(description = "ID")
	@TableId
	private Long id;

    /**
     * 所属区域
     */
    @Schema(description = "所属区域")
    private String region;
    /**
     * 拼车类型（如顺风车、拼货车等）
     */
    @Schema(description = "拼车类型（如顺风车、拼货车等）")
    private String carpoolType;
    /**
     * 出发地
     */
    @Schema(description = "出发地")
    private String departure;
    /**
     * 目的地
     */
    @Schema(description = "目的地")
    private String destination;
    /**
     * 途径地（可存多个，用逗号等分隔或 JSON 格式，根据实际解析需求定）
     */
    @Schema(description = "途径地（可存多个，用逗号等分隔或 JSON 格式，根据实际解析需求定）")
    private String via;
    /**
     * 出发时间
     */
    @Schema(description = "出发时间")
    private LocalDateTime departureTime;
    /**
     * 空位（人数场景）
     */
    @Schema(description = "空位（人数场景）")
    private Long emptySeats;
    /**
     * 数量(吨)，货车场景
     */
    @Schema(description = "数量(吨)，货车场景")
    private BigDecimal tonnage;
    /**
     * 关联帖子表ID
     */
    @Schema(description = "关联帖子表ID")
    private Long postId;


}
