<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.inviterelation_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
        <el-button type="warning"
                   icon="el-icon-share"
                   plain
                   @click="handleViewRelationTree">关系树
        </el-button>
      </template>

      <!-- 邀请人插槽 -->
      <template #inviterUserId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.inviterUserId)"
          class="user-link">
          {{ row.inviterUserId }}
        </el-button>
      </template>

      <!-- 被邀请人插槽 -->
      <template #inviteeUserId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.inviteeUserId)"
          class="user-link">
          {{ row.inviteeUserId }}
        </el-button>
      </template>

      <!-- 邀请类型插槽 -->
      <template #inviteType="{ row }">
        <el-tag :type="getInviteTypeColor(row.inviteType)">
          {{ getInviteTypeText(row.inviteType) }}
        </el-tag>
      </template>

      <!-- 关系层级插槽 -->
      <template #relationLevel="{ row }">
        <el-tag :type="getLevelColor(row.relationLevel)">
          第{{ row.relationLevel }}级
        </el-tag>
      </template>

      <!-- 操作菜单插槽 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewRelationDetail(row)">
          详情
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="viewDownlineTree(row)">
          下级关系
        </el-button>
      </template>
    </avue-crud>

    <!-- 关系详情对话框 -->
    <el-dialog v-model="relationDetailVisible" title="邀请关系详情" width="700px">
      <div v-if="selectedRelation">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="邀请人">{{ selectedRelation.inviterUserId }}</el-descriptions-item>
          <el-descriptions-item label="被邀请人">{{ selectedRelation.inviteeUserId }}</el-descriptions-item>
          <el-descriptions-item label="邀请码">{{ selectedRelation.inviteCode }}</el-descriptions-item>
          <el-descriptions-item label="邀请类型">
            <el-tag :type="getInviteTypeColor(selectedRelation.inviteType)">
              {{ getInviteTypeText(selectedRelation.inviteType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="关系层级">
            <el-tag :type="getLevelColor(selectedRelation.relationLevel)">
              第{{ selectedRelation.relationLevel }}级
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邀请来源">{{ selectedRelation.inviteSource }}</el-descriptions-item>
          <el-descriptions-item label="建立时间">{{ selectedRelation.createTime }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedRelation.isActive ? 'success' : 'danger'">
              {{ selectedRelation.isActive ? '有效' : '无效' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/inviterelation";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "邀请人用户ID",
              prop: "inviterUserId",
              rules: [{
                required: true,
                message: "请输入邀请人用户ID",
                trigger: "blur"
              }]
            },
            {
              label: "被邀请人用户ID",
              prop: "inviteeUserId",
              rules: [{
                required: true,
                message: "请输入被邀请人用户ID",
                trigger: "blur"
              }]
            },
            {
              label: "邀请码",
              prop: "inviteCode",
              rules: [{
                required: true,
                message: "请输入邀请码",
                trigger: "blur"
              }]
            },
            {
              label: "邀请码ID",
              prop: "inviteCodeId",
              rules: [{
                required: true,
                message: "请输入邀请码ID",
                trigger: "blur"
              }]
            },
            {
              label: "邀请类型：qrcode-二维码邀请，link-链接邀请，share-分享邀请",
              prop: "inviteType",
              rules: [{
                required: true,
                message: "请输入邀请类型：qrcode-二维码邀请，link-链接邀请，share-分享邀请",
                trigger: "blur"
              }]
            },
            {
              label: "邀请来源：miniapp-小程序，h5-H5页面，app-APP",
              prop: "inviteSource",
              rules: [{
                required: true,
                message: "请输入邀请来源：miniapp-小程序，h5-H5页面，app-APP",
                trigger: "blur"
              }]
            },
            {
              label: "关系状态：pending-待确认，active-有效，inactive-无效",
              prop: "relationStatus",
              rules: [{
                required: true,
                message: "请输入关系状态：pending-待确认，active-有效，inactive-无效",
                trigger: "blur"
              }]
            },
            {
              label: "注册时间",
              prop: "registerTime",
              rules: [{
                required: true,
                message: "请输入注册时间",
                trigger: "blur"
              }]
            },
            {
              label: "首次互动时间",
              prop: "firstInteractionTime",
              rules: [{
                required: true,
                message: "请输入首次互动时间",
                trigger: "blur"
              }]
            },
            {
              label: "关系确认时间",
              prop: "confirmTime",
              rules: [{
                required: true,
                message: "请输入关系确认时间",
                trigger: "blur"
              }]
            },
            {
              label: "奖励状态：0-未发放，1-已发放，2-发放失败",
              prop: "rewardStatus",
              rules: [{
                required: true,
                message: "请输入奖励状态：0-未发放，1-已发放，2-发放失败",
                trigger: "blur"
              }]
            },
            {
              label: "奖励类型：points-积分，coupon-优惠券，cash-现金",
              prop: "rewardType",
              rules: [{
                required: true,
                message: "请输入奖励类型：points-积分，coupon-优惠券，cash-现金",
                trigger: "blur"
              }]
            },
            {
              label: "奖励金额/数量",
              prop: "rewardAmount",
              rules: [{
                required: true,
                message: "请输入奖励金额/数量",
                trigger: "blur"
              }]
            },
            {
              label: "奖励发放时间",
              prop: "rewardTime",
              rules: [{
                required: true,
                message: "请输入奖励发放时间",
                trigger: "blur"
              }]
            },
            {
              label: "邀请渠道详情",
              prop: "channelDetails",
              rules: [{
                required: true,
                message: "请输入邀请渠道详情",
                trigger: "blur"
              }]
            },
            {
              label: "设备信息",
              prop: "deviceInfo",
              rules: [{
                required: true,
                message: "请输入设备信息",
                trigger: "blur"
              }]
            },
            {
              label: "IP地址",
              prop: "ipAddress",
              rules: [{
                required: true,
                message: "请输入IP地址",
                trigger: "blur"
              }]
            },
            {
              label: "地理位置信息",
              prop: "locationInfo",
              rules: [{
                required: true,
                message: "请输入地理位置信息",
                trigger: "blur"
              }]
            },
            {
              label: "备注信息",
              prop: "remark",
              rules: [{
                required: true,
                message: "请输入备注信息",
                trigger: "blur"
              }]
            },
            {
              label: "扩展信息（JSON格式）",
              prop: "extInfo",
              rules: [{
                required: true,
                message: "请输入扩展信息（JSON格式）",
                trigger: "blur"
              }]
            },
            {
              label: "状态标识",
              prop: "statusFlag",
              rules: [{
                required: true,
                message: "请输入状态标识",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.inviterelation_add, false),
          viewBtn: this.validData(this.permission.inviterelation_view, false),
          delBtn: this.validData(this.permission.inviterelation_delete, false),
          editBtn: this.validData(this.permission.inviterelation_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
