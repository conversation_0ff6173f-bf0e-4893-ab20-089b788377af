/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.points.entity.PointsGoods;
import org.springblade.business.points.service.IPointsGoodsService;
import org.springblade.business.points.vo.PointsGoodsVO;
import org.springblade.business.points.wrapper.PointsGoodsWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 积分商城商品表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/pointsgoods")
@io.swagger.v3.oas.annotations.tags.Tag(name = "积分商城商品表", description = "积分商城商品表接口")
public class PointsGoodsController extends BladeController {

	private IPointsGoodsService pointsGoodsService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入pointsGoods")
	public R<PointsGoodsVO> detail(PointsGoods pointsGoods) {
		PointsGoods detail = pointsGoodsService.getOne(Condition.getQueryWrapper(pointsGoods));
		return R.data(PointsGoodsWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 积分商城商品表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入pointsGoods")
	public R<IPage<PointsGoodsVO>> list(PointsGoods pointsGoods, Query query) {
		IPage<PointsGoods> pages = pointsGoodsService.page(Condition.getPage(query), Condition.getQueryWrapper(pointsGoods));
		return R.data(PointsGoodsWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 积分商城商品表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入pointsGoods")
	public R<IPage<PointsGoodsVO>> page(PointsGoodsVO pointsGoods, Query query) {
		IPage<PointsGoodsVO> pages = pointsGoodsService.selectPointsGoodsPage(Condition.getPage(query), pointsGoods);
		return R.data(pages);
	}

	/**
	 * 新增 积分商城商品表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入pointsGoods")
	public R save(@Valid @RequestBody PointsGoods pointsGoods) {
		boolean result = pointsGoodsService.savePointsGoods(pointsGoods);
		return R.status(result);
	}

	/**
	 * 修改 积分商城商品表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入pointsGoods")
	public R update(@Valid @RequestBody PointsGoods pointsGoods) {
		boolean result = pointsGoodsService.updatePointsGoodsById(pointsGoods);
		return R.status(result);
	}

	/**
	 * 新增或修改 积分商城商品表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入pointsGoods")
	public R submit(@Valid @RequestBody PointsGoods pointsGoods) {
		boolean result = pointsGoodsService.saveOrUpdatePointsGoods(pointsGoods);
		return R.status(result);
	}


	/**
	 * 删除 积分商城商品表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = pointsGoodsService.deleteLogicPointsGoods(ids);
		return R.status(result);
	}


}
