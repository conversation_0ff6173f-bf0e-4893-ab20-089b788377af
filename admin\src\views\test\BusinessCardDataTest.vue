<template>
  <div class="business-card-data-test">
    <h2>名片数据记录功能测试</h2>
    
    <el-card class="test-card">
      <h3>测试名片数据记录查询</h3>
      <el-form inline>
        <el-form-item label="名片ID:">
          <el-input v-model="testParams.relevancyId" placeholder="请输入名片ID" />
        </el-form-item>
        <el-form-item label="记录类型:">
          <el-select v-model="testParams.recordType" placeholder="请选择记录类型">
            <el-option label="浏览记录" value="view" />
            <el-option label="点赞记录" value="like" />
            <el-option label="反馈记录" value="feedback" />
            <el-option label="收藏记录" value="favorite" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testDataRecords">测试查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="testBusinessCardList">测试名片列表</el-button>
        </el-form-item>
      </el-form>

      <div v-if="testResult">
        <h4>查询结果:</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <h3>测试数据记录对话框</h3>
      <el-form inline>
        <el-form-item label="名片ID:">
          <el-input v-model="dialogTestParams.relevancyId" placeholder="请输入名片ID" />
        </el-form-item>
        <el-form-item label="记录类型:">
          <el-select v-model="dialogTestParams.recordType" placeholder="请选择记录类型">
            <el-option label="浏览记录" value="view" />
            <el-option label="点赞记录" value="like" />
            <el-option label="反馈记录" value="feedback" />
            <el-option label="收藏记录" value="favorite" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="showTestDialog">打开对话框</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据记录查看对话框 -->
    <DataRecordDialog
      v-if="dialogVisible && apiConfig"
      v-model="dialogVisible"
      :record-type="dialogTestParams.recordType"
      :relevancy-id="dialogTestParams.relevancyId"
      :relevancy-type="'1'"
      :api-config="apiConfig" />
  </div>
</template>

<script>
import { dataRecordApiConfig } from '@/api/ad/datarecord'
import { getList } from '@/api/ad/businesscard'
import DataRecordDialog from '@/components/DataRecordDialog.vue'

export default {
  name: 'BusinessCardDataTest',
  components: {
    DataRecordDialog
  },
  data() {
    return {
      testParams: {
        relevancyId: '',
        recordType: 'view'
      },
      dialogTestParams: {
        relevancyId: '',
        recordType: 'view'
      },
      testResult: null,
      dialogVisible: false,
      dataRecordApiConfig: dataRecordApiConfig
    }
  },
  computed: {
    apiConfig() {
      return this.dataRecordApiConfig[this.dialogTestParams.recordType]
    }
  },
  methods: {
    async testDataRecords() {
      if (!this.testParams.relevancyId || !this.testParams.recordType) {
        this.$message.warning('请输入名片ID和选择记录类型')
        return
      }
      
      try {
        const apiConfig = this.dataRecordApiConfig[this.testParams.recordType]
        if (!apiConfig) {
          this.$message.error('不支持的记录类型')
          return
        }
        
        const params = {
          current: 1,
          size: 10,
          relevancyId: this.testParams.relevancyId,
          type: '1' // 名片类型
        }
        
        console.log('测试参数:', params)
        const response = await apiConfig.getList(params)
        console.log('API响应:', response)
        
        this.testResult = response
        this.$message.success('查询成功')
      } catch (error) {
        console.error('查询失败:', error)
        this.$message.error('查询失败: ' + error.message)
        this.testResult = { error: error.message }
      }
    },
    
    showTestDialog() {
      if (!this.dialogTestParams.relevancyId || !this.dialogTestParams.recordType) {
        this.$message.warning('请先输入名片ID和选择记录类型')
        return
      }
      this.dialogVisible = true
    },

    async testBusinessCardList() {
      try {
        console.log('测试名片列表查询...')
        const response = await getList(1, 10, {})
        console.log('名片列表响应:', response)

        this.testResult = {
          type: 'businessCardList',
          data: response
        }
        this.$message.success('名片列表查询成功')
      } catch (error) {
        console.error('名片列表查询失败:', error)
        this.$message.error('名片列表查询失败: ' + error.message)
        this.testResult = {
          type: 'businessCardList',
          error: error.message
        }
      }
    }
  }
}
</script>

<style scoped>
.business-card-data-test {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
