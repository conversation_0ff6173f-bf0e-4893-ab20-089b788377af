
package org.springblade.business.post.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 消息模板更新DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "消息模板更新DTO")
public class MessageTemplateUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "模板ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标题模板
     */
    @Schema(description = "消息标题模板")
    private String titleTemplate;

    /**
     * 内容模板
     */
    @Schema(description = "消息内容模板")
    private String contentTemplate;

    /**
     * 状态(1:启用 0:禁用)
     */
    @Schema(description = "模板状态(1:启用 0:禁用)")
    private Integer status;
}
