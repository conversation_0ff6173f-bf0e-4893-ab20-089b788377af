/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 微信支付服务接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IWechatPayService {

    /**
     * 创建小程序支付订单
     *
     * @param orderNo     订单号
     * @param totalAmount 支付金额（单位：元）
     * @param description 商品描述
     * @param openId      用户openId
     * @param clientIp    客户端IP
     * @return 小程序支付参数
     * @throws WxPayException 微信支付异常
     */
    Map<String, String> createMiniAppPayOrder(String orderNo, BigDecimal totalAmount,
                                             String description, String openId,
                                             String clientIp) throws WxPayException;

    /**
     * 统一下单
     *
     * @param request 统一下单请求
     * @return 统一下单结果
     * @throws WxPayException 微信支付异常
     */
    WxPayUnifiedOrderResult unifiedOrder(WxPayUnifiedOrderRequest request) throws WxPayException;

    /**
     * 查询订单
     *
     * @param transactionId 微信支付交易号
     * @param outTradeNo    商户订单号
     * @return 订单查询结果
     * @throws WxPayException 微信支付异常
     */
    Map<String, String> queryOrder(String transactionId, String outTradeNo) throws WxPayException;

    /**
     * 关闭订单
     *
     * @param outTradeNo 商户订单号
     * @return 操作是否成功
     * @throws WxPayException 微信支付异常
     */
    boolean closeOrder(String outTradeNo) throws WxPayException;

    /**
     * 申请退款
     *
     * @param outTradeNo   商户订单号
     * @param outRefundNo  商户退款单号
     * @param totalAmount  订单总金额（单位：元）
     * @param refundAmount 退款金额（单位：元）
     * @param reason       退款原因
     * @return 退款申请结果
     * @throws WxPayException 微信支付异常
     */
    Map<String, String> refund(String outTradeNo, String outRefundNo,
                               BigDecimal totalAmount, BigDecimal refundAmount,
                               String reason) throws WxPayException;

    /**
     * 处理支付回调
     *
     * @param xmlData 回调数据
     * @return 处理结果
     * @throws WxPayException 微信支付异常
     */
    Map<String, Object> handlePayNotify(String xmlData) throws WxPayException;

    /**
     * 验证回调签名
     *
     * @param xmlData 回调数据
     * @return 验证是否通过
     */
    boolean verifyNotifySign(String xmlData);

    /**
     * 查询微信订单状态
     *
     * @param outTradeNo 商户订单号
     * @return 订单状态信息
     */
    Map<String, Object> queryOrderStatus(String outTradeNo);
}
