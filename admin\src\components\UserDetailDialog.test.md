# 用户详情弹窗功能测试说明

## 功能概述
在以下页面中实现了点击用户昵称弹出用户详情信息弹窗的功能：
- 帖子管理页面 (`/admin/src/views/ad/post/post.vue`)
- 反馈管理页面 (`/admin/src/views/ad/post/feedback.vue`)
- 名片管理页面 (`/admin/src/views/ad/businesscard.vue`)

## 实现的功能

### 1. 用户详情弹窗组件 (`UserDetailDialog.vue`)
- 显示用户基本信息（头像、昵称、真实姓名、性别、手机号、邮箱等）
- 显示账户状态信息（账户状态、实名认证、VIP状态等）
- 显示活动统计信息（发布帖子数、反馈数、名片数等）
- 显示个人简介
- 提供管理用户按钮（可扩展）

### 2. 点击用户昵称功能
- 在表格中将用户昵称显示为可点击的链接样式
- 点击后弹出用户详情弹窗
- 自动加载用户详细信息

### 3. API接口预留
- 在 `/admin/src/api/ad/user.js` 中添加了 `getUserDetail` 方法
- 接口地址：`/blade-ad/user/detail`
- 参数：`id` (用户ID)
- 返回：用户详细信息

## 测试步骤

### 1. 帖子管理页面测试
1. 访问帖子管理页面
2. 在表格的"发布者"列中，用户昵称应显示为蓝色可点击链接
3. 点击用户昵称，应弹出用户详情弹窗
4. 弹窗应显示用户的详细信息

### 2. 反馈管理页面测试
1. 访问反馈管理页面
2. 在表格的"反馈用户"列中，用户昵称应显示为蓝色可点击链接
3. 点击用户昵称，应弹出用户详情弹窗

### 3. 名片管理页面测试
1. 访问名片管理页面
2. 在表格的"用户"列中，用户昵称应显示为蓝色可点击链接
3. 点击用户昵称，应弹出用户详情弹窗

## 后端开发需要实现的接口

### 用户详情接口
```
GET /blade-ad/user/detail?id={userId}

响应格式：
{
  "code": 200,
  "success": true,
  "data": {
    "id": "用户ID",
    "nickname": "用户昵称",
    "realName": "真实姓名",
    "avatar": "头像URL",
    "gender": 1, // 0-保密，1-男，2-女
    "phone": "手机号",
    "email": "邮箱",
    "birthday": "生日",
    "province": "省份",
    "city": "城市",
    "district": "区县",
    "bio": "个人简介",
    "status": 1, // 1-正常，0-禁用
    "isRealNameAuth": true, // 是否实名认证
    "isVip": false, // 是否VIP用户
    "createTime": "注册时间",
    "lastLoginTime": "最后登录时间",
    "loginCount": 10, // 登录次数
    "stats": {
      "postCount": 5, // 发布帖子数
      "feedbackCount": 3, // 发布反馈数
      "businessCardCount": 1, // 名片数量
      "favoriteCount": 8 // 收藏数量
    }
  }
}
```

## 注意事项
1. 确保后端接口返回的数据格式与前端期望的格式一致
2. 如果用户不存在或获取失败，应返回适当的错误信息
3. 可以根据实际需求调整用户详情弹窗中显示的字段
4. "管理用户"按钮的功能可以根据需求进行扩展，比如跳转到用户管理页面
