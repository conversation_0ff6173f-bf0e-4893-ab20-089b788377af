/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.tokenizer.Word;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springblade.business.post.controller.TagController;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.entity.CategoryTag;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.service.ICategoryTagService;
import org.springblade.business.post.vo.CategoryTagVO;
import org.springblade.business.user.mapper.CategoryMapper;
import org.springblade.business.user.mapper.CategoryTagMapper;
import org.springblade.business.user.mapper.TagMapper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_TAGS;

/**
 * 分类标签关联服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@AllArgsConstructor
public class CategoryTagServiceImpl implements ICategoryTagService {

	private final CategoryMapper categoryMapper;
	private final TagMapper tagMapper;
	private final CategoryTagMapper categoryTagMapper;

	@Override
	public IPage<CategoryTagVO> selectCategoryTagPage(Page<CategoryTagVO> page, CategoryTagVO categoryTag) {
		return page.setRecords(categoryTagMapper.selectCategoryTagPage(page, categoryTag));
	}

	@Override
	public List<Tag> getTagsByCategory(Long categoryId) {
		return categoryTagMapper.selectTagsByCategoryId(categoryId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@CacheEvict(
		cacheNames = WECHAT_CATEGORY_TAGS,
		key = "#tagRequest.categoryId"  // 删除对应分类ID的缓存
	)
	public Boolean addTagToCategory(TagController.TagCreateRequest tagRequest) {
		Long categoryId = tagRequest.getCategoryId();
		Long tagId = tagRequest.getTagId();
		Integer type = tagRequest.getType();
		// 检查分类和标签是否存在
		Category category = categoryMapper.selectById(categoryId);
		Tag tag = tagMapper.selectById(tagId);
		if (category == null || tag == null) {
			return false;
		}
		// 检查是否已经关联
		boolean existing = this.isExistTag(categoryId, tagId, type);
		if (existing) {
			return true; // 已经关联，返回成功
		}

		// 创建关联
		CategoryTag categoryTag = new CategoryTag();
		categoryTag.setCategoryId(categoryId);
		categoryTag.setType(type);
		categoryTag.setTagId(tagId);
		categoryTag.setSortOrder(0);

		return categoryTagMapper.insert(categoryTag) > 0;
	}


	private boolean isExistTag(Long tagId, Long categoryId, Integer type) {
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getTagId, tagId)
			.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getType, type);
		return categoryTagMapper.selectCount(queryWrapper) > 0;
	}


	@Override
	public List<Tag> getTagsByCategoryAndType(Long categoryId, Integer type) {
		LambdaQueryWrapper<CategoryTag> wrapper = new LambdaQueryWrapper<CategoryTag>()
			.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getType, type);
		List<CategoryTag> categoryTags = categoryTagMapper.selectList(wrapper);
		List<Long> tagIds = categoryTags.stream()
			.map(CategoryTag::getTagId)
			.collect(Collectors.toList());
		if (CollUtil.isEmpty(tagIds)) {
			return new ArrayList<>();
		}
		return tagMapper.selectList(new LambdaQueryWrapper<Tag>().in(Tag::getId, tagIds));
	}

	@Override
	@Caching(
		evict = {
			@CacheEvict(
				cacheNames = WECHAT_CATEGORY_TAGS,
				key = "#tagRequest.categoryId"  // 删除对应分类ID的缓存
			)
		}
	)
	public Boolean removeFeedbackTagFromCategory(TagController.TagCreateRequest tagRequest) {
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<CategoryTag>()
			.eq(CategoryTag::getCategoryId, tagRequest.getCategoryId())
			.eq(CategoryTag::getTagId, tagRequest.getTagId())
			.eq(CategoryTag::getType, tagRequest.getType());
		return categoryTagMapper.delete(queryWrapper) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void removeByTags(List<Long> ids) {
		LambdaQueryWrapper<CategoryTag> wp = new LambdaQueryWrapper<>();
		wp.in(CategoryTag::getTagId,ids);
		this.categoryTagMapper.delete(wp);
	}
}
