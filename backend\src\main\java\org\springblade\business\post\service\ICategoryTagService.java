/**
 * Copyright (c) 2018-2099, Chill <PERSON>ang 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.business.post.controller.TagController;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.vo.CategoryTagVO;

import java.util.List;

/**
 * 分类标签关联服务接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ICategoryTagService {

	/**
	 * 分页查询分类标签关联
	 *
	 * @param page        分页参数
	 * @param categoryTag 查询条件
	 * @return 分页结果
	 */
	IPage<CategoryTagVO> selectCategoryTagPage(Page<CategoryTagVO> page, CategoryTagVO categoryTag);

	/**
	 * 根据分类ID获取标签列表
	 *
	 * @param categoryId 分类ID
	 * @return 标签列表
	 */
	List<Tag> getTagsByCategory(Long categoryId);

	Boolean addTagToCategory(TagController.TagCreateRequest tagRequest);

	List<Tag> getTagsByCategoryAndType(Long categoryId, Integer type);

	Boolean removeFeedbackTagFromCategory(TagController.TagCreateRequest tagRequest);

	void removeByTags(List<Long> ids);
}
