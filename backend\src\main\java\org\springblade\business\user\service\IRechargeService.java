/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.user.entity.RechargeOrder;
import org.springblade.business.user.vo.RechargeOrderVO;
import org.springblade.core.mp.base.BaseService;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 充值服务接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IRechargeService extends BaseService<RechargeOrder> {


	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rechargeOrder
	 * @return
	 */
	IPage<RechargeOrderVO> selectRechargeOrderPage(IPage<RechargeOrderVO> page, RechargeOrderVO rechargeOrder);

    /**
     * 获取充值金额限制配置
     *
     * @return 充值金额限制配置 {minAmount: 最小金额, maxAmount: 最大金额, suggestedAmounts: 建议金额列表}
     */
    Map<String, Object> getRechargeAmountConfig();

    /**
     * 验证充值金额是否有效
     *
     * @param amount 充值金额
     * @return 验证结果
     */
    boolean validateRechargeAmount(BigDecimal amount);

    /**
     * 创建充值订单
     *
     * @param userId        用户ID
     * @param rechargeAmount 充值金额（单位：元）
     * @param openId        用户openId
     * @param clientIp      客户端IP
     * @param configName    支付配置名称（可选，为空则使用默认配置）
     * @return 充值订单和支付参数
     */
    Map<String, Object> createRechargeOrder(Long userId, BigDecimal rechargeAmount, String openId, String clientIp, String configName);

    /**
     * 根据订单号获取充值订单
     *
     * @param orderNo 订单号
     * @return 充值订单
     */
    RechargeOrder getRechargeOrderByOrderNo(String orderNo);

    /**
     * 处理支付成功回调
     *
     * @param orderNo       订单号
     * @param transactionId 微信支付交易号
     * @param paymentTime   支付时间
     * @return 处理是否成功
     */
    boolean handlePaymentSuccess(String orderNo, String transactionId, String paymentTime);

    /**
     * 处理充值成功
     *
     * @param rechargeOrder 充值订单
     * @return 处理是否成功
     */
    boolean handleRechargeSuccess(RechargeOrder rechargeOrder);

    /**
     * 取消充值订单
     *
     * @param orderNo 订单号
     * @param reason  取消原因
     * @return 操作是否成功
     */
    boolean cancelRechargeOrder(String orderNo, String reason);

    /**
     * 查询充值订单状态
     *
     * @param orderNo 订单号
     * @return 订单状态信息
     */
    Map<String, Object> queryRechargeOrderStatus(String orderNo);

    /**
     * 获取用户充值记录
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 充值记录列表
     */
    Map<String, Object> getUserRechargeRecords(Long userId, Integer page, Integer size);
}
