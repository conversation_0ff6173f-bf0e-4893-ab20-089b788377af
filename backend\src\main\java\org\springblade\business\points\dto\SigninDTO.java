package org.springblade.business.points.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 签到DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "签到DTO")
public class SigninDTO {

    /**
     * 用户OpenID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 签到日期（补签时使用，格式：YYYY-MM-DD）
     */
    @Schema(description = "签到日期（补签时使用）")
    private String signinDate;

    /**
     * 签到类型（NORMAL：正常签到，MAKEUP：补签）
     */
    @Schema(description = "签到类型")
    private String signinType = "NORMAL";

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    private String deviceInfo;

    /**
     * 签到IP
     */
    @Schema(description = "签到IP")
    private String signinIp;
}
