/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.service;


import org.springblade.business.subscriptionPlan.entity.Feature;
import org.springblade.business.subscriptionPlan.entity.UserFeaturePermission;
import org.springblade.business.subscriptionPlan.vo.UserFeaturePermissionVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 用户功能权限表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface IUserFeaturePermissionService extends BaseService<UserFeaturePermission> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userFeaturePermission
	 * @return
	 */
	IPage<UserFeaturePermissionVO> selectUserFeaturePermissionPage(IPage<UserFeaturePermissionVO> page, UserFeaturePermissionVO userFeaturePermission);

	boolean hasPermission(Long userId, String featureCode);

	List<Feature> selectFeaturesByUser(Long userId);

	void deleteBySubscriptionId(Long id);
}
