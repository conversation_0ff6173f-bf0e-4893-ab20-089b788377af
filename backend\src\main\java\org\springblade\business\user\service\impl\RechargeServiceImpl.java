/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.exception.WxPayException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.config.RechargeConfig;
import org.springblade.business.user.entity.RechargeOrder;
import org.springblade.business.user.entity.UserBalanceLog;
import org.springblade.business.user.mapper.RechargeOrderMapper;
import org.springblade.business.user.service.IRechargeService;
import org.springblade.business.user.service.IUserBalanceService;
import org.springblade.business.user.service.IWechatPayService;
import org.springblade.business.user.vo.RechargeOrderVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 充值服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class RechargeServiceImpl extends BaseServiceImpl<RechargeOrderMapper, RechargeOrder> implements IRechargeService {

    private final RechargeOrderMapper rechargeOrderMapper;
    private final IWechatPayService wechatPayService;
    private final IUserBalanceService userBalanceService;
    private final RechargeConfig rechargeConfig;


	@Override
	public IPage<RechargeOrderVO> selectRechargeOrderPage(IPage<RechargeOrderVO> page, RechargeOrderVO rechargeOrder) {
		return page.setRecords(baseMapper.selectRechargeOrderPage(page, rechargeOrder));
	}

    @Override
    public Map<String, Object> getRechargeAmountConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", rechargeConfig.getEnabled());
        config.put("minAmount", rechargeConfig.getMinAmount());
        config.put("maxAmount", rechargeConfig.getMaxAmount());
        config.put("suggestedAmounts", rechargeConfig.getSuggestedAmounts());
        config.put("orderExpireMinutes", rechargeConfig.getOrderExpireMinutes());
        return config;
    }

    @Override
    public boolean validateRechargeAmount(BigDecimal amount) {
        if (!rechargeConfig.getEnabled()) {
            log.warn("充值功能已禁用");
            return false;
        }

        // 基础验证
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 使用正常配置验证
        boolean isValid = rechargeConfig.isValidAmount(amount);
        log.debug("使用正常配置验证充值金额: {}, 结果: {}", amount, isValid);
        return isValid;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> createRechargeOrder(Long userId, BigDecimal rechargeAmount,
                                                  String openId, String clientIp, String configName) {

        // 验证充值金额
//        if (!validateRechargeAmount(rechargeAmount)) {
//            throw new RuntimeException("充值金额无效");
//        }

        try {
            // 生成订单号
            String orderNo = generateOrderNo();

            // 创建充值订单
            RechargeOrder rechargeOrder = new RechargeOrder();
            rechargeOrder.setOrderNo(orderNo);
            rechargeOrder.setUserId(userId);
            rechargeOrder.setRechargeAmount(rechargeAmount);
            rechargeOrder.setPaymentMethod(RechargeOrder.PaymentMethod.WECHAT_PAY.getCode());
            rechargeOrder.setOrderStatus(RechargeOrder.OrderStatus.PENDING.getCode());
            rechargeOrder.setPaymentStatus(RechargeOrder.PaymentStatus.UNPAID.getCode());
            rechargeOrder.setClientIp(clientIp);
            rechargeOrder.setExpireTime(LocalDateTime.now().plusMinutes(rechargeConfig.getOrderExpireMinutes()));

            // 生成订单描述
            String orderDescription = rechargeConfig.formatDescription(rechargeAmount);
            rechargeOrder.setRemark(orderDescription);

            int insertCount = rechargeOrderMapper.insert(rechargeOrder);
            if (insertCount <= 0) {
                throw new RuntimeException("创建充值订单失败");
            }

            // 调用微信支付创建订单
            Map<String, String> payParams = wechatPayService.createMiniAppPayOrder(
                orderNo,
                rechargeAmount,
                orderDescription,
                openId,
                clientIp
            );

            // 更新订单的微信支付相关字段
            if (payParams != null && !payParams.isEmpty()) {
                String prepayId = payParams.get("prepayId");
                String wxOutTradeNo = payParams.get("outTradeNo");

                if (prepayId != null && wxOutTradeNo != null) {
                    rechargeOrder.setWxPrepayId(prepayId);
                    rechargeOrder.setWxOutTradeNo(wxOutTradeNo);

                    // 更新订单
                    int updateCount = rechargeOrderMapper.updateById(rechargeOrder);
                    if (updateCount > 0) {
                        log.info("更新订单微信支付信息成功: orderNo={}, prepayId={}, wxOutTradeNo={}",
                            orderNo, prepayId, wxOutTradeNo);
                    } else {
                        log.warn("更新订单微信支付信息失败: orderNo={}", orderNo);
                    }
                } else {
                    log.warn("微信支付返回信息不完整: orderNo={}, payParams={}", orderNo, payParams);
                }
            } else {
                log.warn("微信支付返回参数为空: orderNo={}", orderNo);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("orderNo", orderNo);
            result.put("rechargeAmount", rechargeAmount);
            result.put("expireTime", rechargeOrder.getExpireTime());
            result.put("payParams", payParams);

            log.info("创建充值订单成功: userId={}, orderNo={}, amount={}", userId, orderNo, rechargeAmount);
            return result;

        } catch (WxPayException e) {
            log.error("创建微信支付订单失败: userId={}, amount={}", userId, rechargeAmount, e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("创建充值订单异常: userId={}, amount={}", userId, rechargeAmount, e);
            throw new RuntimeException("创建充值订单失败");
        }
    }

    @Override
    public RechargeOrder getRechargeOrderByOrderNo(String orderNo) {
        if (StringUtil.isBlank(orderNo)) {
            log.warn("订单号为空");
            return null;
        }

        try {
            log.debug("查询充值订单: orderNo={}", orderNo);
            RechargeOrder order = rechargeOrderMapper.selectByOrderNo(orderNo);
            log.debug("查询结果: {}", order != null ? "找到订单" : "订单不存在");
            return order;
        } catch (Exception e) {
            log.error("查询充值订单异常: orderNo={}", orderNo, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentSuccess(String orderNo, String transactionId, String paymentTime) {
        try {
            RechargeOrder rechargeOrder = getRechargeOrderByOrderNo(orderNo);
            if (rechargeOrder == null) {
                log.warn("充值订单不存在: orderNo={}", orderNo);
                return false;
            }

            // 检查订单状态
            if (!RechargeOrder.OrderStatus.PENDING.getCode().equals(rechargeOrder.getOrderStatus())) {
                log.warn("充值订单状态异常: orderNo={}, status={}", orderNo, rechargeOrder.getOrderStatus());
                return false;
            }

            // 更新订单状态
            rechargeOrder.setWxTransactionId(transactionId);
            rechargeOrder.setPaymentStatus(RechargeOrder.PaymentStatus.PAID.getCode());
            rechargeOrder.setOrderStatus(RechargeOrder.OrderStatus.PAID.getCode());
            rechargeOrder.setPaymentTime(LocalDateTime.parse(paymentTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

            int updateCount = rechargeOrderMapper.updateById(rechargeOrder);
            if (updateCount <= 0) {
                log.warn("更新充值订单状态失败: orderNo={}", orderNo);
                return false;
            }

            // 处理充值成功
            return handleRechargeSuccess(rechargeOrder);

        } catch (Exception e) {
            log.error("处理支付成功回调异常: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleRechargeSuccess(RechargeOrder rechargeOrder) {
        try {
            // 增加用户钱包余额
            boolean balanceResult = userBalanceService.addWalletBalance(
                rechargeOrder.getUserId(),
                rechargeOrder.getRechargeAmount(),
                UserBalanceLog.ChangeType.RECHARGE.getCode(),
                UserBalanceLog.BusinessType.RECHARGE.getCode(),
                rechargeOrder.getOrderNo(),
                rechargeOrder.getOrderNo(),
                "充值到账: " + rechargeOrder.getRemark()
            );

            if (!balanceResult) {
                log.error("增加用户钱包余额失败: orderNo={}", rechargeOrder.getOrderNo());
                return false;
            }

            // 更新订单状态为成功
            rechargeOrder.setOrderStatus(RechargeOrder.OrderStatus.SUCCESS.getCode());
            rechargeOrder.setSuccessTime(LocalDateTime.now());

            int updateCount = rechargeOrderMapper.updateById(rechargeOrder);
            if (updateCount <= 0) {
                log.error("更新充值订单状态为成功失败: orderNo={}", rechargeOrder.getOrderNo());
                return false;
            }

            log.info("处理充值成功: userId={}, orderNo={}, amount={}",
                    rechargeOrder.getUserId(), rechargeOrder.getOrderNo(), rechargeOrder.getRechargeAmount());
            return true;

        } catch (Exception e) {
            log.error("处理充值成功异常: orderNo={}", rechargeOrder.getOrderNo(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelRechargeOrder(String orderNo, String reason) {
        try {
            RechargeOrder rechargeOrder = getRechargeOrderByOrderNo(orderNo);
            if (rechargeOrder == null) {
                log.warn("充值订单不存在: orderNo={}", orderNo);
                return false;
            }

            // 只能取消待支付的订单
            if (!RechargeOrder.OrderStatus.PENDING.getCode().equals(rechargeOrder.getOrderStatus())) {
                log.warn("订单状态不允许取消: orderNo={}, status={}", orderNo, rechargeOrder.getOrderStatus());
                return false;
            }

            // 更新订单状态
            rechargeOrder.setOrderStatus(RechargeOrder.OrderStatus.CANCELLED.getCode());
            rechargeOrder.setRemark(rechargeOrder.getRemark() + " [取消原因: " + reason + "]");

            int updateCount = rechargeOrderMapper.updateById(rechargeOrder);
            if (updateCount > 0) {
                log.info("取消充值订单成功: orderNo={}, reason={}", orderNo, reason);
                return true;
            } else {
                log.warn("取消充值订单失败: orderNo={}", orderNo);
                return false;
            }

        } catch (Exception e) {
            log.error("取消充值订单异常: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> queryRechargeOrderStatus(String orderNo) {
        RechargeOrder rechargeOrder = getRechargeOrderByOrderNo(orderNo);
        if (rechargeOrder == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("orderNo", rechargeOrder.getOrderNo());
        result.put("orderStatus", rechargeOrder.getOrderStatus());
        result.put("paymentStatus", rechargeOrder.getPaymentStatus());
        result.put("rechargeAmount", rechargeOrder.getRechargeAmount());
        result.put("createTime", rechargeOrder.getCreateTime());
        result.put("paymentTime", rechargeOrder.getPaymentTime());
        result.put("successTime", rechargeOrder.getSuccessTime());
        result.put("expireTime", rechargeOrder.getExpireTime());
        result.put("remark", rechargeOrder.getRemark());

        return result;
    }

    @Override
    public Map<String, Object> getUserRechargeRecords(Long userId, Integer page, Integer size) {
        // 构建分页对象
        IPage<RechargeOrder> pageObj = new Page<>(page, size);

        // 分页查询用户充值记录
        IPage<RechargeOrder> resultPage = rechargeOrderMapper.selectUserRechargeOrderPage(pageObj, userId);

        Map<String, Object> result = new HashMap<>();
        result.put("records", resultPage.getRecords());
        result.put("total", resultPage.getTotal());
        result.put("current", resultPage.getCurrent());
        result.put("size", resultPage.getSize());
        result.put("pages", resultPage.getPages());


        return result;
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    private String generateOrderNo() {
        // 格式：RC + yyyyMMddHHmmss + 6位随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%06d", (int) (Math.random() * 1000000));
        return "RC" + timestamp + random;
    }
}
