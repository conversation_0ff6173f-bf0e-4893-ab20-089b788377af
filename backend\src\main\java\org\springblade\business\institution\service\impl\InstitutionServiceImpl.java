/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.entity.InstitutionAuditLog;
import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.mapper.InstitutionMapper;
import org.springblade.business.institution.mapper.InstitutionTypeMapper;
import org.springblade.business.institution.mapper.UserInstitutionMapper;
import org.springblade.business.institution.service.IInstitutionService;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.common.constant.bizz.AuditStatusEnum;
import org.springblade.common.constant.bizz.PublishStatusEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机构主表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class InstitutionServiceImpl extends BaseServiceImpl<InstitutionMapper, Institution> implements IInstitutionService {

	@Resource
	private SupPostMapper supPostMapper;
	@Resource
	private UserInstitutionMapper userInstitutionMapper;
	@Resource
	private InstitutionTypeMapper institutionTypeMapper;
	@Resource
	private InstitutionAuditLogServiceImpl institutionAuditLogService;

	@Override
	@Transactional
	public IPage<InstitutionVO> selectInstitutionPage(IPage<InstitutionVO> page, InstitutionVO institutionVo) {
		//把vo转换为实体
		 Institution institution = new Institution();
		 BeanUtil.copyProperties(institutionVo, institution);
		Map<String, Object> params = new HashMap<>();
		params.put("visitUser",AuthUtil.getUserId());
		params.put("typeId", institutionVo.getTypeId());
		params.put("name", institutionVo.getName());
		params.put("description", institutionVo.getDescription());
		IPage<InstitutionVO> institutionVOIPage = page.setRecords(baseMapper.selectInstitutionPage(page, params));

		//根据机构id查询帖子
		for (InstitutionVO institutionVO : institutionVOIPage.getRecords()) {
			Integer postCount = supPostMapper.getPostCountByInstitutionId(institutionVO.getId());
			institutionVO.setPostCount(postCount != null ? postCount : 0); // 防止空指针
		}
		//补充机构分类名
		for (InstitutionVO institutionVO : institutionVOIPage.getRecords()) {
			InstitutionType institutionType = institutionTypeMapper.selectById(institutionVO.getTypeId());
			institutionVO.setTypeName(institutionType.getName());
		}

		return institutionVOIPage; // 修复返回值
	}



	@Override
	public boolean openOrClose(String ids) {
		//验证是不是管理员
		if (!AuthUtil.isAdministrator()){
			throw new SecureException("无操作权限");
		}
		List<Institution> institutions = this.listByIds(Func.toLongList(ids));
		if (institutions == null || institutions.isEmpty()){
			throw new ServiceException("机构不存在");
		}
		//把查询出来的机构的状态改为禁用或者启动
		for (Institution institution : institutions) {
			//判断当前机构状态
			if (institution.getStatus() == 0){
				institution.setStatus(1);
			}
			else {
				institution.setStatus(0);
			}
		}
		return this.updateBatchById(institutions);

	}

	@Override
	public IPage<InstitutionVO> InstitutionPage(Institution institution, Query query) {
		IPage<Institution> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(institution));
		//把Ipage里面的实体列表转化为vo列表
		List<InstitutionVO> collect = pages.getRecords().stream()
			.map(ins -> {
				InstitutionVO institutionVO = new InstitutionVO();
				BeanUtil.copyProperties(ins, institutionVO);
				//查询机构分类名
				if (institutionVO.getTypeId() != null) {
					InstitutionType institutionType = institutionTypeMapper.selectById(institutionVO.getTypeId());
					institutionVO.setTypeName(institutionType.getName());
				}
				//查询帖子总数
				Integer postCount = supPostMapper.getPostCountByInstitutionId(institutionVO.getId());
				institutionVO.setPostCount(postCount != null ? postCount : 0);

				return institutionVO;
			}).collect(Collectors.toList());
		//创建新的Ipage
		IPage<InstitutionVO> institutionVOIPage = new Page<>(pages.getCurrent(), pages.getSize(), pages.getTotal());
		institutionVOIPage.setRecords(collect);

		return institutionVOIPage;
	}

	@Override
	@Transactional
	public boolean batchAuditInstitution(List<Long> ids, String auditStatus, String auditRemark) {

		ArrayList<Institution> institutions = new ArrayList<>();
		for (Long id : ids) {
			Institution institution = new Institution();
			//判断一下是通过审核还是拒绝
			institution.setId(id);
			institution.setAuditStatus(auditStatus);
			institution.setLastAuditTime(LocalDateTime.now());
			institution.setLastAuditUserId(AuthUtil.getUserId());
			institution.setAuditRemark(auditRemark);
			institution.setPublishStatus(auditStatus.equals(AuditStatusEnum.APPROVED.getCode()) ?
				PublishStatusEnum.PUBLISHED.getValue() :
				PublishStatusEnum.DRAFT.getValue()
			);
			institutions.add(institution);
		}
		this.updateBatchById(institutions);

		//3.创建审核日志
		List<InstitutionAuditLog> auditLogs = new ArrayList<>();
		for (Institution institution : institutions) {
			InstitutionAuditLog auditLog = new InstitutionAuditLog();
			//判断一下是通过审核还是拒绝
			auditLog.setInstitutionId(institution.getId());
			auditLog.setAuditStatus(institution.getAuditStatus());
			auditLog.setAuditRemark(institution.getAuditRemark());
			auditLog.setAuditTime(LocalDateTime.now());
			auditLog.setAuditUserId(AuthUtil.getUserId());
			auditLogs.add(auditLog);
		}
		//4.保存审核日志
		institutionAuditLogService.saveBatch(auditLogs);
		return true;
	}

}
