/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.business.post.entity.Category;

import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.vo.CategoryVO;

import java.util.List;
import java.util.Map;

/**
 * 广告分类 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface CategoryMapper extends BaseMapper<Category> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param category
	 * @return
	 */
	List<CategoryVO> selectCategoryPage(IPage page, CategoryVO category);

	IPage<CategoryVO> selectCategoryList(IPage<CategoryVO> page, @Param("model") Map<String, Object> model);

	List<Tag> selectTagsByCategoryId(@Param("id") Long id);
}
