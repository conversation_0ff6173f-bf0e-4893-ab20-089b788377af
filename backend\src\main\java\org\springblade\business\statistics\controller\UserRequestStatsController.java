/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.statistics.controller;

import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springblade.business.statistics.entity.UserRequestStats;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.business.user.service.RedisRequestStatsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户信息 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/user/stats")
@io.swagger.v3.oas.annotations.tags.Tag(name = "用户请求统计信息", description = "用户请求统计接口接口")
public class UserRequestStatsController extends BladeController {

	private final IWeUserService iWeUserService;


	/**
	 * 统计每日用户的活跃数  时间区间内，用户活跃度xy
	 * 统计某一天前二十的用户
	 */
	@GetMapping("/getUserRequestStats")
	@Operation(summary = "统计日期区间每日的用户活跃度")
	public R<List<Map<String , Object>>> getUserRequestStats(@RequestParam String startDate, @RequestParam String endDate) {
		return R.data(iWeUserService.getUserRequestStats(startDate, endDate));
	}

	/**
	 * 统计某天前二十的用户
	 */
	@GetMapping("/getUserRequestStatsTop20")
	@Operation(summary = "统计某天前二十用户")
	public R<List<UserRequestStats>> getUserRequestStatsTop20(@RequestParam String date) {
		return R.data(iWeUserService.getUserRequestStatsTop20(date));
	}

}
