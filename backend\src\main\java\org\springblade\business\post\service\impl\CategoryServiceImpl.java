/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.entity.CategoryTag;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.AdminTagCreateRequest;
import org.springblade.business.post.service.ICategoryService;
import org.springblade.business.post.service.ITagService;
import org.springblade.business.post.vo.CategoryVO;
import org.springblade.business.user.mapper.CategoryMapper;
import org.springblade.business.user.mapper.CategoryTagMapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_ALL;
import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_LIST;

/**
 * 广告分类 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@AllArgsConstructor
public class CategoryServiceImpl extends BaseServiceImpl<CategoryMapper, Category> implements ICategoryService {

	private final ITagService tagService;
	private final CategoryTagMapper categoryTagMapper;

	@Override
	public IPage<CategoryVO> selectCategoryPage(IPage<CategoryVO> page, CategoryVO category) {
		return page.setRecords(baseMapper.selectCategoryPage(page, category));
	}


	@Override
	public List<Category> getCategoryTree() {
		// 获取所有分类
		LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.orderByAsc(Category::getSort)
			.orderByAsc(Category::getId);
		List<Category> allCategories = list(queryWrapper);

		// 构建树形结构
		return buildCategoryTree(allCategories, 0L);
	}

	@Override
	public Category getCategoryDetail(Long id) {
		Category category = getById(id);
		if (category != null) {
			// 加载标签
			List<Tag> tags = tagService.getTagsByCategory(id);
			category.setTags(tags);

			// 加载子分类
			List<Category> children = list(new LambdaQueryWrapper<Category>()
				.eq(Category::getParentId, id)
				.orderByAsc(Category::getSort));
			category.setChildren(children);
		}
		return category;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@Caching(
		evict = {
			// 删除启动的分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_LIST, allEntries = true)
		}
	)
	public Boolean enableCategory(Long id, Boolean enabled) {
		Category category = getById(id);
		if (category == null) {
			return false;
		}
		category.setEnabled(enabled ? 1 : 0);
		return updateById(category);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean enableCategoryAudit(Long id, Boolean enableAudit) {
		Category category = getById(id);
		if (category == null) {
			return false;
		}
		category.setEnableAudit(enableAudit ? 1 : 0);
		return updateById(category);
	}

	// ==================== 标签管理相关方法实现 ====================

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean addTagToCategory(Long categoryId, Long tagId) {
		// 检查分类和标签是否存在
		Category category = getById(categoryId);
		Tag tag = tagService.getById(tagId);
		if (category == null || tag == null) {
			return false;
		}

		// 检查是否已经关联
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getTagId, tagId);
		CategoryTag existing = categoryTagMapper.selectOne(queryWrapper);
		if (existing != null) {
			return true; // 已经关联，返回成功
		}

		// 创建关联
		CategoryTag categoryTag = new CategoryTag();
		categoryTag.setCategoryId(categoryId);
		categoryTag.setTagId(tagId);
		categoryTag.setSortOrder(0);

		return categoryTagMapper.insert(categoryTag) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeTagFromCategory(Long categoryId, Long tagId) {
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getTagId, tagId);
		return categoryTagMapper.delete(queryWrapper) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchAddTagsToCategory(Long categoryId, List<Long> tagIds) {
		if (CollUtil.isEmpty(tagIds)) {
			return true;
		}

		// 检查分类是否存在
		Category category = getById(categoryId);
		if (category == null) {
			return false;
		}

		// 获取已关联的标签ID
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId);
		List<CategoryTag> existingTags = categoryTagMapper.selectList(queryWrapper);
		List<Long> existingTagIds = existingTags.stream()
			.map(CategoryTag::getTagId)
			.collect(Collectors.toList());

		// 过滤出需要新增的标签ID
		List<Long> newTagIds = tagIds.stream()
			.filter(tagId -> !existingTagIds.contains(tagId))
			.collect(Collectors.toList());

		if (CollUtil.isEmpty(newTagIds)) {
			return true;
		}

		// 批量插入
		List<CategoryTag> categoryTags = new ArrayList<>();
		for (Long tagId : newTagIds) {
			CategoryTag categoryTag = new CategoryTag();
			categoryTag.setCategoryId(categoryId);
			categoryTag.setTagId(tagId);
			categoryTag.setSortOrder(0);
			categoryTags.add(categoryTag);
		}

		return categoryTagMapper.insertBatch(categoryTags) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateCategoryTagsSort(Long categoryId, List<org.springblade.business.post.controller.CategoryController.TagSortDTO> tagSorts) {
		if (CollUtil.isEmpty(tagSorts)) {
			return true;
		}

		for (org.springblade.business.post.controller.CategoryController.TagSortDTO tagSort : tagSorts) {
			LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(CategoryTag::getCategoryId, categoryId)
				.eq(CategoryTag::getTagId, tagSort.getTagId());
			CategoryTag categoryTag = categoryTagMapper.selectOne(queryWrapper);
			if (categoryTag != null) {
				categoryTag.setSortOrder(tagSort.getSortOrder());
				categoryTagMapper.updateById(categoryTag);
			}
		}

		return true;
	}

	@Override
	public Integer getCategoryTagCount(Long categoryId) {
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId);
		return Math.toIntExact(categoryTagMapper.selectCount(queryWrapper));
	}

	@Override
	public Boolean isTagBelongToCategory(Long categoryId, Long tagId) {
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getTagId, tagId);
		return categoryTagMapper.selectCount(queryWrapper) > 0;
	}

	@Override
	public List<Tag> getFeedbackTagsByCategory(Long categoryId) {
		// type=2为反馈标签
		return tagService.getTagsByCategoryAndType(categoryId, 2);
	}

	@Override
	public List<Tag> getAvailableFeedbackTags() {
		return tagService.getTagsByType(2);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean addFeedbackTagToCategory(Long categoryId, Long tagId) {
		Category category = getById(categoryId);
		Tag tag = tagService.getById(tagId);
		if (category == null || tag == null || tag.getType() != 2) {
			return false;
		}
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getTagId, tagId)
			.eq(CategoryTag::getType, 2);
		CategoryTag existing = categoryTagMapper.selectOne(queryWrapper);
		if (existing != null) {
			return true;
		}
		CategoryTag categoryTag = new CategoryTag();
		categoryTag.setCategoryId(categoryId);
		categoryTag.setTagId(tagId);
		categoryTag.setSortOrder(0);
		categoryTag.setType(2);
		return categoryTagMapper.insert(categoryTag) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeFeedbackTagFromCategory(Long categoryId, Long tagId) {
		LambdaQueryWrapper<CategoryTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CategoryTag::getCategoryId, categoryId)
			.eq(CategoryTag::getTagId, tagId)
			.eq(CategoryTag::getType, 2);
		return categoryTagMapper.delete(queryWrapper) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Tag createFeedbackTag(AdminTagCreateRequest tagReq) {
		tagReq.setType(2);
		return tagService.createTag(tagReq);
	}

	@Override
	@Caching(
		evict = {
			// 删除分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_ALL, allEntries = true),
			// 删除启动的分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_LIST, allEntries = true)
		}
	)
	public boolean createCategory(Category category) {
		return this.save(category);
	}

	@Override
	@Caching(
		evict = {
			// 删除分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_ALL, allEntries = true),
			// 删除启动的分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_LIST, allEntries = true)
		}
	)
	public boolean updateByCategoryId(Category category) {
		return this.updateById(category);
	}

	@Override
	@Caching(
		evict = {
			// 删除分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_ALL, allEntries = true),
			// 删除启动的分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_LIST, allEntries = true)
		}
	)
	public boolean saveOrUpdateCategory(Category category) {
		return this.saveOrUpdate(category);
	}

	@Override
	@Caching(
		evict = {
			// 删除分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_ALL, allEntries = true),
			// 删除启动的分类列表缓存
			@CacheEvict(cacheNames = WECHAT_CATEGORY_LIST, allEntries = true)
		}
	)
	public boolean deleteLogicCategory(String ids) {
		return this.deleteLogic(Func.toLongList(ids));
	}

	/**
	 * 构建分类树形结构
	 */
	private List<Category> buildCategoryTree(List<Category> allCategories, Long parentId) {
		List<Category> tree = new ArrayList<>();

		for (Category category : allCategories) {
			if (parentId.equals(category.getParentId())) {
				// 递归构建子分类
				List<Category> children = buildCategoryTree(allCategories, category.getId());
				category.setChildren(children);

				// 加载标签
				List<Tag> tags = tagService.getTagsByCategory(category.getId());
				category.setTags(tags);

				tree.add(category);
			}
		}

		return tree;
	}
}
