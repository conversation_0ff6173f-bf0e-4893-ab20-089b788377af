/**
 * Copyright (c) 2018-2099, <PERSON>ll <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.report.entity.Report;
import org.springblade.business.report.vo.ReportVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

/**
 * 举报记录 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface IReportService extends BaseService<Report> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param report
	 * @return
	 */
	IPage<ReportVO> selectReportPage(IPage<ReportVO> page, ReportVO report);


	Report getReport(Report report);

	/**
	 * 给用户增加积分
	 *
	 * @param userId 用户ID
	 * @param points 积分数量
	 * @param reason 积分原因
	 */
	void addUserPoints(Long userId, Integer points, String reason);
}
