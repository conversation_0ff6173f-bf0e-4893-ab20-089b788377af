/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.request;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 百事通信息贴DTO
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
//@TableName("urb_post")
//@EqualsAndHashCode(callSuper = true)
@Schema(description = "百事通信息贴")
public class PostSaveOrUpdateRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	@Schema(description = "ID")
	private Long id;

	/**
	 * 标题
	 */
	@Schema(description = "标题")
	private String title;

	/**
	 * 内容
	 */
	@Schema(description = "内容")
	private String content;

	/**
	 * 图片
	 */
	@Schema(description = "图片")
	private String images;



	/**
	 * 标签
	 */

	@Schema(description = "标签")
	private String[] tags;



	/**
	 * 发布状态
	 */
	@Schema(description = "发布状态")
	private String publishStatus;

	/**
	 * 时效状态
	 */
	@Schema(description = "时效状态")
	private String timeStatus;



}
