/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 充值配置
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "recharge")
public class RechargeConfig {

    /**
     * 最小充值金额（单位：元）
     */
    private BigDecimal minAmount = new BigDecimal("1.00");

    /**
     * 最大充值金额（单位：元）
     */
    private BigDecimal maxAmount = new BigDecimal("10000.00");

    /**
     * 建议充值金额列表（单位：元）
     */
    private List<BigDecimal> suggestedAmounts = Arrays.asList(
        new BigDecimal("10.00"),
        new BigDecimal("50.00"),
        new BigDecimal("100.00"),
        new BigDecimal("200.00"),
        new BigDecimal("500.00")
    );

    /**
     * 是否启用充值功能
     */
    private Boolean enabled = true;

    /**
     * 充值描述模板
     */
    private String descriptionTemplate = "钱包充值 {amount} 元";

    /**
     * 订单过期时间（分钟）
     */
    private Integer orderExpireMinutes = 30;

    /**
     * 验证充值金额是否有效
     *
     * @param amount 充值金额
     * @return 是否有效
     */
    public boolean isValidAmount(BigDecimal amount) {
        if (amount == null) {
            return false;
        }

        // 检查金额范围
        if (amount.compareTo(minAmount) < 0 || amount.compareTo(maxAmount) > 0) {
            return false;
        }

        // 检查小数位数（最多2位小数）
        if (amount.scale() > 2) {
            return false;
        }

        return true;
    }

    /**
     * 格式化充值描述
     *
     * @param amount 充值金额
     * @return 格式化后的描述
     */
    public String formatDescription(BigDecimal amount) {
        return descriptionTemplate.replace("{amount}", amount.toString());
    }
}
