/**
 * Copyright (c) 2018-2099, Chi<PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.business.institution.entity.InstitutionAuditLog;

import java.io.Serial;
import java.io.Serializable;

/**
 * 机构审核日志表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class InstitutionAuditLogDTO implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	@Schema(description = "机构ID集合")
	private String id;

	@Schema(description = "审核状态")
	private String auditStatus; // 1-通过，2-拒绝

	@Schema(description = "审核备注")
	private String auditRemark;

}
