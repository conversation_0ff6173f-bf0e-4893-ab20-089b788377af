<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.post_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-check"
                   plain
                   @click="handleBatchAudit">批量审核
        </el-button>
        <el-button type="warning"
                   icon="el-icon-download"
                   plain
                   @click="handleBatchOffline">批量下架
        </el-button>
      </template>
      <template #content="{ row }">
        <el-button type="text" @click="viewContent(row)">
          {{ row.content ? (row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content) : '查看内容' }}
        </el-button>
      </template>

      <!-- 浏览量插槽 -->
      <template #viewCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'view')"
          :disabled="!getViewCount(row)">
          {{ getViewCount(row) }}
        </el-button>
      </template>

      <!-- 点赞数插槽 -->
      <template #likeCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'like')"
          :disabled="!getLikeCount(row)">
          {{ getLikeCount(row) }}
        </el-button>
      </template>

      <!-- 反馈数插槽 -->
      <template #feedbackCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'feedback')"
          :disabled="!getFeedbackCount(row)">
          {{ getFeedbackCount(row) }}
        </el-button>
      </template>

      <!-- 收藏数插槽 -->
      <template #favoriteCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'favorite')"
          :disabled="!getFavoriteCount(row)">
          {{ getFavoriteCount(row) }}
        </el-button>
      </template>
      <template #images="{ row }">
        <el-image 
          v-if="row.images && getImageList(row.images).length > 0"
          :src="getImageList(row.images)[0]"
          style="width: 50px; height: 50px;"
          :preview-src-list="getImageList(row.images)">
        </el-image>
        <span v-else style="color: #999;">无图片</span>
      </template>
      <template #tags="{ row }">
        <el-tag v-for="tag in row.tags" :key="tag" type="info" style="margin-right: 5px;">
          {{ tag }}
        </el-tag>
      </template>
      <template #createUser="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.createUser)"
          style="color: #409EFF; text-decoration: underline;">
          {{ getUserNickname(row) }}
        </el-button>
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.auditStatus === '0'"
                   type="success"
                   size="small"
                   @click="handleAudit(row, '1')">
          通过
        </el-button>
        <el-button v-if="row.auditStatus === '0'"
                   type="danger"
                   size="small"
                   @click="handleAudit(row, '2')">
          拒绝
        </el-button>
        <el-button v-if="row.publishStatus == '1'"
                   type="warning"
                   size="small"
                   @click="handleStatusChange(row, '2')">
          下架
        </el-button>
        <el-button v-if="row.publishStatus == '2' || !row.publishStatus"
                   type="primary"
                   size="small"
                   @click="handleStatusChange(row, '1')">
          上架
        </el-button>
      </template>
    </avue-crud>

    <!-- 内容详情对话框 -->
    <el-dialog v-model="contentDialogVisible" title="帖子内容详情" width="600px">
      <div v-if="selectedPost">
        <el-descriptions :column="2" border>
          <!-- <el-descriptions-item label="标题">{{ selectedPost.title }}</el-descriptions-item> -->
          <el-descriptions-item label="分类">{{ selectedPost.category && selectedPost.category.name ? selectedPost.category.name : '未分类' }}</el-descriptions-item>
          <el-descriptions-item label="发布者">{{ selectedPost.userCreater }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ selectedPost.publishTime }}</el-descriptions-item>
          <el-descriptions-item label="发布状态">{{ getStatusText(selectedPost.publishStatus) }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">{{ getAuditStatusText(selectedPost.auditStatus) }}</el-descriptions-item>
        </el-descriptions>
        <el-divider />
        <div>
          <h4>内容：</h4>
          <p style="white-space: pre-wrap;">{{ selectedPost.content }}</p>
        </div>
        <div v-if="selectedPost.images && getImageList(selectedPost.images).length > 0">
          <h4>图片：</h4>
          <el-image 
            v-for="(img, index) in getImageList(selectedPost.images)" 
            :key="index"
            :src="img"
            style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
            :preview-src-list="getImageList(selectedPost.images)">
          </el-image>
        </div>
        <div v-if="selectedPost.tags && selectedPost.tags.length > 0">
          <h4>标签：</h4>
          <el-tag v-for="tag in selectedPost.tags" :key="tag" style="margin-right: 5px;">
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="帖子审核" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditRemark" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入审核备注（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model="userDetailVisible"
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />

    <!-- 数据记录查看对话框 -->
    <DataRecordDialog
      v-if="currentRecordType && dataRecordApiConfig[currentRecordType]"
      v-model="dataRecordDialogVisible"
      :record-type="currentRecordType"
      :relevancy-id="currentRelevancyId"
      :relevancy-type="'0'"
      :api-config="dataRecordApiConfig[currentRecordType]" />
  </basic-container>
</template>

<script>
  import {getPage, getDetail, add, update, remove, updatePublishStatus, batchOfflinePosts} from "@/api/ad/post";
  import {batchAudit} from "@/api/ad/auditpost";
  import {getUserDict} from "@/api/ad/user";
  import {dicList as getCategoryDict} from "@/api/ad/category";
  import {mapGetters} from "vuex";
  import { baseUrl } from '@/config/env';
  import UserDetailDialog from '@/components/UserDetailDialog.vue';
  import DataRecordDialog from '@/components/DataRecordDialog.vue';
  import { dataRecordApiConfig } from '@/api/ad/datarecord';

  export default {
    components: {
      UserDetailDialog,
      DataRecordDialog
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        contentDialogVisible: false,
        selectedPost: null,
        auditDialogVisible: false,
        auditForm: {
          postIds: null,
          auditStatus: '1',
          auditRemark: ''
        },
        userDetailVisible: false,
        selectedUserId: null,
        // 数据记录对话框相关
        dataRecordDialogVisible: false,
        currentRecordType: '',
        currentRelevancyId: null,
        dataRecordApiConfig: dataRecordApiConfig,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          selection: true,
          editBtn: false,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "内容",
              prop: "content",
              type: "textarea",
              width: '240px',
              span: 24,
              ellipsis : true,
              slot: true
            },
            {
              label: "分类",
              prop: "categoryId",
              type: "select",
              dicData: [],
              props: {
                label: "name",
                value: "id"
              },
              search: true,
              hide: false
            },
            {
              label: "发布者",
              prop: "createUser",
              type: "select",
              dicData: [],
              props: {
                label: "nickname",
                value: "id"
              },
              search: true,
              slot: true
            },
            {
              label: "发布状态",
              prop: "publishStatus",
              type: "select",
              dataType: 'number',
              dicUrl: baseUrl + "/blade-system/dict/dictionary?code=common_publish_status",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
            },
            {
              label: "审核状态",
              prop: "auditStatus",
              type: "select",
              dicData: [
                { label: '待审核', value: '0' },
                { label: '已通过', value: '1' },
                { label: '已拒绝', value: '2' }
              ],
              search: true,
              slot: true
            },
            {
              label: "图片",
              prop: "images",
              slot: true,
              search: false
            },
            {
              label: "标签",
              prop: "tags",
              width: 200,
              slot: true,
              search: false
            },
            {
              label: "浏览量",
              prop: "viewCount",
              type: "number",
              sortable: true,
              slot: true
            },
            {
              label: "点赞数",
              prop: "likeCount",
              type: "number",
              sortable: true,
              slot: true
            },
            {
              label: "反馈数",
              prop: "feedbackCount",
              type: "number",
              sortable: true,
              slot: true
            },
            {
              label: "收藏数",
              prop: "favoriteCount",
              type: "number",
              sortable: true,
              slot: true
            },
       
            {
              label: "发布时间",
              prop: "publishTime",
              type: 'datetime',
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              search: true,
              searchSpan: 12,
              searchRange: true,
              sortable: true
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              sortable: true,
              hide: true
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.post_add, false),
          viewBtn: this.validData(this.permission.post_view, false),
          delBtn: this.validData(this.permission.post_delete, false),
          editBtn: this.validData(this.permission.post_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      getImageList(images) {
        if (!images) return [];
        if (Array.isArray(images)) return images;
        return images.split(',').filter(img => img.trim() !== '');
      },
      // 获取统计数据
      getStatValue(row, field) {
        return row.stats && row.stats[field] !== undefined ? row.stats[field] : 0;
      },
      // 获取分类名称
      getCategoryName(row) {
        return row.category && row.category.name ? row.category.name : '未分类';
      },
      // 获取发布者名称
      getPublisherName(row) {
        return row.user && row.user.nickname ? row.user.nickname : (row.contactName || '匿名用户');
      },
      getAuditStatusType(status) {
        const statusMap = {
          '0': 'warning',
          '1': 'success',
          '2': 'danger'
        };
        return statusMap[status] || 'info';
      },
      getAuditStatusText(status) {
        const statusMap = {
          '0': '待审核',
          '1': '已通过',
          '2': '已拒绝'
        };
        return statusMap[status] || '未知';
      },
      viewContent(row) {
        this.selectedPost = row;
        this.contentDialogVisible = true;
      },
      // 获取浏览量
      getViewCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.viewCount !== undefined) {
          return row.stats.viewCount;
        }
        if (row.viewCount !== undefined) {
          return row.viewCount;
        }
        return 0;
      },
      // 获取点赞数
      getLikeCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.likeCount !== undefined) {
          return row.stats.likeCount;
        }
        if (row.likeCount !== undefined) {
          return row.likeCount;
        }
        return 0;
      },
      // 获取反馈数
      getFeedbackCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.feedbackCount !== undefined) {
          return row.stats.feedbackCount;
        }
        if (row.feedbackCount !== undefined) {
          return row.feedbackCount;
        }
        return 0;
      },
      // 获取收藏数
      getFavoriteCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.favoriteCount !== undefined) {
          return row.stats.favoriteCount;
        }
        if (row.favoriteCount !== undefined) {
          return row.favoriteCount;
        }
        return 0;
      },
      // 显示数据记录对话框
      showDataRecord(row, recordType) {
        if (!row.id) {
          this.$message.warning('帖子信息不存在');
          return;
        }

        // 检查对应的数量是否大于0
        let count = 0;
        if (recordType === 'view') {
          count = this.getViewCount(row);
        } else if (recordType === 'like') {
          count = this.getLikeCount(row);
        } else if (recordType === 'feedback') {
          count = this.getFeedbackCount(row);
        } else if (recordType === 'favorite') {
          count = this.getFavoriteCount(row);
        }

        if (count === 0) {
          this.$message.info('暂无相关记录');
          return;
        }

        this.currentRecordType = recordType;
        this.currentRelevancyId = row.id;
        this.dataRecordDialogVisible = true;
      },
      // 获取用户昵称
      getUserNickname(row) {
        // 从用户字典中查找昵称
        const userCol = this.option.column.find(c => c.prop === 'createUser');
        if (userCol && userCol.dicData) {
          const user = userCol.dicData.find(u => u.id === row.createUser);
          return user ? user.nickname : '未知用户';
        }
        return '未知用户';
      },
      // 显示用户详情
      showUserDetail(userId) {
        if (!userId) {
          this.$message.warning('用户信息不存在');
          return;
        }
        this.selectedUserId = userId;
        this.userDetailVisible = true;
      },
      // 处理管理用户事件
      handleManageUser(userDetail) {
        this.$message.info(`管理用户: ${userDetail.nickname}`);
        // 这里可以跳转到用户管理页面或执行其他管理操作
        // this.$router.push(`/admin/user/detail/${userDetail.id}`);
      },
              handleAudit(row, status) {
          this.auditForm.postIds = row.id;
          this.auditForm.auditStatus = status;
          this.auditForm.auditRemark = '';
          this.auditDialogVisible = true;
        },
        handleBatchAudit() {
          if (this.selectionList.length === 0) {
            this.$message.warning("请选择至少一条数据");
            return;
          }
          this.auditForm.postIds = this.ids;
          this.auditForm.auditStatus = '1';
          this.auditForm.auditRemark = '';
          this.auditDialogVisible = true;
        },
        submitAudit() {
            batchAudit(this.auditForm).then(() => {
              this.auditDialogVisible = false;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "审核操作成功!"
              });
            }).catch(error => {
              this.$message.error("审核操作失败");
            });
        },
        // 处理状态变更（上架/下架）
        handleStatusChange(row, publishStatus) {
          const action = publishStatus == '2' ? '上架' : '下架';
          this.$confirm(`确定要${action}这条帖子吗？`, {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            updatePublishStatus(row.id, publishStatus).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: `${action}操作成功!`
              });
            }).catch(() => {
              this.$message.error(`${action}操作失败`);
            });
          });
        },
        // 批量下架
        handleBatchOffline() {
          if (this.selectionList.length === 0) {
            this.$message.warning("请选择至少一条数据");
            return;
          }
          this.$confirm("确定要下架选中的帖子吗？", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            const postIds = this.selectionList.map(item => item.id);
            batchOfflinePosts(postIds).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "批量下架操作成功!"
              });
              this.$refs.crud.toggleSelection();
            }).catch(() => {
              this.$message.error("批量下架操作失败");
            });
          });
        },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        // 动态加载用户字典
        getUserDict().then(res => {
          const users = res.data.data || [];
          const userCol = this.option.column.find(c => c.prop === 'createUser');
          if (userCol) userCol.dicData = users;
        }).catch(error => {
          console.error('加载用户字典失败:', error);
        });

        // 动态加载分类字典
        getCategoryDict().then(res => {
          const categories = res.data.data || [];
          const categoryCol = this.option.column.find(c => c.prop === 'categoryId');
          if (categoryCol) categoryCol.dicData = categories;
        }).catch(error => {
          console.error('加载分类字典失败:', error);
        });

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getPage(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
