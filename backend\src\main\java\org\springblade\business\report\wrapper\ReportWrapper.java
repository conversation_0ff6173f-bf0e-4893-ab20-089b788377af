/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.wrapper;

import org.springblade.business.report.entity.Report;
import org.springblade.business.report.vo.ReportVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 举报记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public class ReportWrapper extends BaseEntityWrapper<Report, ReportVO>  {

    public static ReportWrapper build() {
        return new ReportWrapper();
    }

	@Override
	public ReportVO entityVO(Report report) {
		ReportVO reportVO = BeanUtil.copyProperties(report, ReportVO.class);

		return reportVO;
	}

}
