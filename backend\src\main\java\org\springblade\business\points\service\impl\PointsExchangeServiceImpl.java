/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.service.impl;

import org.springblade.business.points.entity.PointsExchange;
import org.springblade.business.points.mapper.PointsExchangeMapper;
import org.springblade.business.points.service.IPointsExchangeService;
import org.springblade.business.points.vo.PointsExchangeVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 积分兑换记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class PointsExchangeServiceImpl extends BaseServiceImpl<PointsExchangeMapper, PointsExchange> implements IPointsExchangeService {



	@Override
	public IPage<PointsExchangeVO> selectPointsExchangePage(IPage<PointsExchangeVO> page, PointsExchangeVO pointsExchange) {
		//登录用户id
		pointsExchange.setCreateUser(AuthUtil.getUserId());
		return page.setRecords(baseMapper.selectPointsExchangePage(page, pointsExchange));
	}

	@Override
	public PointsExchange getPointsPointsExchangeDetail(PointsExchange pointsExchange) {
		pointsExchange.setCreateUser(AuthUtil.getUserId());
		PointsExchange detail = baseMapper.getPointsPointsExchangeDetail(pointsExchange);
		return detail;
	}

}
