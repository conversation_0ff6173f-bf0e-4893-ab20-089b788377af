/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 积分商城商品表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_points_goods")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "积分商城商品表")
public class PointsGoods extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String goodsName;
    /**
     * 商品描述
     */
    @Schema(description = "商品描述")
    private String goodsDesc;
    /**
     * 商品图片
     */
    @Schema(description = "商品图片")
    private String goodsImage;
    /**
     * 积分价格
     */
    @Schema(description = "积分价格")
    private Integer pointsPrice;
    /**
     * 原价
     */
    @Schema(description = "原价")
    private BigDecimal originalPrice;
    /**
     * 库存数量
     */
    @Schema(description = "库存数量")
    private Integer stock;
    /**
     * 兑换限制（0：无限制）
     */
    @Schema(description = "兑换限制（0：无限制）")
    private Integer exchangeLimit;
    /**
     * 商品分类
     */
    @Schema(description = "商品分类")
    private String category;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;


}
