/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.user.entity.RechargeOrder;
import org.springblade.business.user.service.IRechargeService;
import org.springblade.business.user.vo.RechargeOrderVO;
import org.springblade.business.user.wrapper.RechargeOrderWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 充值订单 控制器
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/rechargeorder")
@io.swagger.v3.oas.annotations.tags.Tag(name = "充值订单", description = "充值订单接口")
public class RechargeOrderController extends BladeController {

	private IRechargeService rechargeOrderService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入rechargeOrder")
	public R<RechargeOrderVO> detail(RechargeOrder rechargeOrder) {
		RechargeOrder detail = rechargeOrderService.getOne(Condition.getQueryWrapper(rechargeOrder));
		return R.data(RechargeOrderWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 充值订单
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入rechargeOrder")
	public R<IPage<RechargeOrderVO>> list(RechargeOrder rechargeOrder, Query query) {
		IPage<RechargeOrder> pages = rechargeOrderService.page(Condition.getPage(query), Condition.getQueryWrapper(rechargeOrder));
		return R.data(RechargeOrderWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 充值订单
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入rechargeOrder")
	public R<IPage<RechargeOrderVO>> page(RechargeOrderVO rechargeOrder, Query query) {
		IPage<RechargeOrderVO> pages = rechargeOrderService.selectRechargeOrderPage(Condition.getPage(query), rechargeOrder);
		return R.data(pages);
	}

	/**
	 * 新增 充值订单
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入rechargeOrder")
	public R save(@Valid @RequestBody RechargeOrder rechargeOrder) {
		return R.status(rechargeOrderService.save(rechargeOrder));
	}

	/**
	 * 修改 充值订单
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入rechargeOrder")
	public R update(@Valid @RequestBody RechargeOrder rechargeOrder) {
		return R.status(rechargeOrderService.updateById(rechargeOrder));
	}

	/**
	 * 新增或修改 充值订单
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入rechargeOrder")
	public R submit(@Valid @RequestBody RechargeOrder rechargeOrder) {
		return R.status(rechargeOrderService.saveOrUpdate(rechargeOrder));
	}


	/**
	 * 删除 充值订单
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(rechargeOrderService.deleteLogic(Func.toLongList(ids)));
	}


}
