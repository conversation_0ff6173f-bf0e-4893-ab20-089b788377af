# 名片库API测试文档

## 概述
本文档描述了名片库功能使用通用数据操作接口（type=2）的API测试方法。

## 数据类型说明
- `type = "0"` 或 `"post"` - 帖子
- `type = "1"` 或 `"institution"` - 机构  
- `type = "2"` 或 `"card"` - 名片
- `type = "3"` 或 `"comment"` - 评论

## API接口测试

### 1. 点赞名片
**接口**: `POST /blade-chat/data-operate/toggle-like/2/{cardId}`

**请求示例**:
```bash
curl -X POST "http://localhost/blade-chat/data-operate/toggle-like/2/1" \
  -H "Authorization: Bearer {token}"
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "currentState": true,
    "action": "like",
    "message": "点赞成功",
    "targetId": 1,
    "type": "2"
  }
}
```

### 2. 收藏名片
**接口**: `POST /blade-chat/data-operate/toggle-favorite/2/{cardId}`

**请求示例**:
```bash
curl -X POST "http://localhost/blade-chat/data-operate/toggle-favorite/2/1" \
  -H "Authorization: Bearer {token}"
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "currentState": true,
    "action": "favorite",
    "message": "收藏成功",
    "targetId": 1,
    "type": "2"
  }
}
```

### 3. 保存收藏扩展信息
**接口**: `POST /blade-chat/card/favorite/{cardId}`

**请求示例**:
```bash
curl -X POST "http://localhost/blade-chat/card/favorite/1" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "category": "合作伙伴",
    "remark": "潜在合作对象"
  }'
```

### 4. 检查点赞状态
**接口**: `GET /blade-chat/data-operate/check-like/2/{cardId}`

**请求示例**:
```bash
curl -X GET "http://localhost/blade-chat/data-operate/check-like/2/1" \
  -H "Authorization: Bearer {token}"
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "isLiked": true
  }
}
```

### 5. 检查收藏状态
**接口**: `GET /blade-chat/data-operate/check-favorite/2/{cardId}`

**请求示例**:
```bash
curl -X GET "http://localhost/blade-chat/data-operate/check-favorite/2/1" \
  -H "Authorization: Bearer {token}"
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "isFavorited": true
  }
}
```

### 6. 获取公开名片列表
**接口**: `GET /blade-chat/card/public/list`

**请求参数**:
- `current`: 页码（默认1）
- `size`: 每页数量（默认10）
- `keyword`: 搜索关键词（可选）
- `latitude`: 纬度（可选，用于距离计算）
- `longitude`: 经度（可选，用于距离计算）
- `scope`: 距离范围（可选，单位：公里）

**请求示例**:
```bash
curl -X GET "http://localhost/blade-chat/card/public/list?current=1&size=10&latitude=39.9042&longitude=116.4074&scope=10" \
  -H "Authorization: Bearer {token}"
```

### 7. 获取我的收藏名片列表
**接口**: `GET /blade-chat/card/favorite/list`

**请求参数**:
- `current`: 页码（默认1）
- `size`: 每页数量（默认10）
- `category`: 收藏分类（可选）
- `remark`: 备注关键词（可选）

**请求示例**:
```bash
curl -X GET "http://localhost/blade-chat/card/favorite/list?current=1&size=10&category=合作伙伴" \
  -H "Authorization: Bearer {token}"
```

## 数据库表结构

### 通用点赞表 (urb_like)
```sql
-- 新增字段
ALTER TABLE urb_like ADD COLUMN relevancy_id bigint DEFAULT NULL COMMENT '关联ID（通用）';
ALTER TABLE urb_like ADD COLUMN type varchar(20) DEFAULT '0' COMMENT '数据类型：0-帖子，1-机构，2-名片';
```

### 通用收藏表 (urb_favorite)
```sql
-- 新增字段
ALTER TABLE urb_favorite ADD COLUMN relevancy_id bigint DEFAULT NULL COMMENT '关联ID（通用）';
ALTER TABLE urb_favorite ADD COLUMN type varchar(20) DEFAULT '0' COMMENT '数据类型：0-帖子，1-机构，2-名片';
```

### 名片收藏扩展表 (urb_business_card_favorite_ext)
```sql
CREATE TABLE urb_business_card_favorite_ext (
  id bigint NOT NULL AUTO_INCREMENT,
  card_id bigint NOT NULL COMMENT '名片ID',
  user_id bigint NOT NULL COMMENT '收藏用户ID',
  card_snapshot longtext COMMENT '名片快照数据（JSON格式）',
  category varchar(50) DEFAULT NULL COMMENT '收藏分类',
  remark varchar(500) DEFAULT NULL COMMENT '收藏备注',
  create_time datetime(6) DEFAULT NULL,
  create_user bigint DEFAULT NULL,
  update_time datetime(6) DEFAULT NULL,
  update_user bigint DEFAULT NULL,
  is_deleted int DEFAULT '0',
  PRIMARY KEY (id),
  UNIQUE KEY uk_card_user_ext (card_id, user_id)
);
```

## 测试流程

1. **初始化数据**: 创建测试名片数据
2. **点赞测试**: 测试点赞/取消点赞功能
3. **收藏测试**: 测试收藏/取消收藏功能，包括扩展信息保存
4. **状态检查**: 验证点赞和收藏状态的正确性
5. **列表查询**: 测试公开名片列表和收藏名片列表
6. **数据一致性**: 验证统计数据的准确性

## 注意事项

1. 所有名片相关操作使用 `type="2"`
2. 收藏操作分为两步：通用收藏 + 扩展信息保存
3. 取消收藏时会同时删除扩展信息
4. 名片快照功能确保原名片删除后仍可查看收藏的内容
5. 支持地理位置查询和距离计算
6. 支持分类收藏和备注功能
