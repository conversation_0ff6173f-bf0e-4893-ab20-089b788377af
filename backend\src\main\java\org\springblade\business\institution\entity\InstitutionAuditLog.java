/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 机构审核日志表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@TableName("urb_institution_audit_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "机构审核日志表")
public class InstitutionAuditLog extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

  private Long institutionId;
  private Long auditUserId;
  private LocalDateTime auditTime;
  private String auditStatus;
  private String auditRemark;
    /**
     * 操作类型（申请/审核/修改/禁用等）
     */
    @Schema(description = "操作类型（申请/审核/修改/禁用等）")
    private String action;


}
