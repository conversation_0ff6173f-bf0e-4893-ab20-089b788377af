-- 邀请码状态字段迁移脚本
-- 将 status 字段重命名为 invite_status 以避免与 BaseEntity 的 status 字段冲突

-- 1. 添加新的 invite_status 字段
ALTER TABLE `urb_invite_code` 
ADD COLUMN `invite_status` varchar(20) DEFAULT 'active' COMMENT '邀请状态：active-有效，inactive-无效，expired-过期' 
AFTER `reward_points`;

-- 2. 复制现有 status 字段的数据到 invite_status 字段
UPDATE `urb_invite_code` 
SET `invite_status` = COALESCE(`status`, 'active') 
WHERE `invite_status` IS NULL OR `invite_status` = '';

-- 3. 删除旧的 status 字段（如果存在且不是 BaseEntity 的字段）
-- 注意：如果 status 字段是 BaseEntity 的字段，则不要删除
-- ALTER TABLE `urb_invite_code` DROP COLUMN `status`;

-- 4. 添加新字段的索引
ALTER TABLE `urb_invite_code` 
ADD INDEX `idx_invite_status` (`invite_status`);

-- 5. 删除旧的 status 索引（如果存在）
-- ALTER TABLE `urb_invite_code` DROP INDEX `idx_status`;

-- 6. 更新存储过程中的字段引用
DROP PROCEDURE IF EXISTS `sp_cleanup_expired_invite_codes`;

DELIMITER //
CREATE PROCEDURE `sp_cleanup_expired_invite_codes`()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE code_id BIGINT;
  DECLARE cur CURSOR FOR 
    SELECT id FROM urb_invite_code 
    WHERE invite_status = 'active' 
    AND expire_time IS NOT NULL 
    AND expire_time <= NOW()
    AND is_deleted = 0;
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

  START TRANSACTION;
  
  OPEN cur;
  read_loop: LOOP
    FETCH cur INTO code_id;
    IF done THEN
      LEAVE read_loop;
    END IF;
    
    UPDATE urb_invite_code 
    SET invite_status = 'expired', update_time = NOW() 
    WHERE id = code_id;
  END LOOP;
  CLOSE cur;
  
  COMMIT;
  
  SELECT ROW_COUNT() as updated_count;
END //
DELIMITER ;

-- 7. 更新统计视图
DROP VIEW IF EXISTS `v_invite_stats`;

CREATE VIEW `v_invite_stats` AS
SELECT 
  ic.inviter_user_id,
  COUNT(ic.id) as total_codes,
  SUM(ic.used_count) as total_uses,
  SUM(ic.registered_count) as total_registered,
  AVG(CASE WHEN ic.used_count > 0 THEN ic.registered_count * 100.0 / ic.used_count ELSE 0 END) as avg_conversion_rate,
  SUM(CASE WHEN ic.invite_status = 'active' THEN 1 ELSE 0 END) as active_codes,
  MAX(ic.create_time) as last_code_time
FROM urb_invite_code ic
WHERE ic.is_deleted = 0
GROUP BY ic.inviter_user_id;

-- 8. 验证数据迁移
SELECT 
  '数据迁移验证' as check_type,
  COUNT(*) as total_records,
  COUNT(CASE WHEN invite_status IS NOT NULL THEN 1 END) as records_with_invite_status,
  COUNT(CASE WHEN invite_status = 'active' THEN 1 END) as active_records,
  COUNT(CASE WHEN invite_status = 'inactive' THEN 1 END) as inactive_records,
  COUNT(CASE WHEN invite_status = 'expired' THEN 1 END) as expired_records
FROM urb_invite_code 
WHERE is_deleted = 0;

-- 9. 显示迁移完成信息
SELECT 
  '迁移完成' as status,
  '已将 status 字段数据迁移到 invite_status 字段' as message,
  NOW() as completion_time;
