/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (bladeja<PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.business.user.entity.UserBalanceLog;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用户余额变动记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface UserBalanceLogMapper extends BaseMapper<UserBalanceLog> {

    /**
     * 分页查询用户余额变动记录
     *
     * @param page   分页对象
     * @param userId 用户ID
     * @return 余额变动记录列表
     */
    IPage<UserBalanceLog> selectUserBalanceLogPage(IPage<UserBalanceLog> page, @Param("userId") Long userId);

    /**
     * 查询用户余额变动统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserBalanceStats(@Param("userId") Long userId);

    /**
     * 查询用户指定类型的余额变动记录
     *
     * @param userId     用户ID
     * @param changeType 变动类型
     * @param limit      限制条数
     * @return 余额变动记录列表
     */
    List<UserBalanceLog> selectUserBalanceLogByType(@Param("userId") Long userId,
                                                   @Param("changeType") String changeType,
                                                   @Param("limit") Integer limit);

    /**
     * 统计用户指定时间段内的余额变动
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    Map<String, Object> selectUserBalanceStatsByPeriod(@Param("userId") Long userId,
                                                       @Param("startTime") String startTime,
                                                       @Param("endTime") String endTime);
}
