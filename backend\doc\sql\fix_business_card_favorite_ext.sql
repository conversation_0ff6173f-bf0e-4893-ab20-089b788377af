-- 修复名片收藏扩展表结构
-- 添加缺失的字段以匹配BaseEntity

-- 检查表是否存在
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'urb_business_card_favorite_ext';

-- 添加缺失的字段
ALTER TABLE `urb_business_card_favorite_ext` 
ADD COLUMN `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID' AFTER `id`,
ADD COLUMN `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）' AFTER `update_user`;

-- 验证表结构
DESCRIBE `urb_business_card_favorite_ext`;

-- 如果需要重建表（可选，仅在上述ALTER失败时使用）
/*
DROP TABLE IF EXISTS `urb_business_card_favorite_ext_backup`;
RENAME TABLE `urb_business_card_favorite_ext` TO `urb_business_card_favorite_ext_backup`;

CREATE TABLE `urb_business_card_favorite_ext` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `card_id` bigint NOT NULL COMMENT '名片ID',
  `user_id` bigint NOT NULL COMMENT '收藏用户ID',
  `card_snapshot` longtext COMMENT '名片快照数据（JSON格式）',
  `category` varchar(50) DEFAULT NULL COMMENT '收藏分类',
  `remark` varchar(500) DEFAULT NULL COMMENT '收藏备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_user_ext` (`card_id`,`user_id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='名片收藏扩展信息表';

-- 迁移数据
INSERT INTO `urb_business_card_favorite_ext` 
(id, create_time, create_user, update_time, update_user, is_deleted, card_id, user_id, card_snapshot, category, remark)
SELECT id, create_time, create_user, update_time, update_user, is_deleted, card_id, user_id, card_snapshot, category, remark
FROM `urb_business_card_favorite_ext_backup`;

-- 删除备份表（确认数据正确后执行）
-- DROP TABLE `urb_business_card_favorite_ext_backup`;
*/
