<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.subscriptionPlan.mapper.SubscriptionPlanFeatureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="subscriptionPlanFeatureResultMap" type="org.springblade.business.subscriptionPlan.entity.SubscriptionPlanFeature">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="plan_id" property="planId"/>
        <result column="feature_id" property="featureId"/>
    </resultMap>


    <select id="selectSubscriptionPlanFeaturePage" resultMap="subscriptionPlanFeatureResultMap">
        select * from urb_subscription_plan_feature where is_deleted = 0
    </select>

</mapper>
