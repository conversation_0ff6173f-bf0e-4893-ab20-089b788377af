/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.mapper;

import org.apache.ibatis.annotations.Param;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.vo.InstitutionVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import java.util.Map;

/**
 * 机构主表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface InstitutionMapper extends BaseMapper<Institution> {

	List<InstitutionVO> selectInstitutionPage(IPage page,@Param("model") Map<String, Object> params);

	IPage<InstitutionVO> pageByUserId(IPage<Object> page, Long userId);

	Institution getInstutionById(Long id);

	Integer getLikeCount(Long id, Long userId);

	Boolean saveLikeLog(Long id, Long userId);

	Integer getFavoriteCount(Long id, Long userId);

	Boolean saveFavoriteLog(Long id, Long userId);

	Boolean saveViewLog(Long userId, Long id);
}
