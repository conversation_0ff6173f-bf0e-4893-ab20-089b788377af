<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.subscriptionPlan.mapper.SubscriptionPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="subscriptionPlanResultMap" type="org.springblade.business.subscriptionPlan.entity.SubscriptionPlan">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_description" property="planDescription"/>
        <result column="price" property="price"/>
        <result column="duration" property="duration"/>
        <result column="plan_type" property="planType"/>
        <result column="max_features" property="maxFeatures"/>
        <result column="is_popular" property="isPopular"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="icon" property="icon"/>

<!--        功能信息-->
        <association property="feature" javaType="org.springblade.business.subscriptionPlan.entity.Feature">
           <result column="feature_id" property="id"/>
            <result column="feature_name" property="featureName"/>
            <result column="feature_description" property="featureDescription"/>
            <result column="feature_type" property="featureType"/>
        </association>

    </resultMap>

<!--     批量查询防止N+1的问题。直接连表查询优化,多对多-->
    <select id="selectSubscriptionPlanPage" resultMap="subscriptionPlanResultMap">
        SELECT DISTINCT sp.*
        ,f.id AS feature_id, f.feature_name, f.feature_description, f.feature_type
        FROM urb_subscription_plan sp
        LEFT JOIN urb_subscription_plan_feature fsp ON sp.id = fsp.plan_id
        LEFT JOIN urb_feature f ON f.id = fsp.feature_id AND f.is_deleted = 0 AND f.status = 1
        WHERE sp.is_deleted = 0 AND sp.status = 1
        <if test="subscriptionPlan.planName != null and subscriptionPlan.planName != ''">
            AND sp.plan_name LIKE CONCAT('%', #{subscriptionPlan.planName}, '%')
        </if>
        <if test="subscriptionPlan.planType != null and subscriptionPlan.planType != ''">
            AND sp.plan_type = #{subscriptionPlan.planType}
        </if>
        ORDER BY sp.sort_order, sp.create_time DESC
    </select>
    <select id="selectSubscriptionPlanWithFeatures"
            resultMap="subscriptionPlanResultMap">
            SELECT sp.*, f.id AS feature_id, f.feature_name, f.feature_description, f.feature_type
            FROM urb_subscription_plan sp
            LEFT JOIN urb_subscription_plan_feature fsp ON sp.id = fsp.plan_id
            LEFT JOIN urb_feature f ON f.id = fsp.feature_id AND f.is_deleted = 0 AND f.status = 1
            WHERE sp.is_deleted = 0 AND sp.status = 1
             AND sp.id = #{id}
    </select>
    <select id="selectSubscriptionPlanPageByUser" resultMap="subscriptionPlanResultMap">
            SELECT sp.*, f.id AS feature_id, f.feature_name, f.feature_description, f.feature_type
            FROM urb_subscription_plan sp
            LEFT JOIN urb_subscription_plan_feature fsp ON sp.id = fsp.plan_id
            LEFT JOIN urb_feature f ON f.id = fsp.feature_id AND f.is_deleted = 0 AND f.status = 1
            WHERE sp.is_deleted = 0 AND sp.status = 1
             AND sp.id IN (
                SELECT plan_id FROM urb_user_subscription WHERE user_id = #{userId}
            )
    </select>

</mapper>
