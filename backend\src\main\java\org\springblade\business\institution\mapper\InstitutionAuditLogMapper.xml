<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.institution.mapper.InstitutionAuditLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="institutionAuditLogResultMap" type="org.springblade.business.institution.entity.InstitutionAuditLog">
        <result column="id" property="id"/>
        <result column="institution_id" property="institutionId"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="action" property="action"/>
    </resultMap>


    <select id="selectInstitutionAuditLogPage" resultMap="institutionAuditLogResultMap">
        select * from urb_institution_audit_log where is_deleted = 0
    </select>


</mapper>
