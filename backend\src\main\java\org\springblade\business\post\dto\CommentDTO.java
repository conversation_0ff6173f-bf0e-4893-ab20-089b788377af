/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 评论数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
@Schema(description = "评论数据传输对象")
public class CommentDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 帖子ID
	 */
	@Schema(description = "帖子ID")
	@NotNull(message = "帖子ID不能为空")
	private String postId;

	/**
	 * 父评论ID，NULL表示顶级评论
	 */
	@Schema(description = "父评论ID")
	private String parentId;

	/**
	 * 被回复的用户ID
	 */
	@Schema(description = "被回复的用户ID")
	private String replyToUserId;

	/**
	 * 被回复的用户昵称
	 */
	@Schema(description = "被回复的用户昵称")
	private String replyToUserName;

	/**
	 * 评论内容
	 */
	@Schema(description = "评论内容")
	@NotBlank(message = "评论内容不能为空")
	private String content;

	/**
	 * 评论图片URL
	 */
	@Schema(description = "评论图片URL")
	private String image;

	/**
	 * 联系方式
	 */
	@Schema(description = "联系方式")
	private String contactInfo;

	/**
	 * 被提及的用户ID列表
	 */
	@Schema(description = "被提及的用户ID列表")
	private List<Long> mentionedUserIds;

	/**
	 * 被提及的用户信息列表
	 */
	@Schema(description = "被提及的用户信息列表")
	private List<MentionedUserInfo> mentionedUsers;

	/**
	 * 被提及用户信息
	 */
	@Data
	@Schema(description = "被提及用户信息")
	public static class MentionedUserInfo {
		@Schema(description = "用户ID")
		private String userId;

		@Schema(description = "用户昵称")
		private String nickname;
	}

}
