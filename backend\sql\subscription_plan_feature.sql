-- 订阅计划表
CREATE TABLE urb_subscription_plan (
                                       id BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
                                       create_dept BIGINT NULL COMMENT '创建部门ID',
                                       create_time DATETIME(6) NULL COMMENT '创建时间',
                                       create_user BIGINT NULL COMMENT '创建用户ID',
                                       is_deleted INT DEFAULT 0 NULL COMMENT '是否删除（0：未删除，1：已删除）',
                                       status INT DEFAULT 1 NULL COMMENT '状态（0：禁用，1：启用）',
                                       update_time DATETIME(6) NULL COMMENT '更新时间',
                                       update_user BIGINT NULL COMMENT '更新用户ID',
                                       plan_name VARCHAR(100) NOT NULL COMMENT '计划名称',
                                       plan_description TEXT NULL COMMENT '计划描述',
                                       price DECIMAL(10,2) NOT NULL COMMENT '价格',
                                       duration INT NOT NULL COMMENT '有效期（天）',
                                       plan_type VARCHAR(20) DEFAULT 'MONTHLY' NULL COMMENT '计划类型（MONTHLY：月付，YEARLY：年付，CUSTOM：自定义）',
                                       max_features INT DEFAULT 0 NULL COMMENT '最大功能数量限制',
                                       is_popular TINYINT DEFAULT 0 NULL COMMENT '是否热门推荐',
                                       sort_order INT DEFAULT 0 NULL COMMENT '排序',
                                       icon VARCHAR(255) NULL COMMENT '计划图标'
) COMMENT '订阅计划表' CHARSET = utf8mb4;

-- 功能表
CREATE TABLE urb_feature (
                             id BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
                             create_dept BIGINT NULL COMMENT '创建部门ID',
                             create_time DATETIME(6) NULL COMMENT '创建时间',
                             create_user BIGINT NULL COMMENT '创建用户ID',
                             is_deleted INT DEFAULT 0 NULL COMMENT '是否删除（0：未删除，1：已删除）',
                             status INT DEFAULT 1 NULL COMMENT '状态（0：禁用，1：启用）',
                             update_time DATETIME(6) NULL COMMENT '更新时间',
                             update_user BIGINT NULL COMMENT '更新用户ID',
                             feature_name VARCHAR(100) NOT NULL COMMENT '功能名称',
                             feature_code VARCHAR(50) NOT NULL UNIQUE COMMENT '功能代码',
                             feature_description TEXT NULL COMMENT '功能描述',
                             feature_type VARCHAR(20) DEFAULT 'BASIC' NULL COMMENT '功能类型（BASIC：基础功能，PREMIUM：高级功能，CUSTOM：自定义功能）',
                             icon VARCHAR(255) NULL COMMENT '功能图标',
                             sort_order INT DEFAULT 0 NULL COMMENT '排序'
) COMMENT '功能表' CHARSET = utf8mb4;

-- 订阅计划功能关联表（多对多）
CREATE TABLE urb_subscription_plan_feature (
                                               id BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
                                               plan_id BIGINT NOT NULL COMMENT '订阅计划ID',
                                               feature_id BIGINT NOT NULL COMMENT '功能ID',
                                               create_time DATETIME(6) NULL COMMENT '创建时间',
                                               create_user BIGINT NULL COMMENT '创建用户ID',
                                               is_deleted INT DEFAULT 0 NULL COMMENT '是否删除（0：未删除，1：已删除）',
                                               UNIQUE KEY uk_plan_feature (plan_id, feature_id),
                                               INDEX idx_plan_id (plan_id),
                                               INDEX idx_feature_id (feature_id)
) COMMENT '订阅计划功能关联表' CHARSET = utf8mb4;

-- 用户订阅表 （关联表，记录表）
CREATE TABLE urb_user_subscription (
                                       id BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
                                       create_dept BIGINT NULL COMMENT '创建部门ID',
                                       create_time DATETIME(6) NULL COMMENT '创建时间',
                                       create_user BIGINT NULL COMMENT '创建用户ID',
                                       is_deleted INT DEFAULT 0 NULL COMMENT '是否删除（0：未删除，1：已删除）',
                                       status INT DEFAULT 1 NULL COMMENT '状态（0：禁用，1：启用）',
                                       update_time DATETIME(6) NULL COMMENT '更新时间',
                                       update_user BIGINT NULL COMMENT '更新用户ID',
                                       user_id BIGINT NOT NULL COMMENT '用户ID',
                                       plan_id BIGINT NOT NULL COMMENT '订阅计划ID',
                                       start_time DATETIME NOT NULL COMMENT '订阅开始时间',
                                       end_time DATETIME NOT NULL COMMENT '订阅结束时间',
                                       payment_amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
                                       payment_status VARCHAR(20) DEFAULT 'PENDING' NULL COMMENT '支付状态（PENDING：待支付，PAID：已支付，FAILED：支付失败，REFUNDED：已退款）',
                                       payment_method VARCHAR(20) NULL COMMENT '支付方式（WECHAT：微信，ALIPAY：支付宝，BALANCE：余额）',
                                       transaction_id VARCHAR(100) NULL COMMENT '交易流水号',
                                       auto_renew TINYINT DEFAULT 0 NULL COMMENT '是否自动续费',
                                       cancel_reason VARCHAR(255) NULL COMMENT '取消原因',
                                       INDEX idx_user_id (user_id),
                                       INDEX idx_plan_id (plan_id),
                                       INDEX idx_end_time (end_time)
) COMMENT '用户订阅表' CHARSET = utf8mb4;

-- 用户功能权限表（用于快速查询用户当前可用功能）
CREATE TABLE urb_user_feature_permission (
                                             id BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
                                             user_id BIGINT NOT NULL COMMENT '用户ID',
                                             feature_id BIGINT NOT NULL COMMENT '功能ID',
                                             subscription_id BIGINT NULL COMMENT '订阅ID',
                                             is_active TINYINT DEFAULT 1 NULL COMMENT '是否激活',
                                             expire_time DATETIME NULL COMMENT '权限过期时间',
                                             create_time DATETIME(6) NULL COMMENT '创建时间',
                                             update_time DATETIME(6) NULL COMMENT '更新时间',
                                             UNIQUE KEY uk_user_feature (user_id, feature_id),
                                             INDEX idx_user_id (user_id),
                                             INDEX idx_feature_id (feature_id),
                                             INDEX idx_expire_time (expire_time)
) COMMENT '用户功能权限表' CHARSET = utf8mb4;
