/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.service.impl.InstitutionTypeServiceImpl;
import org.springblade.business.post.dto.BatchFeedbackAuditDTO;
import org.springblade.business.post.dto.FeedbackAuditDTO;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.service.IFeedbackService;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.business.user.mapper.FeedbackMapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.miniapp.service.WeChatIInstitutionService;
import org.springblade.modules.system.entity.Post;
import org.springblade.modules.system.service.impl.PostServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户反馈 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class FeedbackServiceImpl extends BaseServiceImpl<FeedbackMapper, Feedback> implements IFeedbackService {

	private final CategoryServiceImpl categoryServiceImpl;
	private final SupPostServiceImpl postServiceImpl;
	private final WeChatIInstitutionService weChatIInstitutionService;
	private final InstitutionTypeServiceImpl institutionTypeServiceImpl;

	public FeedbackServiceImpl(CategoryServiceImpl categoryServiceImpl, SupPostServiceImpl postServiceImpl, WeChatIInstitutionService weChatIInstitutionService, InstitutionTypeServiceImpl institutionTypeServiceImpl) {
		this.categoryServiceImpl = categoryServiceImpl;
		this.postServiceImpl = postServiceImpl;
		this.weChatIInstitutionService = weChatIInstitutionService;
		this.institutionTypeServiceImpl = institutionTypeServiceImpl;
	}

	@Override
	public IPage<FeedbackVO> selectFeedbackPage(IPage<FeedbackVO> page, FeedbackVO feedback) {
		IPage<FeedbackVO> feedbackVOIPage = baseMapper.selectFeedbackPage(page, feedback);
		//如果是帖子反馈加上帖子分类名
		if (Objects.equals(feedback.getType(), "0")) {
			feedbackVOIPage.getRecords().forEach(feedbackVO -> {
				SupPost byId = postServiceImpl.getById(feedbackVO.getRelevancyId());
				if (byId != null) {
					String name = categoryServiceImpl.getById(byId.getCategoryId()).getName();
					feedbackVO.setCategoryName(name);
				}

			});
		}
		//如果是机构加上机构分类名
		if (Objects.equals(feedback.getType(), "1")) {
			feedbackVOIPage.getRecords().forEach(feedbackVO -> {
				Institution byId = weChatIInstitutionService.getById(feedbackVO.getRelevancyId());
				if (byId != null) {
					String name =  institutionTypeServiceImpl.getById(byId.getTypeId()).getName();
					feedbackVO.setCategoryName(name);
				}

			});
		}
		return feedbackVOIPage;
	}

	@Override
	public boolean removeByUserId(List<Long> longList) {
		Long userId = AuthUtil.getUserId();
		LambdaQueryWrapper<Feedback> wp = new LambdaQueryWrapper<>();
		wp.eq(Feedback::getUserId, userId)
			.in(Feedback::getId, longList);
		return this.remove(wp);
	}

	@Override
	public boolean addTag(Tag tag) {
		return baseMapper.addTag(Map.of("label", tag.getTagName(), "categoryId", tag.getCategoryId(),
			"description", tag.getDescription(), "sortOrder", tag.getSortOrder()));
	}

	@Override
	public List<Map<String, Object>> getTagsByCategory(Long categoryId) {
		return baseMapper.getTagsByCategory(categoryId);
	}

	@Override
	public String getHotTags() {
		List<String> list = baseMapper.getHotTags();
		String tags = String.join(",", list);
		return tags;
	}

	@Override
	public boolean removeTag(Long categoryId, Long tagId) {
		// 移除分类与反馈标签的关联关系
		return baseMapper.removeTag(categoryId, tagId);
	}

	@Override
	public List<Map<String, Object>> getAllFeedbackTags() {
		return baseMapper.getAllFeedbackTags();
	}

	@Override
	public Boolean auditFeedback(FeedbackAuditDTO feedbackAuditDTO) {
		Feedback feedback = getById(feedbackAuditDTO.getId());
		if (feedback == null) {
			return false;
		}

		// 更新审核状态、理由、审核时间和审核人ID
		feedback.setAuditStatus(feedbackAuditDTO.getAuditStatus());
		feedback.setReason(feedbackAuditDTO.getReason());
		feedback.setAuditTime(LocalDateTime.now());
		feedback.setAuditUserId(AuthUtil.getUserId());

		return updateById(feedback);
	}

	@Override
	public Boolean batchAuditFeedback(BatchFeedbackAuditDTO batchFeedbackAuditDTO) {
		List<String> feedbackIds = batchFeedbackAuditDTO.getId();
		String auditStatus = batchFeedbackAuditDTO.getAuditStatus();
		String reason = batchFeedbackAuditDTO.getReason();

		// 将String类型的ID转换为Long类型
		List<Long> longFeedbackIds = feedbackIds.stream()
			.map(Long::valueOf)
			.collect(java.util.stream.Collectors.toList());

		// 批量更新反馈审核状态
		LambdaQueryWrapper<Feedback> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.in(Feedback::getId, longFeedbackIds);

		Feedback updateFeedback = new Feedback();
		updateFeedback.setAuditStatus(auditStatus);
		updateFeedback.setReason(reason);
		updateFeedback.setAuditTime(LocalDateTime.now());
		updateFeedback.setAuditUserId(AuthUtil.getUserId());

		return update(updateFeedback, queryWrapper);
	}

	@Override
	public Map<String, Object> getFeedbackAuditStats() {
		Map<String, Object> stats = new HashMap<>();

		// 统计待审核数量
		LambdaQueryWrapper<Feedback> pendingWrapper = new LambdaQueryWrapper<>();
		pendingWrapper.eq(Feedback::getAuditStatus, "0");
		long pendingCount = count(pendingWrapper);
		stats.put("pending", pendingCount);

		// 统计已通过数量
		LambdaQueryWrapper<Feedback> approvedWrapper = new LambdaQueryWrapper<>();
		approvedWrapper.eq(Feedback::getAuditStatus, "1");
		long approvedCount = count(approvedWrapper);
		stats.put("approved", approvedCount);

		// 统计已拒绝数量
		LambdaQueryWrapper<Feedback> rejectedWrapper = new LambdaQueryWrapper<>();
		rejectedWrapper.eq(Feedback::getAuditStatus, "2");
		long rejectedCount = count(rejectedWrapper);
		stats.put("rejected", rejectedCount);

		// 统计总数
		long totalCount = count();
		stats.put("total", totalCount);

		return stats;
	}

	@Override
	public FeedbackVO getFeedbackDetail(Feedback feedback) {
		return baseMapper.getFeedbackDetail(feedback);
	}

	@Override
	public IPage<Feedback> pageFeedback(Feedback feedback, Query query) {
		IPage<Feedback> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(feedback));
		List<Feedback> records = pages.getRecords();
		for (Feedback record : records) {
			Category categoryDetail = categoryServiceImpl.getCategoryDetail(record.getRelevancyId());
			if (categoryDetail != null) {
				record.setCategoryName(categoryDetail.getName());
			}
		}
		return pages;
	}

	@Override
	public Feedback getFeebackById(Feedback feedback) {
		Feedback detail = this.getOne(Condition.getQueryWrapper(feedback));
		if (detail != null) {
			Category categoryDetail = categoryServiceImpl.getCategoryDetail(detail.getRelevancyId());
			if (categoryDetail != null) {
				detail.setCategoryName(categoryDetail.getName());
			}
		}
		return detail;
	}

}
