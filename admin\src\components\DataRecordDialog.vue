<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1000px"
    top="3vh"
    :before-close="handleClose"
    destroy-on-close
    class="data-record-dialog"
    :close-on-click-modal="false">
    <div class="data-record-container">
      <!-- 统计信息 -->
      <div class="stats-info" v-if="recordType">
        <el-card shadow="never" class="stats-card">
          <div class="stats-content">
            <el-icon class="stats-icon" :class="getIconClass()">
              <component :is="getIconComponent()" />
            </el-icon>
            <div class="stats-text">
              <div class="stats-title">{{ getStatsTitle() }}</div>
              <div class="stats-count">{{ totalCount }}</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 数据表格 -->
      <avue-crud
        :option="tableOption"
        :table-loading="loading"
        :data="data"
        v-model:page="page"
        ref="crud"
        @current-change="currentChange"
        @size-change="sizeChange"
        @on-load="onLoad">

        <!-- 用户信息插槽 -->
        <template #userInfo="{ row }">
          <div class="user-info">
            <el-avatar
              :size="32"
              :src="row.userAvatar"
              :alt="row.userNickname">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ row.userNickname || '匿名用户' }}</div>
              <div class="user-id">ID: {{ row.userId || '--' }}</div>
            </div>
          </div>
        </template>

        <!-- 操作时间插槽 -->
        <template #operateTime="{ row }">
          <div class="time-info">
            <div class="time-primary">{{ formatTime(row.operateTime) }}</div>
            <div class="time-relative">{{ getRelativeTime(row.operateTime) }}</div>
          </div>
        </template>

        <!-- IP地址插槽 -->
        <template #ip="{ row }">
          <el-tag type="info" size="small" v-if="row.ip">
            {{ row.ip }}
          </el-tag>
          <span v-else class="text-muted">--</span>
        </template>

        <!-- 反馈内容插槽 -->
        <template #content="{ row }" v-if="recordType === 'feedback'">
          <div class="feedback-content">
            <div class="content-text" v-if="row.content">
              {{ row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content }}
            </div>
            <div class="content-meta">
              <el-tag v-if="row.star" type="warning" size="small">
                {{ row.star }}星
              </el-tag>
              <el-tag v-if="row.commentType" size="small">
                {{ getCommentTypeText(row.commentType) }}
              </el-tag>
            </div>
          </div>
        </template>
      </avue-crud>
    </div>
  </el-dialog>
</template>

<script>
import { User, View, Star, ChatDotRound, Collection } from '@element-plus/icons-vue'

export default {
  name: 'DataRecordDialog',
  components: {
    User,
    View,
    Star,
    ChatDotRound,
    Collection
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    // 记录类型：view-浏览记录，like-点赞记录，feedback-反馈记录，favorite-收藏记录
    recordType: {
      type: String,
      required: true,
      validator: (value) => ['view', 'like', 'feedback', 'favorite'].includes(value)
    },
    // 关联ID（帖子ID或机构ID）
    relevancyId: {
      type: [String, Number],
      required: true
    },
    // 关联类型：0-帖子，1-个人名片，2-机构
    relevancyType: {
      type: [String, Number],
      default: '0'
    },
    // API接口配置
    apiConfig: {
      type: Object,
      required: true
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      loading: false,
      data: [],
      totalCount: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      tableOption: {
        height: 'auto',
        maxHeight: 450,
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        menu: false,
        selection: false,
        stripe: true,
        size: 'small',
        emptyText: '暂无数据',
        column: []
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    dialogTitle() {
      const typeMap = {
        view: '浏览记录',
        like: '点赞记录',
        feedback: '反馈记录',
        favorite: '收藏记录'
      }
      return typeMap[this.recordType] || '数据记录'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initTableColumns()
        this.onLoad(this.page)
      }
    },
    recordType() {
      this.initTableColumns()
    }
  },
  created() {
    this.initTableColumns()
  },
  methods: {
    initTableColumns() {
      const baseColumns = [
        {
          label: "用户信息",
          prop: "userInfo",
          width: 280,
          slot: true
        },
        {
          label: "操作时间",
          prop: "operateTime",
          width: 160,
          slot: true,
          sortable: true
        },
        {
          label: "IP地址",
          prop: "ip",
          width: 130,
          slot: true
        }
      ]

      // 根据记录类型添加特定列
      if (this.recordType === 'feedback') {
        baseColumns.splice(1, 0, {
          label: "反馈内容",
          prop: "content",
          width: 350,
          slot: true
        })
      }

      this.tableOption.column = baseColumns
    },

    getIconClass() {
      const classMap = {
        view: 'view-icon',
        like: 'like-icon',
        feedback: 'feedback-icon',
        favorite: 'favorite-icon'
      }
      return classMap[this.recordType] || ''
    },

    getIconComponent() {
      const componentMap = {
        view: 'View',
        like: 'Star',
        feedback: 'ChatDotRound',
        favorite: 'Collection'
      }
      return componentMap[this.recordType] || 'View'
    },

    getStatsTitle() {
      const titleMap = {
        view: '总浏览量',
        like: '总点赞量',
        feedback: '总反馈量',
        favorite: '总收藏量'
      }
      return titleMap[this.recordType] || '总数量'
    },

    getCommentTypeText(type) {
      const typeMap = {
        'FEEDBACK': '反馈',
        'COMMENT': '评论',
        'REPLY': '回复'
      }
      return typeMap[type] || type
    },

    formatTime(time) {
      if (!time) return '--'

      // 如果时间是数字格式的时间戳，需要转换
      if (typeof time === 'number') {
        // 如果是秒级时间戳，转换为毫秒
        if (time < 10000000000) {
          time = time * 1000
        }
        return this.$dayjs ? this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss') : new Date(time).toLocaleString()
      }

      // 如果是字符串格式的时间
      if (typeof time === 'string') {
        return this.$dayjs ? this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss') : time
      }

      return time
    },

    getRelativeTime(time) {
      if (!time) return ''
      return this.$dayjs ? this.$dayjs(time).fromNow() : ''
    },

    async onLoad(page) {
      if (!this.apiConfig || !this.apiConfig.getList) {
        console.error('API配置不存在:', this.apiConfig)
        this.$message.error('API配置错误')
        this.loading = false
        return
      }

      this.loading = true
      try {
        const params = {
          current: page.currentPage,
          size: page.pageSize,
          relevancyId: this.relevancyId,
          type: this.relevancyType
        }

        let response = await this.apiConfig.getList(params)
        response = response.data

        if (response.code === 200) {
          const data = response.data
          this.data = data.records || []
          this.page.total = Number(data.total) || 0
          this.totalCount = this.page.total
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取数据记录失败:', error)
        this.$message.error('获取数据失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    currentChange(currentPage) {
      this.page.currentPage = currentPage
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageSize = pageSize
      this.page.currentPage = 1
      this.onLoad(this.page)
    },

    handleClose() {
      this.visible = false
      this.data = []
      this.totalCount = 0
      this.page.currentPage = 1
    }
  }
}
</script>

<style lang="scss" scoped>
.data-record-container {
  .stats-info {
    margin-bottom: 20px;

    .stats-card {
      border: none;

      .stats-content {
        display: flex;
        align-items: center;

        .stats-icon {
          font-size: 32px;
          margin-right: 16px;

          &.view-icon {
            color: #409EFF;
          }

          &.like-icon {
            color: #F56C6C;
          }

          &.feedback-icon {
            color: #67C23A;
          }

          &.favorite-icon {
            color: #E6A23C;
          }
        }

        .stats-text {
          .stats-title {
            font-size: 14px;
            color: #909399;
            margin-bottom: 4px;
          }

          .stats-count {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .user-details {
      margin-left: 12px;

      .user-name {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }

      .user-id {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }

  .time-info {
    .time-primary {
      font-size: 14px;
      color: #303133;
    }

    .time-relative {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }

  .feedback-content {
    .content-text {
      font-size: 14px;
      color: #303133;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .content-meta {
      display: flex;
      gap: 8px;
    }
  }

  .text-muted {
    color: #C0C4CC;
  }
}
</style>

<style>
/* 弹框样式优化 */
.data-record-dialog .el-dialog {
  margin-top: 3vh !important;
  margin-bottom: 3vh !important;
  max-height: 94vh;
  display: flex;
  flex-direction: column;
}

.data-record-dialog .el-dialog__body {
  flex: 1;
  padding: 15px 20px 20px;
  overflow: hidden;
}

.data-record-dialog .data-record-container {
  min-height: 500px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.data-record-dialog .stats-info {
  margin-bottom: 15px;
  flex-shrink: 0;
}

.data-record-dialog .avue-crud {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-record-dialog .avue-crud .el-table {
  flex: 1;
  min-height: 300px;
}

.data-record-dialog .avue-crud .avue-crud__pagination {
  flex-shrink: 0;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #EBEEF5;
}

/* 确保表格内容能够正确显示 */
.data-record-dialog .el-table__body-wrapper {
  overflow-y: auto;
}

.data-record-dialog .el-table .el-table__row {
  height: auto;
}

.data-record-dialog .el-table .el-table__cell {
  padding: 8px 0;
}
</style>
