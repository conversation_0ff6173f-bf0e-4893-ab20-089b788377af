/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.entity;

import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.TenantEntity;

import java.io.Serial;

/**
 * 微信支付配置实体类（支持多商户）
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@TableName("urb_wechat_pay_config")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "微信支付配置")
public class WechatPayConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String configName;

    /**
     * 小程序AppID
     */
    @Schema(description = "小程序AppID")
    private String appId;

    /**
     * 商户号
     */
    @Schema(description = "商户号")
    private String mchId;

    /**
     * API密钥
     */
    @Schema(description = "API密钥")
    private String apiKey;

    /**
     * APIv3密钥
     */
    @Schema(description = "APIv3密钥")
    private String apiV3Key;

    /**
     * 证书路径
     */
    @Schema(description = "证书路径")
    private String certPath;

    /**
     * 证书序列号
     */
    @Schema(description = "证书序列号")
    private String certSerialNo;

    /**
     * 私钥路径
     */
    @Schema(description = "私钥路径")
    private String privateKeyPath;

    /**
     * 支付回调地址
     */
    @Schema(description = "支付回调地址")
    private String notifyUrl;

    /**
     * 是否沙箱环境
     */
    @Schema(description = "是否沙箱环境")
    private Boolean isSandbox;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean isEnabled;

    /**
     * 是否默认配置
     */
    @Schema(description = "是否默认配置")
    private Boolean isDefault;

    /**
     * 优先级（数字越大优先级越高）
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
