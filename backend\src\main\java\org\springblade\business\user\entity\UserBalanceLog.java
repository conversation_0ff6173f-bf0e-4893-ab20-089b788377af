/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 用户余额变动记录实体类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@TableName("urb_user_balance_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户余额变动记录")
public class UserBalanceLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 关联订单ID（充值订单、消费订单等）
     */
    @Schema(description = "关联订单ID")
    private String orderId;

    /**
     * 变动类型：RECHARGE-充值，CONSUME-消费，REFUND-退款，REWARD-奖励，DEDUCT-扣减
     */
    @Schema(description = "变动类型")
    private String changeType;

    /**
     * 变动金额（单位：元，正数为增加，负数为减少）
     */
    @Schema(description = "变动金额（单位：元）")
    private BigDecimal changeAmount;

    /**
     * 变动前余额（单位：元）
     */
    @Schema(description = "变动前余额（单位：元）")
    private BigDecimal balanceBefore;

    /**
     * 变动后余额（单位：元）
     */
    @Schema(description = "变动后余额（单位：元）")
    private BigDecimal balanceAfter;

    /**
     * 业务类型：POST_PUBLISH-发帖，POST_TOP-置顶，INVITE_REWARD-邀请奖励等
     */
    @Schema(description = "业务类型")
    private String businessType;

    /**
     * 业务关联ID
     */
    @Schema(description = "业务关联ID")
    private String businessId;

    /**
     * 备注说明
     */
    @Schema(description = "备注说明")
    private String remark;

    /**
     * 余额变动类型枚举
     */
    public enum ChangeType {
        RECHARGE("RECHARGE", "充值"),
        CONSUME("CONSUME", "消费"),
        REFUND("REFUND", "退款"),
        REWARD("REWARD", "奖励"),
        DEDUCT("DEDUCT", "扣减");

        private final String code;
        private final String desc;

        ChangeType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        POST_PUBLISH("POST_PUBLISH", "发帖"),
        POST_TOP("POST_TOP", "置顶"),
        INVITE_REWARD("INVITE_REWARD", "邀请奖励"),
        RECHARGE("RECHARGE", "充值"),
        SYSTEM_REWARD("SYSTEM_REWARD", "系统奖励"),
        SYSTEM_DEDUCT("SYSTEM_DEDUCT", "系统扣减");

        private final String code;
        private final String desc;

        BusinessType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
