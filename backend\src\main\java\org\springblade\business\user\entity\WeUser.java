/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.TenantEntity;

import java.io.Serial;

/**
 * 用户信息实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_user")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户信息")
public class WeUser extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;
    /**
     * 性别
     */
    @Schema(description = "性别")
    private String gender;
    /**
     * 个性签名
     */
    @Schema(description = "个性签名")
    private String signature;

	@Schema(description = "头像")
	private String avatar;

	@Schema(description = "生日")
	private String birthday;

	@Schema(description = "地区")
	private String region;

	@Schema(description = "邮箱")
	private String email;

	/**
	 * 年龄
	 */
	@Schema(description = "年龄")
	private Integer age;


	/**
	 * 积分
	 */
	@Schema(description = "积分")
	private Integer balance;

	/**
	 * 钱包余额（单位：元）
	 */
	@Schema(description = "钱包余额（单位：元）")
	private java.math.BigDecimal walletBalance;

	/**
	 * 公司名字
	 */
	@Schema(description = "公司名字")
	private String companyName;

	/**
	 * 微信OpenID
	 */
	@Schema(description = "微信OpenID")
	private String openId;

	/**
	 * 微信UnionID
	 */
	@Schema(description = "微信UnionID")
	private String unionId;

	/**
	 * 最后登录时间
	 */
	@Schema(description = "最后登录时间")
	private java.time.LocalDateTime lastLoginTime;

}
