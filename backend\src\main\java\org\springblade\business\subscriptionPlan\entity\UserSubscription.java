/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 用户订阅表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("urb_user_subscription")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户订阅表")
public class UserSubscription extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 订阅计划ID
     */
    @Schema(description = "订阅计划ID")
    private Long planId;
    /**
     * 订阅开始时间
     */
    @Schema(description = "订阅开始时间")
    private LocalDateTime startTime;
    /**
     * 订阅结束时间
     */
    @Schema(description = "订阅结束时间")
    private LocalDateTime endTime;
    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;
    /**
     * 支付状态（PENDING：待支付，PAID：已支付，FAILED：支付失败，REFUNDED：已退款）
     */
    @Schema(description = "支付状态（PENDING：待支付，PAID：已支付，FAILED：支付失败，REFUNDED：已退款）")
    private String paymentStatus;
    /**
     * 支付方式（WECHAT：微信，ALIPAY：支付宝，BALANCE：余额）
     */
    @Schema(description = "支付方式（WECHAT：微信，ALIPAY：支付宝，BALANCE：余额）")
    private String paymentMethod;
    /**
     * 交易流水号
     */
    @Schema(description = "交易流水号")
    private String transactionId;
    /**
     * 是否自动续费
     */
    @Schema(description = "是否自动续费")
    private Byte autoRenew;
    /**
     * 取消原因
     */
    @Schema(description = "取消原因")
    private String cancelReason;


}
