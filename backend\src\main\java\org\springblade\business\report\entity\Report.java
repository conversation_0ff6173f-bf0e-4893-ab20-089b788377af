/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 举报记录实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_report")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "举报记录")
public class Report extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 信息贴ID
     */
    @Schema(description = "信息贴ID")
    private Long postId;
    /**
     * 举报用户ID
     */
    @Schema(description = "举报用户ID")
    private Long userId;
    /**
     * 举报内容
     */
    @Schema(description = "举报内容")
    private String content;
    /**
     * 举报图片
     */
    @Schema(description = "举报图片")
    private String images;

	@Schema(description = "审核状态")
	private String auditStatus;

	@Schema(description = "理由")
	private String reason;

	@Schema(description = "审核备注")
	private String auditRemark;

	@Schema(description = "审核时间")
	private String auditTime;

	@Schema(description = "审核人")
	private Long auditUser;

	@Schema(description = "被举报用户名字")
	@TableField(exist = false)
	private String userName;

	@Schema(description = "被举报用户电话")
	@TableField(exist = false)
	private String userPhone;

	@Schema(description = "举报用户ID")
	@TableField(exist = false)
	private Long reportUserId;

	@Schema(description = "举报用户名字")
	@TableField(exist = false)
	private String reportUserName;

	@Schema(description = "举报用户电话")
	@TableField(exist = false)
	private String reportUserPhone;

	@Schema(description = "被举报次数")
	@TableField(exist = false)
	private Integer reportCount;

}
