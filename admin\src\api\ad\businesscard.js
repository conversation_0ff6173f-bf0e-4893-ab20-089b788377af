import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/businesscard/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/businesscard/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/businesscard/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/businesscard/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/businesscard/submit',
    method: 'post',
    data: row
  })
}

// 审核名片
export const auditBusinessCard = (cardId, auditStatus, auditRemark) => {
  return request({
    url: '/blade-ad/businesscard/audit',
    method: 'post',
    params: {
      cardId,
      auditStatus,
      auditRemark
    }
  })
}

// 批量审核名片
export const batchAuditBusinessCards = (auditData) => {
  return request({
    url: '/blade-ad/businesscard/batch-audit',
    method: 'post',
    params: auditData
  })
}

// 修改名片发布状态
export const updatePublishStatus = (id, publishStatus) => {
  return request({
    url: `/blade-ad/businesscard/${id}/publish-status`,
    method: 'post',
    params: { publishStatus }
  })
}

// 批量修改名片发布状态
export const batchUpdatePublishStatus = (cardIds, publishStatus) => {
  return request({
    url: '/blade-ad/businesscard/batch-publish-status',
    method: 'post',
    params: {
      cardIds: Array.isArray(cardIds) ? cardIds.join(',') : cardIds,
      publishStatus
    }
  })
}

// 批量上架名片
export const batchOnlineCards = (cardIds) => {
  return request({
    url: '/blade-ad/businesscard/batch-online',
    method: 'post',
    params: {
      cardIds: Array.isArray(cardIds) ? cardIds.join(',') : cardIds
    }
  })
}

// 批量下架名片
export const batchOfflineCards = (cardIds) => {
  return request({
    url: '/blade-ad/businesscard/batch-offline',
    method: 'post',
    params: {
      cardIds: Array.isArray(cardIds) ? cardIds.join(',') : cardIds
    }
  })
}

