/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.subscriptionPlan.entity.SubscriptionPlanFeature;
import org.springblade.business.subscriptionPlan.service.ISubscriptionPlanFeatureService;
import org.springblade.business.subscriptionPlan.vo.SubscriptionPlanFeatureVO;
import org.springblade.business.subscriptionPlan.wrapper.SubscriptionPlanFeatureWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 订阅计划功能关联表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/subscriptionplanfeature")
@io.swagger.v3.oas.annotations.tags.Tag(name = "订阅计划功能关联表", description = "订阅计划功能关联表接口")
public class SubscriptionPlanFeatureController extends BladeController {

	private ISubscriptionPlanFeatureService subscriptionPlanFeatureService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入subscriptionPlanFeature")
	public R<SubscriptionPlanFeatureVO> detail(SubscriptionPlanFeature subscriptionPlanFeature) {
		SubscriptionPlanFeature detail = subscriptionPlanFeatureService.getOne(Condition.getQueryWrapper(subscriptionPlanFeature));
		return R.data(SubscriptionPlanFeatureWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 订阅计划功能关联表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入subscriptionPlanFeature")
	public R<IPage<SubscriptionPlanFeatureVO>> list(SubscriptionPlanFeature subscriptionPlanFeature, Query query) {
		IPage<SubscriptionPlanFeature> pages = subscriptionPlanFeatureService.page(Condition.getPage(query), Condition.getQueryWrapper(subscriptionPlanFeature));
		return R.data(SubscriptionPlanFeatureWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 订阅计划功能关联表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入subscriptionPlanFeature")
	public R<IPage<SubscriptionPlanFeatureVO>> page(SubscriptionPlanFeatureVO subscriptionPlanFeature, Query query) {
		IPage<SubscriptionPlanFeatureVO> pages = subscriptionPlanFeatureService.selectSubscriptionPlanFeaturePage(Condition.getPage(query), subscriptionPlanFeature);
		return R.data(pages);
	}

	/**
	 * 新增 订阅计划功能关联表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入subscriptionPlanFeature")
	public R save(@Valid @RequestBody SubscriptionPlanFeature subscriptionPlanFeature) {
		return R.status(subscriptionPlanFeatureService.save(subscriptionPlanFeature));
	}

	/**
	 * 修改 订阅计划功能关联表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入subscriptionPlanFeature")
	public R update(@Valid @RequestBody SubscriptionPlanFeature subscriptionPlanFeature) {
		return R.status(subscriptionPlanFeatureService.updateById(subscriptionPlanFeature));
	}

	/**
	 * 新增或修改 订阅计划功能关联表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入subscriptionPlanFeature")
	public R submit(@Valid @RequestBody SubscriptionPlanFeature subscriptionPlanFeature) {
		return R.status(subscriptionPlanFeatureService.saveOrUpdate(subscriptionPlanFeature));
	}


	/**
	 * 删除 订阅计划功能关联表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(subscriptionPlanFeatureService.deleteLogic(Func.toLongList(ids)));
	}


}
