/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.service.impl;

import org.springblade.business.subscriptionPlan.entity.SubscriptionPlanFeature;
import org.springblade.business.subscriptionPlan.mapper.SubscriptionPlanFeatureMapper;
import org.springblade.business.subscriptionPlan.service.ISubscriptionPlanFeatureService;
import org.springblade.business.subscriptionPlan.vo.SubscriptionPlanFeatureVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 订阅计划功能关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class SubscriptionPlanFeatureServiceImpl extends BaseServiceImpl<SubscriptionPlanFeatureMapper, SubscriptionPlanFeature> implements ISubscriptionPlanFeatureService {

	@Override
	public IPage<SubscriptionPlanFeatureVO> selectSubscriptionPlanFeaturePage(IPage<SubscriptionPlanFeatureVO> page, SubscriptionPlanFeatureVO subscriptionPlanFeature) {
		return page.setRecords(baseMapper.selectSubscriptionPlanFeaturePage(page, subscriptionPlanFeature));
	}

}
