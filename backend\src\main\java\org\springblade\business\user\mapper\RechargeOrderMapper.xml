<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.RechargeOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springblade.business.user.entity.RechargeOrder">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="order_no" property="orderNo" />
        <result column="user_id" property="userId" />
        <result column="pay_config_id" property="payConfigId" />
        <result column="recharge_amount" property="rechargeAmount" />
        <result column="payment_method" property="paymentMethod" />
        <result column="order_status" property="orderStatus" />
        <result column="payment_status" property="paymentStatus" />
        <result column="wx_prepay_id" property="wxPrepayId" />
        <result column="wx_transaction_id" property="wxTransactionId" />
        <result column="wx_out_trade_no" property="wxOutTradeNo" />
        <result column="payment_time" property="paymentTime" />
        <result column="success_time" property="successTime" />
        <result column="expire_time" property="expireTime" />
        <result column="client_ip" property="clientIp" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, order_no, user_id, pay_config_id, recharge_amount, payment_method,
        order_status, payment_status, wx_prepay_id, wx_transaction_id, wx_out_trade_no,
        payment_time, success_time, expire_time, client_ip, remark,
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>


    <select id="selectRechargeOrderPage" resultMap="BaseResultMap">
        select * from urb_recharge_order where is_deleted = 0
    </select>

    <!-- 根据订单号查询充值订单 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE order_no = #{orderNo}
        AND is_deleted = 0
    </select>

    <!-- 根据微信交易号查询充值订单 -->
    <select id="selectByWxTransactionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE wx_transaction_id = #{transactionId}
        AND is_deleted = 0
    </select>

    <!-- 分页查询用户充值订单 -->
    <select id="selectUserRechargeOrderPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE user_id = #{userId}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户充值统计 -->
    <select id="selectUserRechargeStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalOrders,
            COALESCE(SUM(CASE WHEN order_status = 'SUCCESS' THEN recharge_amount ELSE 0 END), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN order_status = 'SUCCESS' THEN 1 ELSE 0 END), 0) as successOrders,
            COALESCE(SUM(CASE WHEN order_status = 'FAILED' THEN 1 ELSE 0 END), 0) as failedOrders,
            COALESCE(SUM(CASE WHEN order_status = 'PENDING' THEN 1 ELSE 0 END), 0) as pendingOrders
        FROM urb_recharge_order
        WHERE user_id = #{userId}
        AND is_deleted = 0
    </select>

    <!-- 查询过期未支付的订单 -->
    <select id="selectExpiredOrders" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE order_status = 'PENDING'
        AND payment_status = 'UNPAID'
        AND expire_time &lt; NOW()
        AND create_time &gt;= DATE_SUB(NOW(), INTERVAL #{expireMinutes} MINUTE)
        AND is_deleted = 0
        ORDER BY create_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新订单状态 -->
    <update id="batchUpdateOrderStatus">
        UPDATE urb_recharge_order
        SET order_status = #{newStatus},
            update_time = NOW()
        WHERE id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND order_status = #{oldStatus}
        AND is_deleted = 0
    </update>

    <!-- 查询待同步的订单（最近创建但状态为PENDING或PAID的订单） -->
    <select id="selectPendingOrdersForSync" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE (
            (order_status = 'PENDING' AND payment_status = 'UNPAID') OR
            (order_status = 'PAID' AND payment_status = 'PAID')
        )
        AND create_time &gt;= DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
        AND wx_out_trade_no IS NOT NULL
        AND is_deleted = 0
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询今日充值统计 -->
    <select id="selectTodayRechargeStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as todayOrders,
            COALESCE(SUM(CASE WHEN order_status = 'SUCCESS' THEN recharge_amount ELSE 0 END), 0) as todayAmount,
            COALESCE(SUM(CASE WHEN order_status = 'SUCCESS' THEN 1 ELSE 0 END), 0) as todaySuccessOrders,
            COALESCE(AVG(CASE WHEN order_status = 'SUCCESS' THEN recharge_amount ELSE NULL END), 0) as avgAmount
        FROM urb_recharge_order
        WHERE DATE(create_time) = CURDATE()
        AND is_deleted = 0
    </select>

    <!-- 查询指定时间范围内的充值记录 -->
    <select id="selectRechargeOrdersByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            AND order_status = #{orderStatus}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND payment_status = #{paymentStatus}
        </if>
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户最近的充值订单 -->
    <select id="selectUserRecentOrders" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE user_id = #{userId}
        AND is_deleted = 0
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 根据微信商户订单号查询 -->
    <select id="selectByWxOutTradeNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM urb_recharge_order
        WHERE wx_out_trade_no = #{wxOutTradeNo}
        AND is_deleted = 0
    </select>

    <!-- 统计充值成功率 -->
    <select id="selectRechargeSuccessRate" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalOrders,
            SUM(CASE WHEN order_status = 'SUCCESS' THEN 1 ELSE 0 END) as successOrders,
            ROUND(SUM(CASE WHEN order_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as successRate,
            SUM(CASE WHEN order_status = 'FAILED' THEN 1 ELSE 0 END) as failedOrders,
            SUM(CASE WHEN order_status = 'PENDING' THEN 1 ELSE 0 END) as pendingOrders
        FROM urb_recharge_order
        WHERE create_time &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        AND is_deleted = 0
    </select>

</mapper>
