package org.springblade.business.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 名片收藏扩展信息表实体类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@TableName("urb_business_card_favorite_ext")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "名片收藏扩展信息表")
public class BusinessCardFavorite extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名片ID
     */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
    @Schema(description = "名片ID")
    private Long cardId;

    /**
     * 收藏用户ID
     */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
    @Schema(description = "收藏用户ID")
    private Long userId;

    /**
     * 名片快照数据（JSON格式）
     */
    @Schema(description = "名片快照数据")
    private String cardSnapshot;

    /**
     * 收藏分类
     */
    @Schema(description = "收藏分类")
    private String category;

    /**
     * 收藏备注
     */
    @Schema(description = "收藏备注")
    private String remark;
}
