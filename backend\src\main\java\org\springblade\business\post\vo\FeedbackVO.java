/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.business.post.dto.FeedBackStatsDTO;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.institution.entity.Institution;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 用户反馈视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户反馈")
public class FeedbackVO extends Feedback {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 是否标记为有帮助
	 */
	@Schema(description = "是否标记为有帮助")
	private boolean isHelpful;

	/**
	 * 有帮助标记数量
	 */
	@Schema(description = "有帮助标记数量")
	private int helpfulCount;

	private String nickname;

	private String avatar;

	/**
	 * 帖子分类
	 *
	 */
	@Schema(description = "反馈分类名")
	private String categoryName;

	/**
	 * 反馈时间
	 *
	 */
	@Schema(description = "反馈时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date feedbackTime;

	/**
	 * 关联的帖子信息
	 */
	@Schema(description = "关联的帖子信息")
	private SupPost post;

	/**
	 * 关联的机构信息
	 */
	@Schema(description = "关联的机构信息")
	private Institution institution;

	/**
	 * 评论统计信息
	 */
	@Schema(description = "评论统计信息")
	private FeedBackStatsDTO stats;

}
