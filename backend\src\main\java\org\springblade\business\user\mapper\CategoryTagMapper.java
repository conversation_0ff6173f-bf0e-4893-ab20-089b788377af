/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.business.post.entity.CategoryTag;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.vo.CategoryTagVO;

import java.util.List;

/**
 * 分类标签关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Mapper
public interface CategoryTagMapper extends BaseMapper<CategoryTag> {

    /**
     * 批量插入
     *
     * @param categoryTags 分类标签关联列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<CategoryTag> categoryTags);

    /**
     * 根据分类ID删除所有关联
     *
     * @param categoryId 分类ID
     * @return 影响行数
     */
    int deleteByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据标签ID删除所有关联
     *
     * @param tagId 标签ID
     * @return 影响行数
     */
    int deleteByTagId(@Param("tagId") Long tagId);

    /**
     * 分页查询分类标签关联
     *
     * @param page        分页参数
     * @param categoryTag 查询条件
     * @return 分页结果
     */
    List<CategoryTagVO> selectCategoryTagPage(Page<CategoryTagVO> page, @Param("categoryTag") CategoryTagVO categoryTag);

    /**
     * 根据分类ID查询标签信息（包含标签详情）
     *
     * @param categoryId 分类ID
     * @return 标签列表
     */
    List<Tag> selectTagsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据分类ID查询标签数量
     *
     * @param categoryId 分类ID
     * @return 标签数量
     */
    Integer selectTagCountByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 检查标签是否属于分类
     *
     * @param categoryId 分类ID
     * @param tagId 标签ID
     * @return 是否存在关联
     */
    Boolean checkTagBelongToCategory(@Param("categoryId") Long categoryId, @Param("tagId") Long tagId);

    /**
     * 获取分类下的所有标签ID
     *
     * @param categoryId 分类ID
     * @return 标签ID列表
     */
    List<Long> selectTagIdsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 更新标签排序
     *
     * @param categoryId 分类ID
     * @param tagId 标签ID
     * @param sortOrder 排序
     * @return 影响行数
     */
    int updateTagSort(@Param("categoryId") Long categoryId, @Param("tagId") Long tagId, @Param("sortOrder") Integer sortOrder);

    /**
     * 批量更新标签排序
     *
     * @param categoryTags 分类标签关联列表
     * @return 影响行数
     */
    int batchUpdateTagSort(@Param("list") List<CategoryTag> categoryTags);

    /**
     * 获取热门标签（按使用次数排序）
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<Tag> selectHotTags(@Param("limit") Integer limit);

    /**
     * 根据分类ID获取标签统计信息
     *
     * @param categoryId 分类ID
     * @return 统计信息
     */
    List<CategoryTagVO> selectTagStatsByCategory(@Param("categoryId") Long categoryId);
}
