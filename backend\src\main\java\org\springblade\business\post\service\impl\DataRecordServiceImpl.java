package org.springblade.business.post.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springblade.business.post.mapper.DataRecordMapper;
import org.springblade.business.post.service.IDataRecordService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 数据记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Service
@AllArgsConstructor
public class DataRecordServiceImpl implements IDataRecordService {

	private DataRecordMapper dataRecordMapper;

	@Override
	public IPage<Map<String, Object>> getViewRecords(Page<Map<String, Object>> page, Long relevancyId, String type) {
		return dataRecordMapper.selectViewRecords(page, relevancyId, type);
	}

	@Override
	public IPage<Map<String, Object>> getLikeRecords(Page<Map<String, Object>> page, Long relevancyId, String type) {
		return dataRecordMapper.selectLikeRecords(page, relevancyId, type);
	}

	@Override
	public IPage<Map<String, Object>> getFeedbackRecords(Page<Map<String, Object>> page, Long relevancyId) {
		return dataRecordMapper.selectFeedbackRecords(page, relevancyId);
	}

	@Override
	public IPage<Map<String, Object>> getFavoriteRecords(Page<Map<String, Object>> page, Long relevancyId, String type) {
		return dataRecordMapper.selectFavoriteRecords(page, relevancyId, type);
	}

}
