package org.springblade.business.user.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.springblade.business.statistics.entity.UserRequestStats;

import java.util.List;

@Mapper
public interface UserLoginStatsMapper {
    /**
     * 插入或更新用户请求统计（使用MySQL的INSERT ON DUPLICATE KEY UPDATE）
     */
    int upsert(UserRequestStats stats);

    /**
     * 批量插入或更新用户请求统计
     */
    int batchUpsert(List<UserRequestStats> statsList);
}

