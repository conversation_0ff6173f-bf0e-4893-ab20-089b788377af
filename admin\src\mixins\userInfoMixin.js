import { getUserDict } from '@/api/ad/user'
import UserDetailDialog from '@/components/UserDetailDialog.vue'

/**
 * 用户信息处理混入
 * 提供用户ID映射为昵称、用户详情查看等功能
 */
export default {
  components: {
    UserDetailDialog
  },
  
  data() {
    return {
      // 用户详情对话框相关
      userDetailVisible: false,
      selectedUserId: null,
      // 用户字典数据
      userDict: []
    }
  },
  
  methods: {
    /**
     * 加载用户字典数据
     */
    async loadUserDict() {
      try {
        const res = await getUserDict()
        this.userDict = res.data.data || []
        
        // 更新表格列中的用户字典数据
        this.updateUserColumnDict()
      } catch (error) {
        console.error('加载用户字典失败:', error)
      }
    },
    
    /**
     * 更新表格列中的用户字典数据
     */
    updateUserColumnDict() {
      if (!this.option || !this.option.column) return
      
      // 查找所有用户相关的列
      const userColumns = this.option.column.filter(col => 
        this.isUserColumn(col.prop)
      )
      
      userColumns.forEach(col => {
        if (col.type === 'select') {
          col.dicData = this.userDict
          col.props = {
            label: "nickname",
            value: "id"
          }
        }
      })
    },
    
    /**
     * 判断是否为用户相关的列
     */
    isUserColumn(prop) {
      const userProps = [
        'userId', 'createUser', 'updateUser', 'inviterUserId', 
        'inviteeUserId', 'publishUser', 'auditUser', 'operatorId'
      ]
      return userProps.includes(prop)
    },
    
    /**
     * 根据用户ID获取用户昵称
     */
    getUserNickname(userId) {
      if (!userId) return '未知用户'
      
      const user = this.userDict.find(u => u.id === userId)
      return user ? user.nickname : `用户${userId}`
    },
    
    /**
     * 根据行数据获取用户昵称（支持多种用户字段）
     */
    getUserNicknameFromRow(row, userField = 'userId') {
      const userId = row[userField]
      return this.getUserNickname(userId)
    },
    
    /**
     * 显示用户详情
     */
    showUserDetail(userId) {
      if (!userId) {
        this.$message.warning('用户信息不存在')
        return
      }
      this.selectedUserId = userId
      this.userDetailVisible = true
    },
    
    /**
     * 处理管理用户事件
     */
    handleManageUser(userDetail) {
      this.$message.info(`管理用户: ${userDetail.nickname}`)
      // 可以在这里跳转到用户管理页面或执行其他管理操作
      // this.$router.push(`/admin/user/detail/${userDetail.id}`)
    },
    
    /**
     * 创建用户插槽模板
     */
    createUserSlotTemplate(userField = 'userId') {
      return {
        template: `
          <el-button
            type="text"
            @click="showUserDetail(row.${userField})"
            class="user-link">
            {{ getUserNicknameFromRow(row, '${userField}') }}
          </el-button>
        `
      }
    },
    
    /**
     * 为用户列添加插槽配置
     */
    setupUserColumnSlot(columnProp) {
      if (!this.option || !this.option.column) return
      
      const userCol = this.option.column.find(c => c.prop === columnProp)
      if (userCol) {
        userCol.slot = true
        userCol.search = true
        userCol.type = 'select'
        userCol.dicData = this.userDict
        userCol.props = {
          label: "nickname",
          value: "id"
        }
      }
    }
  },
  
  /**
   * 在组件挂载时加载用户字典
   */
  async mounted() {
    await this.loadUserDict()
  }
}
