/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 评论提及实体类
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
@TableName("urb_comment_mention")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "评论提及")
public class CommentMention extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 评论ID
	 */
	@Schema(description = "评论ID")
	private Long commentId;

	/**
	 * 被提及的用户ID
	 */
	@Schema(description = "被提及的用户ID")
	private Long mentionedUserId;

	/**
	 * 被提及的用户昵称
	 */
	@Schema(description = "被提及的用户昵称")
	private String mentionedUserName;

}
