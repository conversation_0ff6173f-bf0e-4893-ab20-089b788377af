/**
 * Copyright (c) 2018-2099, Chi<PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 售卖二手车表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_used_car")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "售卖二手车表")
public class UsedCar extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 关联帖子Id
     */
    @Schema(description = "关联帖子Id")
    private Long postId;
    /**
     * 车名
     */
    @Schema(description = "车名")
    private String carName;
    /**
     * 车年限
     */
    @Schema(description = "车年限")
    private String carAge;
    /**
     * 车价
     */
    @Schema(description = "车价")
    private String carPrice;
    /**
     * 行驶公里数
     */
    @Schema(description = "行驶公里数")
    private String carKm;
    /**
     * 配置
     */
    @Schema(description = "配置")
    private String carConfiguration;
    /**
     * （1-卖车，2-买车）
     */
    @Schema(description = "（1-卖车，2-买车）")
    private String type;


	@Schema(description = "车辆图片")
	private String images;


}
