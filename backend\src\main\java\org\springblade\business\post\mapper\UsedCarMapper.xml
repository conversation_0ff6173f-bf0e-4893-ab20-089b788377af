<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.UsedCarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="usedCarResultMap" type="org.springblade.business.post.entity.UsedCar">
        <result column="id" property="id"/>
        <result column="post_id" property="postId"/>
        <result column="car_name" property="carName"/>
        <result column="car_age" property="carAge"/>
        <result column="car_price" property="carPrice"/>
        <result column="car_km" property="carKm"/>
        <result column="car_configuration" property="carConfiguration"/>
        <result column="type" property="type"/>
    </resultMap>


    <select id="selectUsedCarPage" resultMap="usedCarResultMap">
        select * from urb_used_car where is_deleted = 0
    </select>

</mapper>
