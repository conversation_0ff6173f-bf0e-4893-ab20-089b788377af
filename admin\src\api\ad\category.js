import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-ad/category/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const dicList = () => {
  return request({
    url: '/api/blade-ad/category/list',
    method: 'get',
  })
}



export const getDetail = (id) => {
  return request({
    url: '/api/blade-ad/category/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-ad/category/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-ad/category/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-ad/category/submit',
    method: 'post',
    data: row
  })
}

// 获取启用的分类列表
export const getEnabledList = () => {
  return request({
    url: '/api/blade-ad/category/enabled',
    method: 'get'
  })
}

// 获取分类树形结构
export const getCategoryTree = () => {
  return request({
    url: '/api/blade-ad/category/tree',
    method: 'get'
  })
}

// 获取分类详情（包含标签）
export const getCategoryDetail = (id) => {
  return request({
    url: `/api/blade-ad/category/detail/${id}`,
    method: 'get'
  })
}

// 启用/禁用分类
export const enableCategory = (id, enabled) => {
  return request({
    url: '/api/blade-ad/category/enable',
    method: 'post',
    params: {
      id,
      enabled
    }
  })
}

// 启用/禁用分类审核
export const enableCategoryAudit = (id, enableAudit) => {
  return request({
    url: '/api/blade-ad/category/enable-audit',
    method: 'post',
    params: {
      id,
      enableAudit
    }
  })
}

// ==================== 标签管理相关接口 ====================

// 获取分类下的标签
export const getCategoryTags = (categoryId) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags`,
    method: 'get'
  })
}

// 添加标签到分类
export const addTagToCategory = (categoryId, tagId) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags`,
    method: 'post',
    params: {
      tagId
    }
  })
}

// 从分类移除标签
export const removeTagFromCategory = (categoryId, tagId) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags/${tagId}`,
    method: 'delete'
  })
}

// 批量添加标签到分类
export const batchAddTagsToCategory = (categoryId, tagIds) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags/batch`,
    method: 'post',
    data: tagIds
  })
}

// 更新分类标签排序
export const updateCategoryTagsSort = (categoryId, tagSorts) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags/sort`,
    method: 'put',
    data: tagSorts
  })
}

// 获取可用标签列表
export const getAvailableTags = () => {
  return request({
    url: '/api/blade-ad/category/tags/available',
    method: 'get'
  })
}

// 搜索标签
export const searchTags = (tagName) => {
  return request({
    url: '/api/blade-ad/category/tags/search',
    method: 'get',
    params: {
      tagName
    }
  })
}

// 创建标签
export const createTag = (categoryId, tagName, color) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags/create`,
    method: 'post',
    params: {
      tagName,
      color
    }
  })
}

// 获取分类标签数量
export const getCategoryTagCount = (categoryId) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags/count`,
    method: 'get'
  })
}

// 检查标签是否属于分类
export const isTagBelongToCategory = (categoryId, tagId) => {
  return request({
    url: `/api/blade-ad/category/${categoryId}/tags/check`,
    method: 'get',
    params: {
      tagId
    }
  })
}

