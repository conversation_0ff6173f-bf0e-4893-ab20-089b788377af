/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.business.post.entity.CategoryTag;

/**
 * 分类标签关联视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分类标签关联视图对象")
public class CategoryTagVO extends CategoryTag {

	@Schema(description = "分类名称")
	private String categoryName;

	@Schema(description = "标签名称")
	private String tagName;

	@Schema(description = "标签颜色")
	private String tagColor;

	@Schema(description = "标签使用次数")
	private Integer tagUseCount;

	@Schema(description = "是否系统标签")
	private Integer tagIsSystem;

	@Schema(description = "创建人姓名")
	private String createUserName;

	@Schema(description = "更新人姓名")
	private String updateUserName;
}
