/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值订单实体类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@TableName("urb_recharge_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "充值订单")
public class RechargeOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 支付配置ID
     */
    @Schema(description = "支付配置ID")
    private Long payConfigId;

    /**
     * 充值金额（单位：元）
     */
    @Schema(description = "充值金额（单位：元）")
    private BigDecimal rechargeAmount;

    /**
     * 支付方式：WECHAT_PAY-微信支付，ALIPAY-支付宝
     */
    @Schema(description = "支付方式")
    private String paymentMethod;

    /**
     * 订单状态：PENDING-待支付，PAID-已支付，SUCCESS-充值成功，FAILED-充值失败，CANCELLED-已取消
     */
    @Schema(description = "订单状态")
    private String orderStatus;

    /**
     * 支付状态：UNPAID-未支付，PAID-已支付，REFUNDED-已退款
     */
    @Schema(description = "支付状态")
    private String paymentStatus;

    /**
     * 微信预支付ID
     */
    @Schema(description = "微信预支付ID")
    private String wxPrepayId;

    /**
     * 微信支付交易号
     */
    @Schema(description = "微信支付交易号")
    private String wxTransactionId;

    /**
     * 商户订单号
     */
    @Schema(description = "商户订单号")
    private String wxOutTradeNo;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    /**
     * 充值成功时间
     */
    @Schema(description = "充值成功时间")
    private LocalDateTime successTime;

    /**
     * 订单过期时间
     */
    @Schema(description = "订单过期时间")
    private LocalDateTime expireTime;

    /**
     * 支付回调地址
     */
    @Schema(description = "支付回调地址")
    private String notifyUrl;

    /**
     * 客户端IP
     */
    @Schema(description = "客户端IP")
    private String clientIp;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    private String deviceInfo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

	/**
     * 订单类型
     */
	@Schema(description = "订单类型")
	private String type;

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        WECHAT_PAY("WECHAT_PAY", "微信支付"),
        ALIPAY("ALIPAY", "支付宝");

        private final String code;
        private final String desc;

        PaymentMethod(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 订单状态枚举
     */
    public enum OrderStatus {
        PENDING("PENDING", "待支付"),
        PAID("PAID", "已支付"),
        SUCCESS("SUCCESS", "充值成功"),
        FAILED("FAILED", "充值失败"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String desc;

        OrderStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 支付状态枚举
     */
    public enum PaymentStatus {
        UNPAID("UNPAID", "未支付"),
        PAID("PAID", "已支付"),
        REFUNDED("REFUNDED", "已退款");

        private final String code;
        private final String desc;

        PaymentStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
