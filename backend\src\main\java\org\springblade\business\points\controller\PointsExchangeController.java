/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.points.entity.PointsExchange;
import org.springblade.business.points.service.IPointsExchangeService;
import org.springblade.business.points.vo.PointsExchangeVO;
import org.springblade.business.points.wrapper.PointsExchangeWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 积分兑换记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/pointsexchange")
@io.swagger.v3.oas.annotations.tags.Tag(name = "积分兑换记录表", description = "积分兑换记录表接口")
public class PointsExchangeController extends BladeController {

	private IPointsExchangeService pointsExchangeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入pointsExchange")
	public R<PointsExchangeVO> detail(PointsExchange pointsExchange) {
		PointsExchange detail = pointsExchangeService.getOne(Condition.getQueryWrapper(pointsExchange));
		return R.data(PointsExchangeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 积分兑换记录表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入pointsExchange")
	public R<IPage<PointsExchangeVO>> list(PointsExchange pointsExchange, Query query) {
		IPage<PointsExchange> pages = pointsExchangeService.page(Condition.getPage(query), Condition.getQueryWrapper(pointsExchange));
		return R.data(PointsExchangeWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 积分兑换记录表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入pointsExchange")
	public R<IPage<PointsExchangeVO>> page(PointsExchangeVO pointsExchange, Query query) {
		IPage<PointsExchangeVO> pages = pointsExchangeService.selectPointsExchangePage(Condition.getPage(query), pointsExchange);
		return R.data(pages);
	}

	/**
	 * 新增 积分兑换记录表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入pointsExchange")
	public R save(@Valid @RequestBody PointsExchange pointsExchange) {
		return R.status(pointsExchangeService.save(pointsExchange));
	}

	/**
	 * 修改 积分兑换记录表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入pointsExchange")
	public R update(@Valid @RequestBody PointsExchange pointsExchange) {
		return R.status(pointsExchangeService.updateById(pointsExchange));
	}

	/**
	 * 新增或修改 积分兑换记录表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入pointsExchange")
	public R submit(@Valid @RequestBody PointsExchange pointsExchange) {
		return R.status(pointsExchangeService.saveOrUpdate(pointsExchange));
	}


	/**
	 * 删除 积分兑换记录表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(pointsExchangeService.deleteLogic(Func.toLongList(ids)));
	}


}
