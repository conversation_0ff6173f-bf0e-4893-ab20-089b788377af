/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 分类标签关联实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_category_tag")
@Schema(description = "分类标签关联")
public class CategoryTag implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	@TableId
	private Long id;
	/**
	 * 分类ID
	 */
	@Schema(description = "分类ID")
	private Long categoryId;

	/**
	 * 标签ID
	 */
	@Schema(description = "标签ID")
	private Long tagId;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sortOrder;


	private Integer type;

}
