/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import org.springblade.business.post.controller.CategoryController;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.AdminTagCreateRequest;
import org.springblade.business.post.vo.CategoryVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 广告分类 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ICategoryService extends BaseService<Category> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param category
	 * @return
	 */
	IPage<CategoryVO> selectCategoryPage(IPage<CategoryVO> page, CategoryVO category);


	/**
	 * 获取分类树形结构
	 *
	 * @return 分类树
	 */
	List<Category> getCategoryTree();

	/**
	 * 根据ID获取分类详情
	 *
	 * @param id 分类ID
	 * @return 分类详情
	 */
	Category getCategoryDetail(Long id);

	/**
	 * 启用/禁用分类
	 *
	 * @param id      分类ID
	 * @param enabled 是否启用
	 * @return 是否成功
	 */
	Boolean enableCategory(Long id, Boolean enabled);

	/**
	 * 启用/禁用分类审核
	 *
	 * @param id          分类ID
	 * @param enableAudit 是否启用审核
	 * @return 是否成功
	 */
	Boolean enableCategoryAudit(Long id, Boolean enableAudit);

	// ==================== 标签管理相关方法 ====================

	/**
	 * 为分类添加标签
	 *
	 * @param categoryId 分类ID
	 * @param tagId      标签ID
	 * @return 是否成功
	 */
	Boolean addTagToCategory(Long categoryId, Long tagId);

	/**
	 * 从分类移除标签
	 *
	 * @param categoryId 分类ID
	 * @param tagId      标签ID
	 * @return 是否成功
	 */
	Boolean removeTagFromCategory(Long categoryId, Long tagId);

	/**
	 * 批量为分类添加标签
	 *
	 * @param categoryId 分类ID
	 * @param tagIds     标签ID列表
	 * @return 是否成功
	 */
	Boolean batchAddTagsToCategory(Long categoryId, List<Long> tagIds);

	/**
	 * 更新分类标签排序
	 *
	 * @param categoryId 分类ID
	 * @param tagSorts   标签排序信息
	 * @return 是否成功
	 */
	Boolean updateCategoryTagsSort(Long categoryId, List<CategoryController.TagSortDTO> tagSorts);

	/**
	 * 获取分类的标签数量
	 *
	 * @param categoryId 分类ID
	 * @return 标签数量
	 */
	Integer getCategoryTagCount(Long categoryId);

	/**
	 * 检查标签是否属于分类
	 *
	 * @param categoryId 分类ID
	 * @param tagId      标签ID
	 * @return 是否属于
	 */
	Boolean isTagBelongToCategory(Long categoryId, Long tagId);

	// 反馈标签管理相关
	List<Tag> getFeedbackTagsByCategory(Long categoryId);

	List<Tag> getAvailableFeedbackTags();

	Boolean addFeedbackTagToCategory(Long categoryId, Long tagId);

	Boolean removeFeedbackTagFromCategory(Long categoryId, Long tagId);

	Tag createFeedbackTag(AdminTagCreateRequest tagReq);

	boolean createCategory(@Valid Category category);

	boolean updateByCategoryId(@Valid Category category);

	boolean saveOrUpdateCategory(@Valid Category category);

	boolean deleteLogicCategory(String ids);
}
