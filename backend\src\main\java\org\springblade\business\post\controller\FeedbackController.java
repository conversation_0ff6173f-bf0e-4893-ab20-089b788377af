/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.post.dto.BatchFeedbackAuditDTO;
import org.springblade.business.post.dto.FeedbackAuditDTO;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.service.IFeedbackService;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.business.post.wrapper.FeedbackWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户反馈 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/feedback")
@io.swagger.v3.oas.annotations.tags.Tag(name = "用户反馈", description = "用户反馈接口")
public class FeedbackController extends BladeController {

	private IFeedbackService feedbackService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入feedback")
	public R<FeedbackVO> detail(Feedback feedback) {
		FeedbackVO detail = feedbackService.getFeedbackDetail(feedback);
		return R.data(detail);
//		Feedback detail = feedbackService.getOne(Condition.getQueryWrapper(feedback));
//		Feedback detail = feedbackService.getFeebackById(feedback);
//		return R.data(FeedbackWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 用户反馈
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入feedback")
	public R<IPage<FeedbackVO>> list(Feedback feedback, Query query) {
//		IPage<Feedback> pages = feedbackService.page(Condition.getPage(query), Condition.getQueryWrapper(feedback));
		IPage<Feedback> feedbackIPage = feedbackService.pageFeedback(feedback, query);
		return R.data(FeedbackWrapper.build().pageVO(feedbackIPage));
	}


	/**
	 * 自定义分页 用户反馈
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入feedback")
	public R<IPage<FeedbackVO>> page(FeedbackVO feedback, Query query) {
		IPage<FeedbackVO> pages = feedbackService.selectFeedbackPage(Condition.getPage(query), feedback);
		return R.data(pages);
	}

	/**
	 * 新增 用户反馈
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入feedback")
	public R save(@Valid @RequestBody Feedback feedback) {
		return R.status(feedbackService.save(feedback));
	}

	/**
	 * 修改 用户反馈
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入feedback")
	public R update(@Valid @RequestBody Feedback feedback) {
		return R.status(feedbackService.updateById(feedback));
	}

	/**
	 * 新增或修改 用户反馈
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入feedback")
	public R submit(@Valid @RequestBody Feedback feedback) {
		return R.status(feedbackService.saveOrUpdate(feedback));
	}


	/**
	 * 删除 用户反馈
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(feedbackService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 审核反馈
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 13)
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "审核反馈", description = "传入反馈审核信息")
	public R auditFeedback(@Valid @RequestBody FeedbackAuditDTO feedbackAuditDTO) {
		return R.status(feedbackService.auditFeedback(feedbackAuditDTO));
	}

	/**
	 * 批量审核反馈
	 */
	@PostMapping("/batch-audit")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@ApiOperationSupport(order = 14)
	@Operation(summary = "批量审核反馈", description = "传入批量反馈审核信息")
	public R batchAuditFeedback(@Valid @RequestBody BatchFeedbackAuditDTO batchFeedbackAuditDTO) {
		return R.status(feedbackService.batchAuditFeedback(batchFeedbackAuditDTO));
	}

	/**
	 * 获取反馈审核统计
	 */
	@GetMapping("/audit-stats")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@ApiOperationSupport(order = 15)
	@Operation(summary = "获取反馈审核统计", description = "获取反馈审核统计数据")
	public R<Map<String, Object>> getFeedbackAuditStats() {
		return R.data(feedbackService.getFeedbackAuditStats());
	}
}
