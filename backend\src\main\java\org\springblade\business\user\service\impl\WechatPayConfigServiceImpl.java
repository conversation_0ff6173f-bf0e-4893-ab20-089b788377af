/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import com.github.binarywang.wxpay.service.WxPayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.config.WechatPayConfigManager;
import org.springblade.business.user.entity.WechatPayConfig;
import org.springblade.business.user.mapper.WechatPayConfigMapper;
import org.springblade.business.user.service.IWechatPayConfigService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 微信支付配置服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class WechatPayConfigServiceImpl implements IWechatPayConfigService {

    private final WechatPayConfigMapper wechatPayConfigMapper;
    private final WechatPayConfigManager.WechatPayServiceFactory wechatPayServiceFactory;

    /**
     * 微信支付服务缓存
     */
    private final ConcurrentHashMap<String, WxPayService> wxPayServiceCache = new ConcurrentHashMap<>();

    @Override
    public WechatPayConfig getDefaultConfig() {
        WechatPayConfig config = wechatPayConfigMapper.selectDefaultConfig();
        if (config == null) {
            // 如果没有默认配置，返回最佳配置
            config = getBestConfig();
        }

        // 如果数据库中没有配置，创建一个临时的默认配置
        if (config == null) {
            log.warn("数据库中未找到微信支付配置，使用临时默认配置");
            config = createTemporaryDefaultConfig();
        }

        return config;
    }

    @Override
    public WechatPayConfig getConfigByName(String configName) {
        if (configName == null || configName.trim().isEmpty()) {
            return getDefaultConfig();
        }
        return wechatPayConfigMapper.selectByConfigName(configName);
    }

    @Override
    public WechatPayConfig getConfigByMchId(String mchId) {
        if (mchId == null || mchId.trim().isEmpty()) {
            return null;
        }
        return wechatPayConfigMapper.selectByMchId(mchId);
    }

    @Override
    public List<WechatPayConfig> getEnabledConfigs() {
        return wechatPayConfigMapper.selectEnabledConfigs();
    }

    @Override
    public WechatPayConfig getBestConfig() {
        return wechatPayConfigMapper.selectBestConfig();
    }

    @Override
    public WxPayService createWxPayService(WechatPayConfig config) {
        if (config == null || !validateConfig(config)) {
            log.warn("微信支付配置无效，无法创建支付服务");
            return null;
        }

        try {
            return wechatPayServiceFactory.createWxPayService(config);
        } catch (Exception e) {
            log.error("创建微信支付服务失败: configName={}", config.getConfigName(), e);
            return null;
        }
    }

    @Override
    public WxPayService getDefaultWxPayService() {
        WechatPayConfig config = getDefaultConfig();
        if (config == null) {
            log.warn("未找到默认微信支付配置");
            return null;
        }

        return getWxPayServiceFromCache(config);
    }

    @Override
    public WxPayService getWxPayServiceByName(String configName) {
        WechatPayConfig config = getConfigByName(configName);
        if (config == null) {
            log.warn("未找到微信支付配置: configName={}", configName);
            return null;
        }

        return getWxPayServiceFromCache(config);
    }

    @Override
    public boolean validateConfig(WechatPayConfig config) {
        return wechatPayServiceFactory.validateConfig(config);
    }

    @Override
    public boolean testConfig(WechatPayConfig config) {
        if (!validateConfig(config)) {
            return false;
        }

        try {
            WxPayService wxPayService = createWxPayService(config);
            if (wxPayService == null) {
                return false;
            }

            // 这里可以调用微信支付的测试接口，比如查询订单接口
            // 由于没有具体的测试订单，这里简单返回true
            // 实际项目中可以调用 wxPayService.queryOrder() 等方法进行测试

            log.info("微信支付配置测试通过: configName={}", config.getConfigName());
            return true;

        } catch (Exception e) {
            log.error("微信支付配置测试失败: configName={}", config.getConfigName(), e);
            return false;
        }
    }

    /**
     * 从缓存中获取微信支付服务
     *
     * @param config 支付配置
     * @return 微信支付服务
     */
    private WxPayService getWxPayServiceFromCache(WechatPayConfig config) {
        String cacheKey = config.getConfigName();

        return wxPayServiceCache.computeIfAbsent(cacheKey, key -> {
            log.info("创建并缓存微信支付服务: configName={}", config.getConfigName());
            return createWxPayService(config);
        });
    }

    /**
     * 清除缓存中的微信支付服务
     *
     * @param configName 配置名称
     */
    public void clearWxPayServiceCache(String configName) {
        if (configName != null) {
            wxPayServiceCache.remove(configName);
            log.info("清除微信支付服务缓存: configName={}", configName);
        }
    }

    /**
     * 清除所有缓存
     */
    public void clearAllWxPayServiceCache() {
        wxPayServiceCache.clear();
        log.info("清除所有微信支付服务缓存");
    }

    /**
     * 创建临时默认配置
     * 当数据库中没有配置时使用
     *
     * @return 临时默认配置
     */
    private WechatPayConfig createTemporaryDefaultConfig() {
        WechatPayConfig config = new WechatPayConfig();
        config.setConfigName("temporary_default");
        config.setAppId("wx5f0591468a438c48");
        config.setMchId("1722243412");
        config.setApiKey("kqhkiG9w0BBQ0wMzAbBgkqhkiG9w0BBQ"); // 临时密钥，需要替换为真实密钥
        config.setApiV3Key("asdqweqwezxc12312312313233215AA2");
        config.setCertSerialNo("2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E");
        config.setCertPath("classpath:cert/apiclient_cert.pem");
        config.setPrivateKeyPath("classpath:cert/apiclient_key.pem");
        config.setNotifyUrl("https://yourdomain.com/api/pay/notify");
        config.setIsSandbox(false);
        config.setIsEnabled(true);
        config.setIsDefault(true);
        config.setPriority(100);
        config.setRemark("临时默认配置，请尽快在数据库中配置正确的支付参数");

        log.warn("使用临时微信支付配置，请尽快执行 init_wechat_pay_config.sql 初始化数据库配置");
        return config;
    }
}
