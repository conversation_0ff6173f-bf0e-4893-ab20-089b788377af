/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.service.impl;


import jakarta.annotation.Resource;
import org.springblade.business.subscriptionPlan.entity.Feature;
import org.springblade.business.subscriptionPlan.entity.UserFeaturePermission;
import org.springblade.business.subscriptionPlan.entity.UserSubscription;
import org.springblade.business.subscriptionPlan.mapper.FeatureMapper;
import org.springblade.business.subscriptionPlan.mapper.UserFeaturePermissionMapper;
import org.springblade.business.subscriptionPlan.mapper.UserSubscriptionMapper;
import org.springblade.business.subscriptionPlan.service.IUserSubscriptionService;
import org.springblade.business.subscriptionPlan.vo.UserSubscriptionVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户订阅表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class UserSubscriptionServiceImpl extends BaseServiceImpl<UserSubscriptionMapper, UserSubscription> implements IUserSubscriptionService {

	@Resource
	private UserFeaturePermissionMapper userFeaturePermissionMapper;
	@Resource
	private FeatureMapper featureMapper;
	@Resource
	private UserFeaturePermissionServiceImpl userFeaturePermissionService;

	@Override
	public IPage<UserSubscriptionVO> selectUserSubscriptionPage(IPage<UserSubscriptionVO> page, UserSubscriptionVO userSubscription) {
		return page.setRecords(baseMapper.selectUserSubscriptionPage(page, userSubscription));
	}

	@Override
	@Transactional
	public Boolean saveUserSubscription(UserSubscription userSubscription) {
		//保存用户绑定的订阅
		this.save(userSubscription);
		//同步保存用户和功能的绑定
		//先查询到这个订阅计划下的所有功能
		List<Feature> features = featureMapper.selectFeaturesByPlanId(userSubscription.getId());  //主键回显
		// 构建权限列表
		List<UserFeaturePermission> permissions = features.stream()
			.map(feature -> {
				UserFeaturePermission permission = new UserFeaturePermission();
				permission.setUserId(userSubscription.getUserId());  //这里用传进来的用户ID
				permission.setFeatureId(feature.getId());
				permission.setSubscriptionId(userSubscription.getPlanId());
				permission.setExpireTime(userSubscription.getEndTime());
				permission.setFeatureCode(feature.getFeatureCode());  //FeedCode作为判断用户有没有功能的标识
				return permission;
			})
			.collect(Collectors.toList());
		// 批量插入
		userFeaturePermissionService.saveBatch(permissions);
		return true;
	}

}
