package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 帖子统计数据信息
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/24
 */@Data
@Schema(description = "帖子统计信息")
public class PostStatsDTO {

	private Boolean isLiked;  // 是否点赞
	private Boolean isFavorite;  // 是否收藏
	/**
	 * 点赞数
	 */
	@Schema(description = "点赞数")
	private Integer likeCount;

	@Schema(description = "反馈数量")
	private Integer feedbackCount;
	/**
	 * 收藏数
	 */
	@Schema(description = "收藏数")
	private Integer favoriteCount;

	private Integer viewCount;



}
