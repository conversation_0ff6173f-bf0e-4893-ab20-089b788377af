<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.usersubscription_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
        <el-button type="warning"
                   icon="el-icon-time"
                   plain
                   @click="handleBatchRenew">批量续费
        </el-button>
      </template>

      <!-- 用户ID插槽 -->
      <template #userId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.userId)"
          class="user-link">
          {{ row.userId }}
        </el-button>
      </template>

      <!-- 支付金额插槽 -->
      <template #paymentAmount="{ row }">
        <span class="amount-text">
          ¥{{ row.paymentAmount }}
        </span>
      </template>

      <!-- 支付状态插槽 -->
      <template #paymentStatus="{ row }">
        <el-tag :type="getPaymentStatusType(row.paymentStatus)">
          {{ getPaymentStatusText(row.paymentStatus) }}
        </el-tag>
      </template>

      <!-- 支付方式插槽 -->
      <template #paymentMethod="{ row }">
        <el-tag :type="getPaymentMethodType(row.paymentMethod)">
          {{ getPaymentMethodText(row.paymentMethod) }}
        </el-tag>
      </template>

      <!-- 自动续费插槽 -->
      <template #autoRenew="{ row }">
        <el-tag :type="row.autoRenew ? 'success' : 'info'">
          {{ row.autoRenew ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 订阅状态插槽 -->
      <template #subscriptionStatus="{ row }">
        <el-tag :type="getSubscriptionStatusType(row)">
          {{ getSubscriptionStatusText(row) }}
        </el-tag>
      </template>

      <!-- 操作菜单插槽 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewSubscriptionDetail(row)">
          详情
        </el-button>
        <el-button
          v-if="isExpiringSoon(row)"
          type="warning"
          size="small"
          @click="handleRenew(row)">
          续费
        </el-button>
        <el-button
          v-if="row.autoRenew"
          type="info"
          size="small"
          @click="handleCancelAutoRenew(row)">
          取消自动续费
        </el-button>
      </template>
    </avue-crud>

    <!-- 订阅详情对话框 -->
    <el-dialog v-model="subscriptionDetailVisible" title="用户订阅详情" width="800px">
      <div v-if="selectedSubscription">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedSubscription.userId }}</el-descriptions-item>
          <el-descriptions-item label="订阅计划">{{ selectedSubscription.planName || selectedSubscription.planId }}</el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span class="amount-text">¥{{ selectedSubscription.paymentAmount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag :type="getPaymentMethodType(selectedSubscription.paymentMethod)">
              {{ getPaymentMethodText(selectedSubscription.paymentMethod) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusType(selectedSubscription.paymentStatus)">
              {{ getPaymentStatusText(selectedSubscription.paymentStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订阅状态">
            <el-tag :type="getSubscriptionStatusType(selectedSubscription)">
              {{ getSubscriptionStatusText(selectedSubscription) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ selectedSubscription.startTime }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ selectedSubscription.endTime }}</el-descriptions-item>
          <el-descriptions-item label="自动续费">
            <el-tag :type="selectedSubscription.autoRenew ? 'success' : 'info'">
              {{ selectedSubscription.autoRenew ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="交易流水号">{{ selectedSubscription.transactionId || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedSubscription.cancelReason">
          <el-divider />
          <h4>取消原因：</h4>
          <p>{{ selectedSubscription.cancelReason }}</p>
        </div>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/usersubscription";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        subscriptionDetailVisible: false,
        selectedSubscription: null,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: true,
          editBtn: true,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "用户ID",
              prop: "userId",
              width: 120,
              search: true,
              slot: true,
              rules: [{
                required: true,
                message: "请输入用户ID",
                trigger: "blur"
              }]
            },
            {
              label: "订阅计划",
              prop: "planId",
              width: 120,
              search: true,
              rules: [{
                required: true,
                message: "请选择订阅计划",
                trigger: "blur"
              }]
            },
            {
              label: "支付金额",
              prop: "paymentAmount",
              type: "number",
              width: 120,
              sortable: true,
              slot: true,
              rules: [{
                required: true,
                message: "请输入支付金额",
                trigger: "blur"
              }]
            },
            {
              label: "支付状态",
              prop: "paymentStatus",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '待支付', value: 'PENDING' },
                { label: '已支付', value: 'PAID' },
                { label: '支付失败', value: 'FAILED' },
                { label: '已退款', value: 'REFUNDED' }
              ],
              rules: [{
                required: true,
                message: "请选择支付状态",
                trigger: "blur"
              }]
            },
            {
              label: "支付方式",
              prop: "paymentMethod",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '微信', value: 'WECHAT' },
                { label: '支付宝', value: 'ALIPAY' },
                { label: '余额', value: 'BALANCE' }
              ],
              rules: [{
                required: true,
                message: "请选择支付方式",
                trigger: "blur"
              }]
            },
            {
              label: "订阅状态",
              prop: "subscriptionStatus",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '有效', value: 'ACTIVE' },
                { label: '已过期', value: 'EXPIRED' },
                { label: '即将过期', value: 'EXPIRING' }
              ]
            },
            {
              label: "开始时间",
              prop: "startTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "结束时间",
              prop: "endTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "自动续费",
              prop: "autoRenew",
              type: "switch",
              width: 100,
              search: true,
              slot: true,
              dicData: [
                { label: '是', value: true },
                { label: '否', value: false }
              ]
            },
            {
              label: "交易流水号",
              prop: "transactionId",
              width: 200,
              search: true,
              hide: true
            },
            {
              label: "取消原因",
              prop: "cancelReason",
              type: "textarea",
              hide: true
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.usersubscription_add, false),
          viewBtn: this.validData(this.permission.usersubscription_view, false),
          delBtn: this.validData(this.permission.usersubscription_delete, false),
          editBtn: this.validData(this.permission.usersubscription_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 排序处理
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      // 支付状态相关方法
      getPaymentStatusType(status) {
        const statusMap = {
          'PENDING': 'warning',
          'PAID': 'success',
          'FAILED': 'danger',
          'REFUNDED': 'info'
        };
        return statusMap[status] || 'info';
      },

      getPaymentStatusText(status) {
        const statusMap = {
          'PENDING': '待支付',
          'PAID': '已支付',
          'FAILED': '支付失败',
          'REFUNDED': '已退款'
        };
        return statusMap[status] || '未知';
      },

      // 支付方式相关方法
      getPaymentMethodType(method) {
        const methodMap = {
          'WECHAT': 'success',
          'ALIPAY': 'primary',
          'BALANCE': 'warning'
        };
        return methodMap[method] || 'info';
      },

      getPaymentMethodText(method) {
        const methodMap = {
          'WECHAT': '微信',
          'ALIPAY': '支付宝',
          'BALANCE': '余额'
        };
        return methodMap[method] || '未知';
      },

      // 订阅状态相关方法
      getSubscriptionStatusType(row) {
        if (!row.endTime) return 'info';
        const now = new Date();
        const endTime = new Date(row.endTime);
        const diffDays = Math.ceil((endTime - now) / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return 'danger'; // 已过期
        if (diffDays <= 7) return 'warning'; // 即将过期
        return 'success'; // 有效
      },

      getSubscriptionStatusText(row) {
        if (!row.endTime) return '未知';
        const now = new Date();
        const endTime = new Date(row.endTime);
        const diffDays = Math.ceil((endTime - now) / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return '已过期';
        if (diffDays <= 7) return `即将过期(${diffDays}天)`;
        return '有效';
      },

      // 判断是否即将过期
      isExpiringSoon(row) {
        if (!row.endTime) return false;
        const now = new Date();
        const endTime = new Date(row.endTime);
        const diffDays = Math.ceil((endTime - now) / (1000 * 60 * 60 * 24));
        return diffDays >= 0 && diffDays <= 30; // 30天内过期
      },

      // 查看订阅详情
      viewSubscriptionDetail(row) {
        this.selectedSubscription = row;
        this.subscriptionDetailVisible = true;
      },

      // 显示用户详情
      showUserDetail(userId) {
        if (!userId) {
          this.$message.warning('用户信息不存在');
          return;
        }
        this.$message.info(`查看用户详情: ${userId}`);
        // 这里可以跳转到用户详情页面
      },

      // 续费
      handleRenew(row) {
        this.$confirm('确定要为此用户续费吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用续费的API
          this.$message.success('续费成功');
          this.onLoad(this.page);
        });
      },

      // 取消自动续费
      handleCancelAutoRenew(row) {
        this.$confirm('确定要取消自动续费吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用取消自动续费的API
          this.$message.success('取消自动续费成功');
          this.onLoad(this.page);
        });
      },

      // 批量续费
      handleBatchRenew() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm('确定要为选中的用户批量续费吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用批量续费的API
          this.$message.success('批量续费成功');
          this.onLoad(this.page);
          this.$refs.crud.toggleSelection();
        });
      },

      // 刷新数据
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },

      // 导出数据
      handleExport() {
        this.$message.info('导出功能开发中...');
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error('加载数据失败');
        });
      }
    }
  };
</script>

<style scoped>
/* 金额显示样式 */
.amount-text {
  color: #f56c6c;
  font-weight: bold;
  font-size: 14px;
}

/* 用户链接样式 */
.user-link {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
}

.user-link:hover {
  color: #66b1ff;
}

/* 状态标签样式 */
.el-tag {
  font-size: 12px;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 搜索区域样式优化 */
:deep(.avue-crud__search) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
}
</style>
