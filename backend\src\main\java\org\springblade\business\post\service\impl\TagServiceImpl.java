/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springblade.business.post.controller.TagController;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.AdminTagCreateRequest;
import org.springblade.business.post.service.ICategoryTagService;
import org.springblade.business.post.service.ITagService;
import org.springblade.business.post.vo.TagVO;
import org.springblade.business.user.mapper.TagMapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_TAGS;

/**
 * 信息标签 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@AllArgsConstructor
public class TagServiceImpl extends BaseServiceImpl<TagMapper, Tag> implements ITagService {

	@Resource
	private TagMapper tagMapper;

	private ICategoryTagService categoryTagService;

	@Override
	public IPage<TagVO> selectTagPage(IPage<TagVO> page, TagVO tag) {
		return page.setRecords(baseMapper.selectTagPage(page, tag));
	}

	@Override
	public List<Tag> getTagsByCategory(Long categoryId) {
		LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tag::getCategoryId, categoryId)
			.eq(Tag::getEnabled, 1)
			.orderByAsc(Tag::getSort)
			.orderByDesc(Tag::getUseCount)
			.orderByAsc(Tag::getId);
		return list(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Tag createTag(String tagName, String color, Long categoryId) {
		// 检查标签是否已存在
		LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tag::getTagName, tagName)
			.eq(Tag::getCategoryId, categoryId);
		Tag existingTag = getOne(queryWrapper);

		if (existingTag != null) {
			// 如果标签已存在，增加使用次数
			existingTag.setUseCount(existingTag.getUseCount() + 1);
			updateById(existingTag);
			return existingTag;
		}

		// 创建新标签
		Tag tag = new Tag();
		tag.setTagName(tagName);
		tag.setCategoryId(categoryId);
		tag.setColor("#1890ff"); // 默认颜色
		tag.setSort(0);
		tag.setEnabled(1);
		tag.setUseCount(1);
		tag.setIsSystem(0); // 用户创建的标签

		save(tag);
		return tag;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@Caching(evict = {
		@CacheEvict(value = WECHAT_CATEGORY_TAGS, key = "#categoryId")
	})
	public Boolean enableTag(Long id, Boolean enabled) {
		Tag tag = getById(id);
		if (tag == null) {
			return false;
		}
		tag.setEnabled(enabled ? 1 : 0);
		return updateById(tag);
	}

	@Override
	public List<Tag> getHotTags(Integer limit) {
		LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tag::getEnabled, 1)
			.orderByDesc(Tag::getUseCount)
			.last("LIMIT " + limit);
		return list(queryWrapper);
	}

	@Override
	public List<Tag> getAvailableTags() {
		LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tag::getEnabled, 1)
			.orderByAsc(Tag::getSort)
			.orderByDesc(Tag::getUseCount)
			.orderByAsc(Tag::getId);
		return list(queryWrapper);
	}

	@Override
	public List<Tag> searchTagsByName(String tagName) {
		if (StrUtil.isBlank(tagName)) {
			return getAvailableTags();
		}

		LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tag::getEnabled, 1)
			.like(Tag::getTagName, tagName)
			.orderByAsc(Tag::getSort)
			.orderByDesc(Tag::getUseCount)
			.orderByAsc(Tag::getId);
		return list(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateTagUseCount(List<String> tagIds) {
		if (CollUtil.isEmpty(tagIds)) {
			return;
		}

		for (String tagId : tagIds) {
			Tag tag = getById(tagId);
			if (tag != null) {
				tag.setUseCount(tag.getUseCount() + 1);
				updateById(tag);
			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@Caching(evict = {
		@CacheEvict(value = WECHAT_CATEGORY_TAGS, key = "#tagReq.categoryId")
	})
	public Tag createTag(AdminTagCreateRequest tagReq) {
		// 检查标签是否已存在
		LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tag::getTagName, tagReq.getTagName())
			.eq(Tag::getCategoryId, tagReq.getCategoryId())
			.eq(Tag::getType, tagReq.getType());
		Tag existingTag = getOne(queryWrapper);

		if (existingTag != null) {
			// 如果标签已存在，增加使用次数
			existingTag.setUseCount(existingTag.getUseCount() + 1);
			updateById(existingTag);
			return existingTag;
		}

		// 创建新标签
		Tag tag = new Tag();
		tag.setTagName(tagReq.getTagName());
		tag.setCategoryId(tagReq.getCategoryId());
		tag.setColor(tagReq.getColor()); // 默认颜色
		tag.setSort(tagReq.getSortOrder());
		tag.setEnabled(1);
		tag.setUseCount(1);
		tag.setIsSystem(0); // 用户创建的标签
		tag.setType(tagReq.getType());
		save(tag);
		//添加标签到分类
		TagController.TagCreateRequest tagCreateRequest = new TagController.TagCreateRequest();
		tagCreateRequest.setCategoryId(tagReq.getCategoryId());
		tagCreateRequest.setTagId(tag.getId());
		tagCreateRequest.setType(tagReq.getType());
		categoryTagService.addTagToCategory(tagCreateRequest);
		return tag;
	}

	@Override
	public List<Tag> getTagsByCategoryAndType(Long categoryId, int i) {
		LambdaQueryWrapper<Tag> wp = new LambdaQueryWrapper<>();
		wp
			.eq(Tag::getCategoryId, categoryId)
			.eq(Tag::getType, i);
		return tagMapper.selectList(wp);
	}

	@Override
	public List<Tag> getTagsByType(int i) {
		LambdaQueryWrapper<Tag> wp = new LambdaQueryWrapper<>();

		wp.eq(Tag::getType, i);
		return tagMapper.selectList(wp);
	}



    @Override
	@Transactional(rollbackFor = Exception.class)
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_TAGS,allEntries = true)
	})
    public boolean removeClearBind(List<Long> ids) {
		categoryTagService.removeByTags(ids);
		return deleteLogic(ids);
    }

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_TAGS,allEntries = true)
	})
	public boolean saveTag(Tag tag) {
		return this.save(tag);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_TAGS,allEntries = true)
	})
	public boolean updateTagById(Tag tag) {
		return this.updateById(tag);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_TAGS,allEntries = true)
	})
	public boolean saveOrUpdateTag(Tag tag) {
		return this.saveOrUpdate(tag);
	}
}
