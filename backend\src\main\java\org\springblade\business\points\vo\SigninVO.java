package org.springblade.business.points.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 签到VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "签到VO")
public class SigninVO {

    /**
     * 签到ID
     */
    @Schema(description = "签到ID")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户Id")
    private String userId;

    /**
     * 签到日期
     */
    @Schema(description = "签到日期")
    private LocalDate signinDate;

    /**
     * 签到时间
     */
    @Schema(description = "签到时间")
    private LocalDateTime signinTime;

    /**
     * 获得积分
     */
    @Schema(description = "获得积分")
    private Integer points;

    /**
     * 连续签到奖励积分
     */
    @Schema(description = "连续签到奖励积分")
    private Integer continuousReward;

    /**
     * 连续签到天数
     */
    @Schema(description = "连续签到天数")
    private Integer continuousDays;

    /**
     * 总签到天数
     */
    @Schema(description = "总签到天数")
    private Integer totalSigninDays;

    /**
     * 当前积分余额
     */
    @Schema(description = "当前积分余额")
    private Integer currentPoints;

    /**
     * 签到类型
     */
    @Schema(description = "签到类型")
    private String signinType;

    /**
     * 是否今日已签到
     */
    @Schema(description = "是否今日已签到")
    private Boolean todaySigned;

    /**
     * 最后签到时间
     */
    @Schema(description = "最后签到时间")
    private String lastSigninTime;
}
