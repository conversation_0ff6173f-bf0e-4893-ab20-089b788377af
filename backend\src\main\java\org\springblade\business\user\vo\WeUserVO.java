/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.business.user.entity.WeUser;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 用户信息视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户信息")
public class WeUserVO extends WeUser {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 发帖数量
	 */
	@Schema(description = "发帖数量")
	private Long postCount;

	/**
	 * 最后活跃时间
	 */
	@Schema(description = "最后活跃时间")
	private String lastActiveTime;

	/**
	 * 签到天数
	 */
	@Schema(description = "签到天数")
	private Integer signInDays;

	/**
	 * 积分数量
	 */
	@Schema(description = "积分数量")
	private Integer points;


	/**
	 * 最后登录时间
	 */
	@Schema(description = "最后登录时间")
	private LocalDateTime lastLoginTime;
}
