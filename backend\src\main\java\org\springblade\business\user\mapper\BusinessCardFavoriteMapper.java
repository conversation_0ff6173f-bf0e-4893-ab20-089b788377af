package org.springblade.business.user.mapper;

import org.apache.ibatis.annotations.Param;
import org.springblade.business.user.entity.BusinessCardFavorite;
import org.springblade.business.user.vo.BusinessCardFavoriteVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.user.vo.BusinessCardVO;

import java.util.List;
import java.util.Map;

/**
 * 名片收藏表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface BusinessCardFavoriteMapper extends BaseMapper<BusinessCardFavorite> {

    /**
     * 分页查询公开名片列表
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 名片列表
     */
    IPage<BusinessCardVO> selectPublicCardList(IPage page, @Param("params") Map<String, Object> params);

    /**
     * 分页查询我的收藏名片列表
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 收藏名片列表
     */
    IPage<BusinessCardFavoriteVO> selectMyFavoriteCardList(IPage page, @Param("params") Map<String, Object> params);

    /**
     * 删除名片收藏扩展信息
     *
     * @param params 删除参数
     * @return 影响行数
     */
    int deleteCardFavoriteExt(@Param("params") Map<String, Object> params);

    /**
     * 获取用户的收藏分类列表
     *
     * @param userId 用户ID
     * @return 收藏分类列表
     */
    List<String> selectFavoriteCategories(@Param("userId") Long userId);

    /**
     * 更新名片收藏扩展信息
     *
     * @param params 更新参数
     * @return 影响行数
     */
    int updateCardFavoriteExt(@Param("params") Map<String, Object> params);

    /**
     * 获取名片统计信息
     *
     * @param cardId 名片ID
     * @return 统计信息
     */
    Map<String, Object> getCardStats(@Param("cardId") Long cardId);
}
