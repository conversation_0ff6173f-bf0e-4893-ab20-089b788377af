/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.post.dto.BatchFeedbackAuditDTO;
import org.springblade.business.post.dto.FeedbackAuditDTO;
import org.springblade.business.post.entity.Feedback;
import org.springblade.core.mp.support.Query;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 用户反馈 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface IFeedbackService extends BaseService<Feedback> {


	IPage<FeedbackVO> selectFeedbackPage(IPage<FeedbackVO> page, FeedbackVO feedback);

	boolean removeByUserId(List<Long> longList);

	IPage<Feedback> pageFeedback(Feedback feedback, Query query);

	Feedback getFeebackById(Feedback feedback);

	boolean addTag(Tag tag);

	List<Map<String, Object>> getTagsByCategory(Long categoryId);

	String getHotTags();

	/**
	 * 移除反馈标签
	 *
	 * @param categoryId 分类ID
	 * @param tagId      标签ID
	 * @return 是否成功
	 */
	boolean removeTag(Long categoryId, Long tagId);

	/**
	 * 获取所有反馈标签
	 *
	 * @return 反馈标签列表
	 */
	List<Map<String, Object>> getAllFeedbackTags();

	/**
	 * 审核反馈
	 *
	 * @param feedbackAuditDTO 反馈审核DTO
	 * @return 是否成功
	 */
	Boolean auditFeedback(FeedbackAuditDTO feedbackAuditDTO);

	/**
	 * 批量审核反馈
	 *
	 * @param batchFeedbackAuditDTO 批量反馈审核DTO
	 * @return 是否成功
	 */
	Boolean batchAuditFeedback(BatchFeedbackAuditDTO batchFeedbackAuditDTO);

	/**
	 * 获取反馈审核统计
	 *
	 * @return 审核统计数据
	 */
	Map<String, Object> getFeedbackAuditStats();

	/**
	 * 获取反馈详情（包含帖子信息）
	 *
	 * @param feedback 反馈查询条件
	 * @return 反馈详情
	 */
	FeedbackVO getFeedbackDetail(Feedback feedback);
}
