package org.springblade.business.post.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 数据记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Mapper
public interface DataRecordMapper {

	/**
	 * 分页查询浏览记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @param type 类型
	 * @return 浏览记录分页数据
	 */
	IPage<Map<String, Object>> selectViewRecords(Page<Map<String, Object>> page, @Param("relevancyId") Long relevancyId, @Param("type") String type);

	/**
	 * 分页查询点赞记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @param type 类型
	 * @return 点赞记录分页数据
	 */
	IPage<Map<String, Object>> selectLikeRecords(Page<Map<String, Object>> page, @Param("relevancyId") Long relevancyId, @Param("type") String type);

	/**
	 * 分页查询反馈记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @return 反馈记录分页数据
	 */
	IPage<Map<String, Object>> selectFeedbackRecords(Page<Map<String, Object>> page, @Param("relevancyId") Long relevancyId);

	/**
	 * 分页查询收藏记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @param type 类型
	 * @return 收藏记录分页数据
	 */
	IPage<Map<String, Object>> selectFavoriteRecords(Page<Map<String, Object>> page, @Param("relevancyId") Long relevancyId, @Param("type") String type);

}
