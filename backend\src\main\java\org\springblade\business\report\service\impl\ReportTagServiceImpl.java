/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.service.impl;

import org.springblade.business.report.entity.ReportTag;
import org.springblade.business.report.vo.ReportTagVO;
import org.springblade.business.report.mapper.ReportTagMapper;
import org.springblade.business.report.service.IReportTagService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 举报标签 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class ReportTagServiceImpl extends BaseServiceImpl<ReportTagMapper, ReportTag> implements IReportTagService {

	@Override
	public IPage<ReportTagVO> selectReportTagPage(IPage<ReportTagVO> page, ReportTagVO reportTag) {
		return page.setRecords(baseMapper.selectReportTagPage(page, reportTag));
	}

}
