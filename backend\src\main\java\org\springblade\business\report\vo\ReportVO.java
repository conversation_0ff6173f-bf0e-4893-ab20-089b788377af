/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.business.report.entity.Report;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.dto.PostStatsDTO;
import org.springblade.business.report.dto.ReportUserDTO;
import org.springblade.business.report.dto.ReportedUserDTO;
import org.springblade.business.report.dto.PostCategoryDTO;

import java.io.Serial;

/**
 * 举报记录视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "举报记录")
public class ReportVO extends Report {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 被举报的帖子信息
	 */
	@Schema(description = "被举报的帖子信息")
	private SupPost post;

	/**
	 * 帖子统计信息（点赞、浏览、收藏等）
	 */
	@Schema(description = "帖子统计信息")
	private PostStatsDTO postStats;

	/**
	 * 举报用户信息
	 */
	@Schema(description = "举报用户信息")
	private ReportUserDTO reportUser;

	/**
	 * 被举报用户信息
	 */
	@Schema(description = "被举报用户信息")
	private ReportedUserDTO reportedUser;

	/**
	 * 帖子分类信息
	 */
	@Schema(description = "帖子分类信息")
	private PostCategoryDTO postCategory;

}
