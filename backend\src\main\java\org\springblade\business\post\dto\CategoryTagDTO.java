/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 分类标签关联数据传输对象
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@Schema(description = "分类标签关联数据传输对象")
public class CategoryTagDTO {

	@Schema(description = "分类ID")
	@NotNull(message = "分类ID不能为空")
	private Long categoryId;

	@Schema(description = "标签ID")
	@NotNull(message = "标签ID不能为空")
	private Long tagId;

	@Schema(description = "排序")
	private Integer sortOrder;

	@Schema(description = "标签ID列表（批量操作）")
	private List<Long> tagIds;

	@Schema(description = "标签排序信息")
	private List<TagSortDTO> tagSorts;

	/**
	 * 标签排序DTO
	 */
	@Data
	@Schema(description = "标签排序信息")
	public static class TagSortDTO {
		@Schema(description = "标签ID")
		@NotNull(message = "标签ID不能为空")
		private Long tagId;

		@Schema(description = "排序")
		private Integer sortOrder;
	}
}
