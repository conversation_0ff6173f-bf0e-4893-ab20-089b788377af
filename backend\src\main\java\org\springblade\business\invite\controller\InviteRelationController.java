/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.invite.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.entity.InviteRelation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.invite.vo.InviteRelationVO;
import org.springblade.business.invite.wrapper.InviteRelationWrapper;
import org.springblade.business.invite.service.IInviteRelationService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 邀请关系表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/inviterelation")
@io.swagger.v3.oas.annotations.tags.Tag(name = "邀请关系表", description = "邀请关系表接口")
public class InviteRelationController extends BladeController {

	private IInviteRelationService inviteRelationService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入inviteRelation")
	public R<InviteRelationVO> detail(InviteRelation inviteRelation) {
		InviteRelation detail = inviteRelationService.getOne(Condition.getQueryWrapper(inviteRelation));
		return R.data(InviteRelationWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 邀请关系表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入inviteRelation")
	public R<IPage<InviteRelationVO>> list(InviteRelation inviteRelation, Query query) {
		IPage<InviteRelation> pages = inviteRelationService.page(Condition.getPage(query), Condition.getQueryWrapper(inviteRelation));
		return R.data(InviteRelationWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 邀请关系表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入inviteRelation")
	public R<IPage<InviteRelationVO>> page(InviteRelationVO inviteRelation, Query query) {
		IPage<InviteRelationVO> pages = inviteRelationService.selectInviteRelationPage(Condition.getPage(query), inviteRelation);
		return R.data(pages);
	}

	/**
	 * 新增 邀请关系表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入inviteRelation")
	public R save(@Valid @RequestBody InviteRelation inviteRelation) {
		return R.status(inviteRelationService.save(inviteRelation));
	}

	/**
	 * 修改 邀请关系表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入inviteRelation")
	public R update(@Valid @RequestBody InviteRelation inviteRelation) {
		return R.status(inviteRelationService.updateById(inviteRelation));
	}

	/**
	 * 新增或修改 邀请关系表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入inviteRelation")
	public R submit(@Valid @RequestBody InviteRelation inviteRelation) {
		return R.status(inviteRelationService.saveOrUpdate(inviteRelation));
	}


	/**
	 * 删除 邀请关系表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(inviteRelationService.deleteLogic(Func.toLongList(ids)));
	}


}
