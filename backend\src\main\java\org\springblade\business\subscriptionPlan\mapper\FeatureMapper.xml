<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.subscriptionPlan.mapper.FeatureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="featureResultMap" type="org.springblade.business.subscriptionPlan.entity.Feature">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="feature_name" property="featureName"/>
        <result column="feature_code" property="featureCode"/>
        <result column="feature_description" property="featureDescription"/>
        <result column="feature_type" property="featureType"/>
        <result column="icon" property="icon"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectFeaturePage" resultMap="featureResultMap">
        select * from urb_feature where is_deleted = 0
    </select>
    <select id="selectFeaturesByPlanId" resultType="org.springblade.business.subscriptionPlan.entity.Feature">
       select * from urb_feature where id in (select feature_id from urb_subscription_plan_feature where plan_id = #{id})
    </select>

</mapper>
