/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.invite.service.impl;


import org.springblade.business.invite.service.IInviteRecordService;
import org.springblade.business.invite.vo.InviteRecordVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.miniapp.entity.InviteRecord;
import org.springblade.miniapp.mapper.InviteRecordMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 邀请推广记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Service("InviteRecordService")
public class InviteRecordServiceImpl extends BaseServiceImpl<InviteRecordMapper, InviteRecord> implements IInviteRecordService {

	@Override
	public IPage<InviteRecordVO> selectInviteRecordPage(IPage<InviteRecordVO> page, InviteRecordVO inviteRecord) {
		return page.setRecords(baseMapper.selectInviteRecordPage(page, inviteRecord));
	}

}
