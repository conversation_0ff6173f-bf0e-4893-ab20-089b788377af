
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 城市消息模板实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("urb_message_template")
public class UrbMessageTemplate implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 类型编码
     */
    @Schema(description = "类型编码")
    private String typeCode;

    /**
     * 类型名称
     */
    @Schema(description = "类型名称")
    private String typeName;

    /**
     * 标题模板
     */
    @Schema(description = "标题模板")
    private String titleTemplate;

    /**
     * 内容模板
     */
    @Schema(description = "内容模板")
    private String contentTemplate;

    /**
     * 是否系统模板
     */
    @Schema(description = "是否系统模板")
    private Boolean isSystem;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
