import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/report/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/report/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/report/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/report/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/report/submit',
    method: 'post',
    data: row
  })
}

// 审核举报记录
export const auditReport = (data) => {
  return request({
    url: '/blade-ad/report/audit',
    method: 'post',
    data: data
  })
}

// 批量审核举报记录
export const batchAuditReport = (data) => {
  return request({
    url: '/blade-ad/report/batch-audit',
    method: 'post',
    data: data
  })
}

