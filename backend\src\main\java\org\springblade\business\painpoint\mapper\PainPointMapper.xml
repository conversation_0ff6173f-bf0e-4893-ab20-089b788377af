<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.painpoint.mapper.PainPointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="painPointResultMap" type="org.springblade.business.painpoint.entity.PainPoint">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="content" property="content"/>
        <result column="image" property="image"/>
        <result column="feedback_time" property="feedbackTime"/>
        <result column="contact_info" property="contactInfo"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_result" property="auditResult"/>
        <result column="nickname" property="nickname"/>
    </resultMap>



    <select id="selectPainPointPage" resultMap="painPointResultMap">
        select upp.*,uu.nickname nickname from urb_pain_point upp
            join urb_user uu on upp.create_user = uu.id
                             where upp.is_deleted = 0
        <if test="painPoint.nickname!=null">
            and uu.nickname like concat('%',#{painPoint.nickname},'%')
        </if>
        <if test="painPoint.auditStatus!=null">
            and upp.audit_status = #{painPoint.auditStatus}
        </if>
        order by upp.create_time desc
    </select>
</mapper>
