<template>
  <basic-container>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户详情弹窗功能演示</span>
        </div>
      </template>
      
      <div class="demo-content">
        <h3>功能说明</h3>
        <p>点击下面的用户昵称链接，将弹出用户详情信息弹窗：</p>
        
        <el-table :data="demoUsers" border style="width: 100%; margin-top: 20px;">
          <el-table-column prop="id" label="用户ID" width="100"></el-table-column>
          <el-table-column label="用户昵称" width="150">
            <template #default="{ row }">
              <el-button 
                type="text" 
                @click="showUserDetail(row.id)"
                style="color: #409EFF; text-decoration: underline;">
                {{ row.nickname }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" width="150"></el-table-column>
          <el-table-column prop="createTime" label="注册时间"></el-table-column>
        </el-table>
        
        <div style="margin-top: 30px;">
          <h3>已实现的页面</h3>
          <ul>
            <li>帖子管理页面 - 发布者列支持点击查看用户详情</li>
            <li>反馈管理页面 - 反馈用户列支持点击查看用户详情</li>
            <li>名片管理页面 - 用户列支持点击查看用户详情</li>
          </ul>
        </div>
        
        <div style="margin-top: 20px;">
          <h3>后端接口要求</h3>
          <el-alert
            title="需要后端开发实现 GET /blade-ad/user/detail?id={userId} 接口"
            type="warning"
            :closable="false"
            show-icon>
            <template #default>
              <p>接口应返回用户的详细信息，包括基本信息、账户状态、活动统计等。</p>
              <p>详细的接口规范请参考 UserDetailDialog.test.md 文件。</p>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog 
      v-model="userDetailVisible" 
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />
  </basic-container>
</template>

<script>
import UserDetailDialog from '@/components/UserDetailDialog.vue'

export default {
  name: 'UserDetailDemo',
  components: {
    UserDetailDialog
  },
  data() {
    return {
      userDetailVisible: false,
      selectedUserId: null,
      demoUsers: [
        {
          id: '1',
          nickname: '张三',
          phone: '138****1234',
          createTime: '2024-01-15 10:30:00'
        },
        {
          id: '2', 
          nickname: '李四',
          phone: '139****5678',
          createTime: '2024-01-20 14:20:00'
        },
        {
          id: '3',
          nickname: '王五',
          phone: '136****9012',
          createTime: '2024-01-25 09:15:00'
        }
      ]
    }
  },
  methods: {
    showUserDetail(userId) {
      this.selectedUserId = userId;
      this.userDetailVisible = true;
    },
    
    handleManageUser(userDetail) {
      this.$message.info(`管理用户: ${userDetail.nickname}`);
      // 这里可以跳转到用户管理页面或执行其他管理操作
    }
  }
}
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  span {
    font-weight: 600;
    color: #303133;
  }
}

.demo-content {
  h3 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
  
  ul {
    color: #606266;
    
    li {
      margin-bottom: 5px;
    }
  }
}
</style>
