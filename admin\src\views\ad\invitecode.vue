<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.invitecode_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
        <el-button type="warning"
                   icon="el-icon-refresh-right"
                   plain
                   @click="handleBatchGenerate">批量生成
        </el-button>
      </template>

      <!-- 邀请人插槽 -->
      <template #inviterUserId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.inviterUserId)"
          class="user-link">
          {{ getUserNickname(row.inviterUserId) }}
        </el-button>
      </template>

      <!-- 邀请码类型插槽 -->
      <template #codeType="{ row }">
        <el-tag :type="getCodeTypeColor(row.codeType)">
          {{ getCodeTypeText(row.codeType) }}
        </el-tag>
      </template>

      <!-- 邀请状态插槽 -->
      <template #inviteStatus="{ row }">
        <el-tag :type="getInviteStatusColor(row.inviteStatus)">
          {{ getInviteStatusText(row.inviteStatus) }}
        </el-tag>
      </template>

      <!-- 使用情况插槽 -->
      <template #usageInfo="{ row }">
        <el-progress
          :percentage="getUsagePercentage(row)"
          :color="getUsageColor(row)"
          :show-text="true"
          :format="() => `${row.usedCount}/${row.maxUses || '∞'}`">
        </el-progress>
      </template>

      <!-- 二维码插槽 -->
      <template #qrImageUrl="{ row }">
        <el-image
          v-if="row.qrImageUrl"
          :src="row.qrImageUrl"
          style="width: 50px; height: 50px;"
          :preview-src-list="[row.qrImageUrl]">
        </el-image>
        <span v-else style="color: #999;">无二维码</span>
      </template>

      <!-- 操作菜单插槽 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewInviteCodeDetail(row)">
          详情
        </el-button>
        <el-button
          v-if="row.inviteStatus === 'active'"
          type="warning"
          size="small"
          @click="handleDisableCode(row)">
          禁用
        </el-button>
        <el-button
          v-if="row.inviteStatus === 'inactive'"
          type="success"
          size="small"
          @click="handleEnableCode(row)">
          启用
        </el-button>
      </template>
    </avue-crud>

    <!-- 邀请码详情对话框 -->
    <el-dialog v-model="inviteCodeDetailVisible" title="邀请码详情" width="800px">
      <div v-if="selectedInviteCode">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="邀请码">{{ selectedInviteCode.inviteCode }}</el-descriptions-item>
          <el-descriptions-item label="邀请人">{{ getUserNickname(selectedInviteCode.inviterUserId) }}</el-descriptions-item>
          <el-descriptions-item label="邀请码类型">
            <el-tag :type="getCodeTypeColor(selectedInviteCode.codeType)">
              {{ getCodeTypeText(selectedInviteCode.codeType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getInviteStatusColor(selectedInviteCode.inviteStatus)">
              {{ getInviteStatusText(selectedInviteCode.inviteStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="使用情况">{{ selectedInviteCode.usedCount }}/${selectedInviteCode.maxUses || '无限制'}</el-descriptions-item>
          <el-descriptions-item label="注册成功">{{ selectedInviteCode.registeredCount }}人</el-descriptions-item>
          <el-descriptions-item label="奖励积分">{{ selectedInviteCode.rewardPoints }}分</el-descriptions-item>
          <el-descriptions-item label="过期时间">{{ selectedInviteCode.expireTime || '永不过期' }}</el-descriptions-item>
          <el-descriptions-item label="最后使用">{{ selectedInviteCode.lastUsedTime || '从未使用' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedInviteCode.createTime }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedInviteCode.qrImageUrl">
          <el-divider />
          <h4>二维码：</h4>
          <el-image
            :src="selectedInviteCode.qrImageUrl"
            style="width: 200px; height: 200px;"
            :preview-src-list="[selectedInviteCode.qrImageUrl]">
          </el-image>
        </div>

        <div v-if="selectedInviteCode.description">
          <el-divider />
          <h4>描述：</h4>
          <p>{{ selectedInviteCode.description }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model="userDetailVisible"
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/invitecode";
  import {mapGetters} from "vuex";
  import userInfoMixin from '@/mixins/userInfoMixin';

  export default {
    mixins: [userInfoMixin],
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        inviteCodeDetailVisible: false,
        selectedInviteCode: null,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: true,
          editBtn: true,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "邀请码",
              prop: "inviteCode",
              width: 150,
              search: true,
              rules: [{
                required: true,
                message: "请输入邀请码",
                trigger: "blur"
              }]
            },
            {
              label: "邀请人",
              prop: "inviterUserId",
              type: "select",
              width: 150,
              search: true,
              slot: true,
              dicData: [],
              props: {
                label: "nickname",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择邀请人",
                trigger: "blur"
              }]
            },
            {
              label: "邀请码类型",
              prop: "codeType",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '普通', value: 'general' },
                { label: '高级', value: 'premium' },
                { label: '限量', value: 'limited' }
              ],
              rules: [{
                required: true,
                message: "请选择邀请码类型",
                trigger: "blur"
              }]
            },
            {
              label: "使用情况",
              prop: "usageInfo",
              width: 150,
              slot: true
            },
            {
              label: "最大使用次数",
              prop: "maxUses",
              type: "number",
              width: 120,
              hide: true,
              rules: [{
                required: true,
                message: "请输入最大使用次数",
                trigger: "blur"
              }]
            },
            {
              label: "已使用次数",
              prop: "usedCount",
              type: "number",
              width: 100,
              sortable: true,
              hide: true
            },
            {
              label: "注册成功数",
              prop: "registeredCount",
              type: "number",
              width: 100,
              sortable: true,
              rules: [{
                required: true,
                message: "请输入成功注册人数",
                trigger: "blur"
              }]
            },
            {
              label: "奖励积分",
              prop: "rewardPoints",
              type: "number",
              width: 100,
              rules: [{
                required: true,
                message: "请输入奖励积分",
                trigger: "blur"
              }]
            },
            {
              label: "邀请状态",
              prop: "inviteStatus",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '有效', value: 'active' },
                { label: '无效', value: 'inactive' },
                { label: '过期', value: 'expired' }
              ],
              rules: [{
                required: true,
                message: "请选择邀请状态",
                trigger: "blur"
              }]
            },
            {
              label: "过期时间",
              prop: "expireTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "二维码",
              prop: "qrImageUrl",
              width: 80,
              slot: true,
              hide: false
            },
            {
              label: "二维码文件ID",
              prop: "qrFileId",
              rules: [{
                required: true,
                message: "请输入二维码文件ID",
                trigger: "blur"
              }]
            },
            {
              label: "小程序页面路径",
              prop: "miniappPath",
              rules: [{
                required: true,
                message: "请输入小程序页面路径",
                trigger: "blur"
              }]
            },
            {
              label: "邀请码描述",
              prop: "description",
              rules: [{
                required: true,
                message: "请输入邀请码描述",
                trigger: "blur"
              }]
            },
            {
              label: "扩展信息（JSON格式）",
              prop: "extInfo",
              rules: [{
                required: true,
                message: "请输入扩展信息（JSON格式）",
                trigger: "blur"
              }]
            },
            {
              label: "最后使用时间",
              prop: "lastUsedTime",
              rules: [{
                required: true,
                message: "请输入最后使用时间",
                trigger: "blur"
              }]
            },
            {
              label: "是否长期有效",
              prop: "isPermanent",
              rules: [{
                required: true,
                message: "请输入是否长期有效",
                trigger: "blur"
              }]
            },
            {
              label: "状态标识",
              prop: "statusFlag",
              rules: [{
                required: true,
                message: "请输入状态标识",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.invitecode_add, false),
          viewBtn: this.validData(this.permission.invitecode_view, false),
          delBtn: this.validData(this.permission.invitecode_delete, false),
          editBtn: this.validData(this.permission.invitecode_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 排序处理
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      // 邀请码类型相关方法
      getCodeTypeColor(type) {
        const typeMap = {
          'general': 'primary',
          'premium': 'success',
          'limited': 'warning'
        };
        return typeMap[type] || 'info';
      },

      getCodeTypeText(type) {
        const typeMap = {
          'general': '普通',
          'premium': '高级',
          'limited': '限量'
        };
        return typeMap[type] || '未知';
      },

      // 邀请状态相关方法
      getInviteStatusColor(status) {
        const statusMap = {
          'active': 'success',
          'inactive': 'danger',
          'expired': 'warning'
        };
        return statusMap[status] || 'info';
      },

      getInviteStatusText(status) {
        const statusMap = {
          'active': '有效',
          'inactive': '无效',
          'expired': '过期'
        };
        return statusMap[status] || '未知';
      },

      // 使用情况相关方法
      getUsagePercentage(row) {
        if (!row.maxUses || row.maxUses === 0) return 0;
        return Math.round((row.usedCount / row.maxUses) * 100);
      },

      getUsageColor(row) {
        const percentage = this.getUsagePercentage(row);
        if (percentage >= 90) return '#f56c6c';
        if (percentage >= 70) return '#e6a23c';
        return '#67c23a';
      },

      // 查看邀请码详情
      viewInviteCodeDetail(row) {
        this.selectedInviteCode = row;
        this.inviteCodeDetailVisible = true;
      },

      // 禁用邀请码
      handleDisableCode(row) {
        this.$confirm('确定要禁用这个邀请码吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用禁用邀请码的API
          this.$message.success('邀请码已禁用');
          this.onLoad(this.page);
        });
      },

      // 启用邀请码
      handleEnableCode(row) {
        this.$confirm('确定要启用这个邀请码吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用启用邀请码的API
          this.$message.success('邀请码已启用');
          this.onLoad(this.page);
        });
      },

      // 批量生成邀请码
      handleBatchGenerate() {
        this.$message.info('批量生成功能开发中...');
      },

      // 刷新数据
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },

      // 导出数据
      handleExport() {
        this.$message.info('导出功能开发中...');
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;

        // 加载用户字典
        this.loadUserDict();

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error('加载数据失败');
        });
      }
    }
  };
</script>

<style scoped>
/* 用户链接样式 */
.user-link {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
}

.user-link:hover {
  color: #66b1ff;
}

/* 状态标签样式 */
.el-tag {
  font-size: 12px;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 搜索区域样式优化 */
:deep(.avue-crud__search) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
}

/* 进度条样式 */
:deep(.el-progress) {
  width: 120px;
}

:deep(.el-progress__text) {
  font-size: 12px !important;
}

/* 二维码图片样式 */
.el-image {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>
