/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 用户功能权限表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("urb_user_feature_permission")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户功能权限表")
public class UserFeaturePermission extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 功能ID
     */
    @Schema(description = "功能ID")
    private Long featureId;
    /**
     * 订阅ID
     */
    @Schema(description = "订阅ID")
    private Long subscriptionId;
    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Byte isActive;
    /**
     * 权限过期时间
     */
    @Schema(description = "权限过期时间")
    private LocalDateTime expireTime;
    /**
     * 功能代码
     */
    @Schema(description = "功能代码")
    private String featureCode;



}
