/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.subscriptionPlan.entity.Feature;
import org.springblade.business.subscriptionPlan.service.IFeatureService;
import org.springblade.business.subscriptionPlan.vo.FeatureVO;
import org.springblade.business.subscriptionPlan.wrapper.FeatureWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 功能表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/feature")
@io.swagger.v3.oas.annotations.tags.Tag(name = "功能表", description = "功能表接口")
public class FeatureController extends BladeController {

	private IFeatureService featureService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入feature")
	public R<FeatureVO> detail(Feature feature) {
		Feature detail = featureService.getOne(Condition.getQueryWrapper(feature));
		return R.data(FeatureWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 功能表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入feature")
	public R<IPage<FeatureVO>> list(Feature feature, Query query) {
		IPage<Feature> pages = featureService.page(Condition.getPage(query), Condition.getQueryWrapper(feature));
		return R.data(FeatureWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 功能表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入feature")
	public R<IPage<FeatureVO>> page(FeatureVO feature, Query query) {
		IPage<FeatureVO> pages = featureService.selectFeaturePage(Condition.getPage(query), feature);
		return R.data(pages);
	}

	/**
	 * 新增 功能表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入feature")
	public R save(@Valid @RequestBody Feature feature) {
		return R.status(featureService.save(feature));
	}

	/**
	 * 修改 功能表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入feature")
	public R update(@Valid @RequestBody Feature feature) {
		return R.status(featureService.updateById(feature));
	}

	/**
	 * 新增或修改 功能表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入feature")
	public R submit(@Valid @RequestBody Feature feature) {
		return R.status(featureService.saveOrUpdate(feature));
	}


	/**
	 * 删除 功能表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(featureService.deleteLogic(Func.toLongList(ids)));
	}


}
