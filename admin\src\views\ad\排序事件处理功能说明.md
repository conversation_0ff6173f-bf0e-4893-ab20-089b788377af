# 排序事件处理功能说明

## 功能概述
根据 Avue 文档，为管理后台的表格添加排序事件处理功能，实现前端排序参数传递给后端，支持动态排序查询。

## 实现步骤

### 1. 添加排序事件监听
在 `avue-crud` 组件中添加 `@sort-change` 事件监听：

```vue
<avue-crud :option="option"
           :table-loading="loading"
           :data="data"
           @sort-change="sortChange"
           @on-load="onLoad">
</avue-crud>
```

### 2. 添加排序参数
在 `data()` 中添加排序参数存储：

```javascript
data() {
  return {
    // 其他数据...
    sortParams: {
      ascs: [],   // 升序字段数组
      descs: []   // 降序字段数组
    }
  }
}
```

### 3. 实现排序事件处理方法
在 `methods` 中添加排序处理方法：

```javascript
methods: {
  sortChange(val) {
    this.sortParams.ascs = [];
    this.sortParams.descs = [];
    
    if (val.order === 'ascending') {
      this.sortParams.ascs.push(val.prop);
    } else if (val.order === 'descending') {
      this.sortParams.descs.push(val.prop);
    }
    
    this.page.currentPage = 1;  // 重置到第一页
    this.onLoad(this.page);     // 重新加载数据
  }
}
```

### 4. 修改数据加载方法
在 `onLoad` 方法中合并排序参数：

```javascript
onLoad(page, params = {}) {
  this.loading = true;
  
  // 合并查询参数和排序参数
  const queryParams = Object.assign({}, params, this.query, this.sortParams);
  
  // 调用API传递排序参数
  getPage(page.currentPage, page.pageSize, queryParams).then(res => {
    const data = res.data.data;
    this.page.total = Number(data.total);
    this.data = data.records;
    this.loading = false;
    this.selectionClear();
  });
}
```

## 已完成页面

### 1. 反馈管理页面 ✅
**文件**: `admin/src/views/ad/post/feedback.vue`

**修改内容**:
- ✅ 添加 `@sort-change="sortChange"` 事件监听
- ✅ 添加 `sortParams` 排序参数
- ✅ 实现 `sortChange` 排序处理方法
- ✅ 修改 `onLoad` 方法合并排序参数

### 2. 帖子管理页面 ✅
**文件**: `admin/src/views/ad/post/post.vue`

**修改内容**:
- ✅ 添加 `@sort-change="sortChange"` 事件监听
- ✅ 添加 `sortParams` 排序参数
- ✅ 实现 `sortChange` 排序处理方法
- ✅ 修改 `onLoad` 方法合并排序参数

### 3. 举报管理页面 ✅
**文件**: `admin/src/views/ad/post/report.vue`

**修改内容**:
- ✅ 添加 `@sort-change="sortChange"` 事件监听
- ✅ 添加 `sortParams` 排序参数
- ✅ 实现 `sortChange` 排序处理方法
- ✅ 修改 `onLoad` 方法合并排序参数

### 4. 痛点管理页面 🔄
**文件**: `admin/src/views/ad/painpoint.vue`

**修改内容**:
- ✅ 添加 `@sort-change="sortChange"` 事件监听
- ⏳ 需要添加 `sortParams` 排序参数
- ⏳ 需要实现 `sortChange` 排序处理方法
- ⏳ 需要修改 `onLoad` 方法合并排序参数

## 待完成页面

### 5. 名片管理页面 ⏳
**文件**: `admin/src/views/ad/businesscard.vue`

### 6. 积分记录页面 ⏳
**文件**: `admin/src/views/ad/point/pointsrecord.vue`

### 7. 积分兑换页面 ⏳
**文件**: `admin/src/views/ad/point/pointsexchange.vue`

## 排序参数格式

### 前端发送格式
```javascript
// 升序排序
{
  current: 1,
  size: 10,
  ascs: ['createTime'],
  descs: []
}

// 降序排序
{
  current: 1,
  size: 10,
  ascs: [],
  descs: ['createTime']
}

// 无排序
{
  current: 1,
  size: 10,
  ascs: [],
  descs: []
}
```

### 后端接收处理
MyBatis-Plus 会自动处理 `ascs` 和 `descs` 参数：

```java
// 自动转换为 SQL ORDER BY 子句
// ascs: ['createTime'] -> ORDER BY create_time ASC
// descs: ['createTime'] -> ORDER BY create_time DESC
```

## 排序事件参数

### sortChange 事件参数
```javascript
// 点击列头排序时，Avue 传递的参数
{
  prop: 'createTime',     // 排序字段
  order: 'descending'     // 排序方向: 'ascending' | 'descending' | null
}
```

### 排序状态
- `ascending` - 升序排列
- `descending` - 降序排列
- `null` - 取消排序

## 技术实现细节

### 1. 事件流程
```
用户点击列头 → sortChange事件触发 → 更新sortParams → 重置页码 → 重新加载数据
```

### 2. 参数合并
```javascript
// 合并顺序：默认参数 < 搜索参数 < 查询参数 < 排序参数
const queryParams = Object.assign({}, params, this.query, this.sortParams);
```

### 3. 页码重置
排序时自动重置到第一页，避免数据错乱：
```javascript
this.page.currentPage = 1;
```

## 用户体验

### 1. 排序操作
- 点击列头进行排序
- 再次点击切换排序方向
- 第三次点击取消排序

### 2. 排序状态指示
- 🔼 升序图标
- 🔽 降序图标
- ➖ 无排序状态

### 3. 数据加载
- 排序时显示加载状态
- 自动重置到第一页
- 保持其他查询条件

## 性能优化

### 1. 前端优化
- 排序时重置页码避免无效请求
- 合并参数减少重复计算
- 防抖处理避免频繁请求

### 2. 后端优化
- 数据库字段索引优化
- SQL 查询语句优化
- 分页排序性能优化

## 错误处理

### 1. 排序字段验证
```javascript
// 可以添加字段白名单验证
const allowedSortFields = ['createTime', 'updateTime', 'viewCount'];
if (allowedSortFields.includes(val.prop)) {
  // 执行排序
}
```

### 2. 异常处理
```javascript
sortChange(val) {
  try {
    // 排序逻辑
  } catch (error) {
    console.error('排序处理失败:', error);
    this.$message.error('排序失败，请重试');
  }
}
```

## 扩展功能

### 1. 多字段排序
```javascript
// 支持多字段组合排序
sortParams: {
  ascs: ['createTime', 'viewCount'],
  descs: ['updateTime']
}
```

### 2. 自定义排序
```javascript
// 支持自定义排序规则
sortChange(val) {
  // 自定义排序逻辑
  if (val.prop === 'customField') {
    // 特殊处理
  }
}
```

### 3. 排序记忆
```javascript
// 记住用户排序偏好
localStorage.setItem('sortPreference', JSON.stringify(this.sortParams));
```

## 测试建议

### 1. 功能测试
- 测试各字段排序功能
- 测试排序方向切换
- 测试排序取消功能

### 2. 性能测试
- 测试大数据量排序性能
- 测试排序查询响应时间
- 测试并发排序请求

### 3. 兼容性测试
- 测试不同浏览器兼容性
- 测试移动端排序体验
- 测试排序与搜索组合使用

## 维护说明

### 1. 新增排序字段
1. 在字段配置中添加 `sortable: true`
2. 确保后端支持该字段排序
3. 测试排序功能正常

### 2. 排序性能监控
- 监控排序查询执行时间
- 优化慢查询排序
- 定期检查索引效果

### 3. 用户反馈处理
- 收集排序功能使用反馈
- 优化排序用户体验
- 修复排序相关问题

现在已经为前3个页面完成了排序事件处理功能，剩余页面需要按照相同的模式继续添加。
