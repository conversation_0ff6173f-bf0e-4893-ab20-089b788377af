/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量反馈审核DTO
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@Schema(description = "批量反馈审核DTO")
public class BatchFeedbackAuditDTO {

	/**
	 * 反馈ID列表
	 */
	@NotNull(message = "反馈ID列表不能为空")
	@Schema(description = "反馈ID列表")
	private List<String> id;

	/**
	 * 审核状态：0-待审核，1-通过，2-拒绝
	 */
	@NotBlank(message = "审核状态不能为空")
	@Schema(description = "审核状态")
	private String auditStatus;

	/**
	 * 审核理由
	 */
	@Schema(description = "审核理由")
	private String reason;

}
