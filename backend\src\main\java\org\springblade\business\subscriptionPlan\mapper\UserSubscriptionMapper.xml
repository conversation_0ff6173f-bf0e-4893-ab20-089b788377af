<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.subscriptionPlan.mapper.UserSubscriptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userSubscriptionResultMap" type="org.springblade.business.subscriptionPlan.entity.UserSubscription">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="user_id" property="userId"/>
        <result column="plan_id" property="planId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="payment_status" property="paymentStatus"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="auto_renew" property="autoRenew"/>
        <result column="cancel_reason" property="cancelReason"/>
    </resultMap>


    <select id="selectUserSubscriptionPage" resultMap="userSubscriptionResultMap">
        select * from urb_user_subscription where is_deleted = 0
    </select>

</mapper>
