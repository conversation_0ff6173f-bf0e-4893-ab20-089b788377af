/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 标签实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_tag")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "标签")
public class Tag extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 标签名称
	 */
	@Schema(description = "标签名称")
	@TableField("tag_name")
	private String tagName;

	/**
	 * 分类ID
	 */
	@Schema(description = "分类ID")
	private Long categoryId;

	/**
	 * 标签颜色
	 */
	@Schema(description = "标签颜色")
	private String color;

	/**
	 * 标签图标
	 */
	@Schema(description = "标签图标")
	private String icon;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	@TableField(value = "`sort`")
	private Integer sort;

	/**
	 * 是否启用：0-否，1-是
	 */
	@Schema(description = "是否启用")
	private Integer enabled;

	/**
	 * 使用次数
	 */
	@Schema(description = "使用次数")
	private Integer useCount;

	/**
	 * 是否系统标签：0-否，1-是
	 */
	@Schema(description = "是否系统标签")
	private Integer isSystem;

	private String description;

/*	@TableField(exist = false)*/
	private String sortOrder;


	// 是举报还是反馈还是分类的。
	private Integer type;

}
