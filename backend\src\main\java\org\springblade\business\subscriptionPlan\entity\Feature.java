/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 功能表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("urb_feature")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "功能表")
public class Feature extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 功能名称
     */
    @Schema(description = "功能名称")
    private String featureName;
    /**
     * 功能代码
     */
    @Schema(description = "功能代码")
    private String featureCode;
    /**
     * 功能描述
     */
    @Schema(description = "功能描述")
    private String featureDescription;
    /**
     * 功能类型（BASIC：基础功能，PREMIUM：高级功能，CUSTOM：自定义功能）
     */
    @Schema(description = "功能类型（BASIC：基础功能，PREMIUM：高级功能，CUSTOM：自定义功能）")
    private String featureType;
    /**
     * 功能图标
     */
    @Schema(description = "功能图标")
    private String icon;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;


}
