<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.PostCarpoolMapper">

    <!-- 通用查询映射结果 -->
    <!-- 通用查询映射结果 -->
    <resultMap id="postResultMap" type="org.springblade.business.post.vo.SupPostVO">
        <id column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="images" property="images"/>
        <result column="category_id" property="categoryId"/>
        <result column="tags" property="tags"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="feedback_count" property="feedbackCount"/>
        <result column="favorite_count" property="favoriteCount"/>
        <result column="status" property="status"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_liked" property="isLiked"/>
        <result column="is_favorite" property="isFavorite"/>
        <result column="top" property="top"/>
        <result column="completed" property="completed"/>
        <result column="category_name" property="categoryName"/>
        <result column="distance" property="distance"/>

        <!-- 顺风车帖子信息映射 -->
        <association property="carpool" javaType="org.springblade.business.post.entity.PostCarpool">
            <result column="id" property="id"/>
            <result column="region" property="region"/>
            <result column="carpool_type" property="carpoolType"/>
            <result column="departure" property="departure"/>
            <result column="destination" property="destination"/>
            <result column="via" property="via"/>
            <result column="departure_time" property="departureTime"/>
            <result column="empty_seats" property="emptySeats"/>
            <result column="tonnage" property="tonnage"/>
            <result column="post_id" property="postId"/>
        </association>

    </resultMap>
    <delete id="deleteByPostId">
            delete from urb_post_carpool where post_id = #{postId}
    </delete>


    <select id="selectPostCarpoolPage" resultMap="postResultMap">
        select pc.*,p.* from urb_post_carpool pc
                  left join urb_post p on  pc.post_id = p.id
                  where
                      p.is_deleted = 0
                    and p.create_user = #{model.createUser}

    </select>

</mapper>
