<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.CategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="categoryResultMap" type="org.springblade.business.post.entity.Category">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <collection property="tags" ofType="org.springblade.business.post.entity.Tag" column="id"
                    select="org.springblade.business.user.mapper.CategoryMapper.selectTagsByCategoryId"/>
    </resultMap>


    <select id="selectCategoryPage" resultMap="categoryResultMap">
        select * from urb_category where is_deleted = 0
    </select>
    <select id="selectTagsByCategoryId" resultType="tag">
        select t.* from urb_tag t
        left join urb_category_tag ct on ct.tag_id = t.id
        where ct.category_id = #{id}
        order by t.sort_order desc
    </select>
    <select id="selectCategoryList" resultMap="categoryResultMap">
        select * from urb_category c
        where c.is_deleted = 0
        and c.status = 1
        and c.enabled = 1
        order by c.sort_order desc
    </select>

</mapper>
