<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   
                   icon="el-icon-delete"
                   plain
                   v-if="permission.feedback_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"

                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-check"
                   plain
                   @click="handleBatchAudit">批量审核
        </el-button>
      </template>
      <template #auditStatus="{ row }">
        <el-tag :type="getAuditStatusType(row.auditStatus)">
          {{ getAuditStatusText(row.auditStatus) }}
        </el-tag>
      </template>
      <template #postStatus="{ row }">
        <el-tag :type="getPostStatusType(row)" size="small">
          {{ getPostStatusText(row) }}
        </el-tag>
      </template>
      <template #content="{ row }">
        <el-button type="text" @click="viewContent(row)">
          {{ row.content ? (row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content) : '查看内容' }}
        </el-button>
      </template>
      <template #createUser="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.createUser)"
          style="color: #409EFF; text-decoration: underline;">
          {{ row.nickname || '未知用户' }}
        </el-button>
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.auditStatus === '0'"
                   type="success"
                   size="small"
                   @click="handleAudit(row, '1')">
          通过
        </el-button>
        <el-button v-if="row.auditStatus === '0'"
                   type="danger"
                   size="small"
                   @click="handleAudit(row, '2')">
          拒绝
        </el-button>
      </template>
    </avue-crud>

    <!-- 内容详情对话框 -->
    <el-dialog v-model="contentDialogVisible" title="反馈内容详情" width="800px">
      <div v-if="selectedFeedback">
        <!-- 反馈信息 -->
        <el-card class="mb-3">
          <template #header>
            <div class="card-header">
              <span>反馈信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="反馈用户">{{ selectedFeedback.nickname }}</el-descriptions-item>
            <el-descriptions-item label="反馈时间">{{ selectedFeedback.createTime }}</el-descriptions-item>
            <el-descriptions-item label="审核状态">
              <el-tag :type="getAuditStatusType(selectedFeedback.auditStatus)">
                {{ getAuditStatusText(selectedFeedback.auditStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="有用数">{{ selectedFeedback.helpfulCount || 0 }}</el-descriptions-item>
            <el-descriptions-item label="反馈内容" :span="2">
              <p style="white-space: pre-wrap; margin: 0;">{{ selectedFeedback.content }}</p>
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedFeedback.reason" label="反馈标签" :span="2">
              <p style="white-space: pre-wrap; margin: 0; color: #666;">{{ selectedFeedback.reason }}</p>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 帖子信息 -->
        <el-card v-if="selectedFeedback.post">
          <template #header>
            <div class="card-header">
              <span>关联帖子信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="帖子ID">{{ selectedFeedback.post.id }}</el-descriptions-item>
            <el-descriptions-item label="发布状态">
              <el-tag :type="selectedFeedback.post.auditStatus === '1' ? 'success' : 'warning'">
                {{ selectedFeedback.post.auditStatus === '1' ? '已通过' : '待审核' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="联系人">{{ selectedFeedback.post.contactName }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ selectedFeedback.post.contactPhone || '未提供' }}</el-descriptions-item>
            <el-descriptions-item label="地址">{{ selectedFeedback.post.address || '未提供' }}</el-descriptions-item>
            <el-descriptions-item label="发布时间">{{ selectedFeedback.post.createTime }}</el-descriptions-item>
            <el-descriptions-item label="帖子内容" :span="2">
              <p style="white-space: pre-wrap; margin: 0;">{{ selectedFeedback.post.content }}</p>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 帖子图片 -->
          <div v-if="selectedFeedback.post.images" style="margin-top: 15px;">
            <h4 style="margin-bottom: 10px;">帖子图片：</h4>
            <el-image
              v-for="(img, index) in getImageList(selectedFeedback.post.images)"
              :key="index"
              :src="img"
              style="width: 120px; height: 120px; margin-right: 10px; margin-bottom: 10px; border-radius: 4px;"
              :preview-src-list="getImageList(selectedFeedback.post.images)"
              fit="cover">
            </el-image>
          </div>
        </el-card>

        <!-- 帖子不存在提示 -->
        <el-card v-else>
          <template #header>
            <div class="card-header">
              <span>关联帖子信息</span>
            </div>
          </template>
          <el-empty description="帖子已删除" :image-size="80">
            <template #description>
              <div style="color: #909399; font-size: 14px;">
                <el-icon style="margin-right: 5px;"><Warning /></el-icon>
                该反馈关联的帖子已被删除或不存在
              </div>
            </template>
          </el-empty>
        </el-card>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="反馈审核" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.reason"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入审核备注（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model="userDetailVisible"
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />
  </basic-container>
</template>

<script>
  import {getList,getPage, getDetail, add, update, remove, auditFeedback, batchAuditFeedback} from "@/api/ad/feedback";
  import {getUserDict} from "@/api/ad/user";
  import {mapGetters} from "vuex";
  import { Warning } from '@element-plus/icons-vue';
  import UserDetailDialog from '@/components/UserDetailDialog.vue';

  export default {
    components: {
      Warning,
      UserDetailDialog
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        contentDialogVisible: false,
        selectedFeedback: null,
        auditDialogVisible: false,
        auditForm: {
          id: null,
          auditStatus: '1',
          reason: ''
        },
        userDetailVisible: false,
        selectedUserId: null,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          selection: true,
          editBtn: false,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "帖子ID",
              prop: "postId",
              search: true
            },
            {
              label: "帖子状态",
              prop: "postStatus",
              slot: true,
              width: 100
            },
             {
              label: "帖子分类",
              prop: "categoryName",
              rules: [{
                required: true,
                message: "请输入帖子分类",
                trigger: "blur"
              }]
            },
            {
              label: "反馈用户",
              prop: "createUser",
              type: "select",
              dicData: [],
              props: {
                label: "nickname",
                value: "id"
              },
              search: true,
              slot: true,
              formatter: (row) => {
                return row.nickname || '未知用户';
              }
            },
            {
              label: "反馈内容",
              prop: "content",
              type: "textarea",
              overHidden: true,
              span: 24,
              slot: true,
              rules: [{
                required: true,
                message: "请输入反馈内容",
                trigger: "blur"
              }]
            },
            {
              label: "审核状态",
              prop: "auditStatus",
              type: "select",
              dicUrl: '/blade-system/region/select?code=common_audit_dic',
              search: true,
              slot: true
            },
            {
              label: "有用数",
              prop: "helpfulCount",
              type: "number",
              sortable: true
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "date",
              search: true,
              searchSpan: 12,
              searchRange: true,
              sortable: true
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              sortable: true,
              hide: true
            }

          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.feedback_add, false),
          viewBtn: this.validData(this.permission.feedback_view, false),
          delBtn: this.validData(this.permission.feedback_delete, false),
          editBtn: this.validData(this.permission.feedback_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      getImageList(images) {
        if (!images) return [];
        if (Array.isArray(images)) return images;
        return images.split(',').filter(img => img.trim() !== '');
      },
      getAuditStatusType(status) {
        const statusMap = {
          '0': 'warning',
          '1': 'success',
          '2': 'danger'
        };
        return statusMap[status] || 'info';
      },
      getAuditStatusText(status) {
        const statusMap = {
          '0': '待审核',
          '1': '已通过',
          '2': '已拒绝'
        };
        return statusMap[status] || '未知';
      },
      getPostStatusType(row) {
        if (!row.post) {
          return 'danger';
        }
        return 'success';
      },
      getPostStatusText(row) {
        if (!row.post) {
          return '已删除';
        }
        return '正常';
      },
      viewContent(row) {
        this.selectedFeedback = row;
        this.contentDialogVisible = true;
      },
      // 显示用户详情
      showUserDetail(userId) {
        if (!userId) {
          this.$message.warning('用户信息不存在');
          return;
        }
        this.selectedUserId = userId;
        this.userDetailVisible = true;
      },
      // 处理管理用户事件
      handleManageUser(userDetail) {
        this.$message.info(`管理用户: ${userDetail.nickname}`);
        // 这里可以跳转到用户管理页面或执行其他管理操作
        // this.$router.push(`/admin/user/detail/${userDetail.id}`);
      },
      handleAudit(row, status) {
        this.auditForm.id = row.id;
        this.auditForm.auditStatus = status;
        this.auditForm.reason = '';
        this.auditDialogVisible = true;
      },
      handleBatchAudit() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.auditForm.id = this.selectionList.map(item => item.id);
        this.auditForm.auditStatus = '1';
        this.auditForm.reason = '';
        this.auditDialogVisible = true;
      },
      submitAudit() {
        // 判断是否为批量审核
        if (this.auditForm.id && Array.isArray(this.auditForm.id)) {
          // 批量审核
          batchAuditFeedback(this.auditForm).then(() => {
            this.auditDialogVisible = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "批量审核操作成功!"
            });
          }).catch(error => {
            this.$message.error("批量审核操作失败");
          });
        } else {
          // 单个审核
          auditFeedback(this.auditForm).then(() => {
            this.auditDialogVisible = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "审核操作成功!"
            });
          }).catch(error => {
            this.$message.error("审核操作失败");
          });
        }
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        // 动态加载用户字典
        getUserDict().then(res => {
          const users = res.data.data || [];
          const userCol = this.option.column.find(c => c.prop === 'createUser');
          if (userCol) userCol.dicData = users;
        }).catch(error => {
          console.error('加载用户字典失败:', error);
        });

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getPage(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
.mb-3 {
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-weight: 600;
  color: #303133;
}
</style>
