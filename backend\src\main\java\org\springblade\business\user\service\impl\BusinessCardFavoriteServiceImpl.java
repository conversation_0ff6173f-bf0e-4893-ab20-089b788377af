package org.springblade.business.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.entity.BusinessCardFavorite;
import org.springblade.business.user.mapper.BusinessCardFavoriteMapper;
import org.springblade.business.user.mapper.BusinessCardMapper;
import org.springblade.business.user.service.IBusinessCardFavoriteService;
import org.springblade.business.user.vo.BusinessCardFavoriteVO;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.miniapp.service.WeChatDataOperateService;

import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 名片收藏表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
public class BusinessCardFavoriteServiceImpl extends BaseServiceImpl<BusinessCardFavoriteMapper, BusinessCardFavorite> implements IBusinessCardFavoriteService {

    @Resource
    private BusinessCardMapper businessCardMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private WeChatDataOperateService dataOperateService;

    /**
     * 名片数据类型常量
     */
    private static final String CARD_TYPE = "2";

    @Override
    public IPage<BusinessCardVO> getPublicCardList(Query query, BusinessCardVO businessCard) {
        Map<String, Object> params = new HashMap<>();

        // 添加查询条件
        if (businessCard != null) {
            Map<String, Object> cardParams = BeanUtil.toMap(businessCard);
            params.putAll(cardParams);
        }

        // 只查询公开的名片
        params.put("isPublic", 1);
        params.put("auditStatus", 1); // 已审核通过
        params.put("currentUserId", AuthUtil.getUserId());

        return baseMapper.selectPublicCardList(Condition.getPage(query), params);
    }

    @Override
    public IPage<BusinessCardFavoriteVO> getMyFavoriteCardList(Query query, BusinessCardVO businessCard) {
        Map<String, Object> params = new HashMap<>();

        // 添加查询条件
        if (businessCard != null) {
            Map<String, Object> cardParams = BeanUtil.toMap(businessCard);
            params.putAll(cardParams);
        }

/*        params.put("userId", AuthUtil.getUserId());*/
		params.put("userId", "1952268335672999938");

        return baseMapper.selectMyFavoriteCardList(Condition.getPage(query), params);
    }

    /**
     * 返回收藏结果
     * @param cardId 名片ID
     * @param category 收藏分类
     * @param remark 收藏备注
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean favoriteCard(Long cardId, String category, String remark) {
        // 使用通用数据操作服务进行收藏
        boolean isFavorited = dataOperateService.toggleFavorite(cardId, CARD_TYPE);

        if (isFavorited) {
            // 如果收藏成功，保存扩展信息
            Long userId = AuthUtil.getUserId();

            // 获取原始名片信息
            BusinessCard originalCard = businessCardMapper.selectById(cardId);
            if (originalCard == null) {
                throw new RuntimeException("名片不存在");
            }

            // 创建名片快照
            String cardSnapshot;
            try {
                cardSnapshot = objectMapper.writeValueAsString(originalCard);
            } catch (Exception e) {
                log.error("创建名片快照失败", e);
                throw new RuntimeException("收藏失败");
            }

            // 保存扩展信息
            BusinessCardFavorite favoriteExt = new BusinessCardFavorite();
            favoriteExt.setCardId(cardId);
            favoriteExt.setUserId(userId);
            favoriteExt.setCardSnapshot(cardSnapshot);
            favoriteExt.setCategory(category);
            favoriteExt.setRemark(remark);

            // 设置基础字段
            favoriteExt.setCreateUser(userId);
            favoriteExt.setUpdateUser(userId);
            favoriteExt.setStatus(1);
            favoriteExt.setIsDeleted(0);

            return this.save(favoriteExt);
        } else {
            // 如果取消收藏，删除扩展信息
            Long userId = AuthUtil.getUserId();
            Map<String, Object> params = new HashMap<>();
            params.put("cardId", cardId);
            params.put("userId", userId);
            baseMapper.deleteCardFavoriteExt(params);
            return isFavorited;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unfavoriteCard(Long cardId) {
        // 使用通用数据操作服务取消收藏
        boolean isUnfavorited = !dataOperateService.toggleFavorite(cardId, CARD_TYPE);

        if (isUnfavorited) {
            // 删除扩展信息
            Long userId = AuthUtil.getUserId();
            Map<String, Object> params = new HashMap<>();
            params.put("cardId", cardId);
            params.put("userId", userId);

            return baseMapper.deleteCardFavoriteExt(params) > 0;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toggleCardLike(Long cardId) {
        // 使用通用数据操作服务切换点赞状态
        return dataOperateService.toggleLike(cardId, CARD_TYPE);
    }

    @Override
    public Boolean isCardFavorited(Long cardId) {
        // 使用通用数据操作服务检查收藏状态
        return dataOperateService.isFavorited(cardId, CARD_TYPE);
    }

    @Override
    public Boolean isCardLiked(Long cardId) {
        // 使用通用数据操作服务检查点赞状态
        return dataOperateService.isLiked(cardId, CARD_TYPE);
    }

    @Override
    public List<String> getFavoriteCategories() {
        // 获取当前用户的收藏分类列表
        Long userId = AuthUtil.getUserId();
        List<String> categories = baseMapper.selectFavoriteCategories(userId);

        // 如果没有收藏分类，返回默认分类
        if (categories == null || categories.isEmpty()) {
            return List.of("工作伙伴", "客户", "朋友", "其他");
        }

        return categories;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFavoriteInfo(Long cardId, String category, String remark) {
        Long userId = AuthUtil.getUserId();

        // 构建更新参数
        Map<String, Object> params = new HashMap<>();
        params.put("cardId", cardId);
        params.put("userId", userId);
        params.put("category", category);
        params.put("remark", remark);
        params.put("updateUser", userId);
        params.put("updateTime", new Date());

        // 更新收藏扩展信息
        int result = baseMapper.updateCardFavoriteExt(params);

        return result > 0;
    }
}
