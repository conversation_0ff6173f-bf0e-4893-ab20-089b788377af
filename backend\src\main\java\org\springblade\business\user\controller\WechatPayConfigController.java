package org.springblade.business.user.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.entity.WechatPayConfig;
import org.springblade.business.user.service.IWechatPayConfigService;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信支付配置管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/wechat-pay-config")
@Tag(name = "微信支付配置管理", description = "微信支付配置管理接口")
public class WechatPayConfigController {

    private final IWechatPayConfigService wechatPayConfigService;

    /**
     * 获取默认支付配置
     */
    @GetMapping("/default")
    @Operation(summary = "获取默认支付配置", description = "获取当前默认的微信支付配置")
    public R<Map<String, Object>> getDefaultConfig() {
        try {
            WechatPayConfig config = wechatPayConfigService.getDefaultConfig();

            Map<String, Object> result = new HashMap<>();
            if (config != null) {
                result.put("exists", true);
                result.put("configName", config.getConfigName());
                result.put("appId", config.getAppId());
                result.put("mchId", config.getMchId());
                result.put("isSandbox", config.getIsSandbox());
                result.put("isEnabled", config.getIsEnabled());
                result.put("isDefault", config.getIsDefault());
                result.put("priority", config.getPriority());
                result.put("remark", config.getRemark());
                // 不返回敏感信息如API密钥
            } else {
                result.put("exists", false);
                result.put("message", "未找到默认支付配置");
            }

            return R.data(result);
        } catch (Exception e) {
            log.error("获取默认支付配置失败", e);
            return R.fail("获取默认支付配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的配置
     */
    @GetMapping("/enabled")
    @Operation(summary = "获取启用的配置", description = "获取所有启用的微信支付配置")
    public R<List<WechatPayConfig>> getEnabledConfigs() {
        try {
            List<WechatPayConfig> configs = wechatPayConfigService.getEnabledConfigs();

            // 清除敏感信息
            configs.forEach(config -> {
                config.setApiKey("***");
                config.setApiV3Key("***");
            });

            return R.data(configs);
        } catch (Exception e) {
            log.error("获取启用配置失败", e);
            return R.fail("获取启用配置失败: " + e.getMessage());
        }
    }

    /**
     * 测试支付配置
     */
    @PostMapping("/test")
    @Operation(summary = "测试支付配置", description = "测试指定的微信支付配置是否有效")
    public R<Map<String, Object>> testConfig(
            @Parameter(description = "配置名称") @RequestParam(required = false) String configName) {
        try {
            WechatPayConfig config;
            if (configName != null && !configName.trim().isEmpty()) {
                config = wechatPayConfigService.getConfigByName(configName);
            } else {
                config = wechatPayConfigService.getDefaultConfig();
            }

            Map<String, Object> result = new HashMap<>();
            if (config == null) {
                result.put("valid", false);
                result.put("message", "配置不存在");
                return R.data(result);
            }

            // 验证配置
            boolean isValid = wechatPayConfigService.validateConfig(config);
            result.put("valid", isValid);
            result.put("configName", config.getConfigName());
            result.put("appId", config.getAppId());
            result.put("mchId", config.getMchId());

            if (isValid) {
                // 测试配置连接
                boolean testResult = wechatPayConfigService.testConfig(config);
                result.put("connected", testResult);
                result.put("message", testResult ? "配置测试通过" : "配置测试失败");
            } else {
                result.put("connected", false);
                result.put("message", "配置验证失败");
            }

            return R.data(result);
        } catch (Exception e) {
            log.error("测试支付配置失败", e);
            return R.fail("测试支付配置失败: " + e.getMessage());
        }
    }

    /**
     * 检查支付配置状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查配置状态", description = "检查微信支付配置的整体状态")
    public R<Map<String, Object>> checkStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 检查默认配置
            WechatPayConfig defaultConfig = wechatPayConfigService.getDefaultConfig();
            status.put("hasDefaultConfig", defaultConfig != null);

            if (defaultConfig != null) {
                status.put("defaultConfigName", defaultConfig.getConfigName());
                status.put("defaultConfigValid", wechatPayConfigService.validateConfig(defaultConfig));
                status.put("isTemporary", "temporary_default".equals(defaultConfig.getConfigName()));
            }

            // 检查启用的配置数量
            List<WechatPayConfig> enabledConfigs = wechatPayConfigService.getEnabledConfigs();
            status.put("enabledConfigCount", enabledConfigs.size());

            // 检查是否需要初始化数据库
            boolean needsInit = defaultConfig == null || "temporary_default".equals(defaultConfig.getConfigName());
            status.put("needsDatabaseInit", needsInit);

            if (needsInit) {
                status.put("initMessage", "请执行 init_wechat_pay_config.sql 初始化数据库配置");
            }

            return R.data(status);
        } catch (Exception e) {
            log.error("检查配置状态失败", e);
            return R.fail("检查配置状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置初始化指南
     */
    @GetMapping("/init-guide")
    @Operation(summary = "获取初始化指南", description = "获取微信支付配置的初始化指南")
    public R<Map<String, Object>> getInitGuide() {
        Map<String, Object> guide = new HashMap<>();

        guide.put("title", "微信支付配置初始化指南");
        guide.put("steps", new String[]{
            "1. 执行 backend/src/main/resources/sql/init_wechat_pay_config.sql",
            "2. 修改配置中的 api_key 为真实的32位API密钥",
            "3. 修改 notify_url 为真实的回调地址",
            "4. 确保证书文件存在于指定路径",
            "5. 重启应用服务",
            "6. 使用 /test 接口测试配置"
        });

        guide.put("sqlPath", "backend/src/main/resources/sql/init_wechat_pay_config.sql");
        guide.put("configFields", new String[]{
            "app_id: 小程序AppID",
            "mch_id: 商户号",
            "api_key: API密钥（32位）",
            "notify_url: 支付回调地址",
            "cert_path: 证书路径",
            "private_key_path: 私钥路径"
        });

        return R.data(guide);
    }
}
