/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import org.springblade.business.post.dto.PostAuditDTO;
import org.springblade.business.post.dto.PostPublishDTO;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 百事通信息贴 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ISupPostService extends BaseService<SupPost> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param post
	 * @param publishTimeStart 发布时间开始
	 * @param publishTimeEnd 发布时间结束
	 * @return
	 */
	IPage<SupPostVO> selectPostPage(IPage<SupPostVO> page, SupPostVO post);


	/**
	 * 获取我的帖子
	 *
	 * @param current 页码
	 * @param size    每页大小
	 * @return 帖子列表
	 */
	IPage<SupPostVO> getMyPosts(Integer current, Integer size);

	/**
	 * 获取帖子列表
	 *
	 * @param categoryId 分类ID
	 * @param keyword    关键词
	 * @param current    页码
	 * @param size       每页大小
	 * @return 帖子列表
	 */
	IPage<SupPostVO> getPostList(Long categoryId, String keyword, Integer current, Integer size);

	/**
	 * 点赞帖子
	 *
	 * @param id     帖子ID
	 * @param openId 用户OpenID
	 * @return 是否成功
	 */
	Boolean likePost(Long id, String openId);

	/**
	 * 收藏帖子
	 *
	 * @param id     帖子ID
	 * @param openId 用户OpenID
	 * @return 是否成功
	 */
	Boolean favoritePost(Long id, String openId);

	/**
	 * 获取收藏的帖子
	 *
	 * @param openId  用户OpenID
	 * @param current 页码
	 * @param size    每页大小
	 * @return 帖子列表
	 */
	IPage<SupPostVO> getFavoritePosts(String openId, Integer current, Integer size);

	/**
	 * 审核帖子
	 *
	 * @param postAuditDTO 审核DTO
	 * @return 是否成功
	 */
	Boolean auditPost(PostAuditDTO postAuditDTO);

	/**
	 * 批量审核帖子
	 *
	 * @param postIds     帖子ID列表
	 * @param auditStatus 审核状态
	 * @param auditRemark 审核备注
	 * @return 是否成功
	 */
	Boolean batchAuditPosts(List<Long> postIds, String auditStatus, String auditRemark);

	/**
	 * 置顶帖子
	 *
	 * @param id    帖子ID
	 * @param isTop 是否置顶
	 * @return 是否成功
	 */
	Boolean topPost(Long id, Boolean isTop);

	/**
	 * 获取审核统计
	 *
	 * @return 统计数据
	 */
	Map<String, Object> getAuditStats();

	/**
	 * 获取帖子统计
	 *
	 * @return 统计数据
	 */
	Map<String, Object> getPostStats();

	Long getPostCountByUserId(Long id);

	IPage<SupPostVO> getPostListByUserId(Long userId, Query query);

	SupPostVO getPostDetail(Long id);

	boolean remove(List<Long> longList);

    Long getPostCountByUserIdAndDate(Long userId, LocalDate start, LocalDate end);

	/**
	 * 修改帖子发布状态
	 *
	 * @param id            帖子ID
	 * @param publishStatus 发布状态：1-上架，0-下架
	 * @return 是否成功
	 */
	Boolean updatePublishStatus(Long id, String publishStatus);

	/**
	 * 批量修改帖子发布状态
	 *
	 * @param postIds       帖子ID列表
	 * @param publishStatus 发布状态：1-上架，0-下架
	 * @return 是否成功
	 */
	Boolean batchUpdatePublishStatus(List<Long> postIds, String publishStatus);
}
