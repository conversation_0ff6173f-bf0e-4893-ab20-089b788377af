/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.service.ICategoryService;
import org.springblade.business.post.service.ICategoryTagService;
import org.springblade.business.post.service.ITagService;
import org.springblade.business.post.vo.CategoryVO;
import org.springblade.business.post.wrapper.CategoryWrapper;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广告分类 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/category")
@io.swagger.v3.oas.annotations.tags.Tag(name = "广告分类", description = "广告分类接口")
@Validated
public class CategoryController extends BladeController {

	private ICategoryService categoryService;
	private ITagService tagService;
	private ICategoryTagService categoryTagService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入category")
	public R<CategoryVO> detail(Category category) {
		Category detail = categoryService.getOne(Condition.getQueryWrapper(category));
		return R.data(CategoryWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 广告分类
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入category")
	public R<List<Category>> list() {
		return R.data(categoryService.list());
	}

	/**
	 * 自定义分页 广告分类
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入category")
	public R<IPage<CategoryVO>> page(CategoryVO category, Query query) {
		IPage<CategoryVO> pages = categoryService.selectCategoryPage(Condition.getPage(query), category);
		return R.data(pages);
	}

	/**
	 * 新增 广告分类
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入category")
	public R save(@Valid @RequestBody Category category) {
		boolean result = categoryService.createCategory(category);
		return R.status(result);
	}

	/**
	 * 修改 广告分类
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入category")
	public R update(@Valid @RequestBody Category category) {
		boolean result = categoryService.updateByCategoryId(category);
		return R.status(result);
	}

	/**
	 * 新增或修改 广告分类
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入category")
	public R submit(@Valid @RequestBody Category category) {
		boolean result = categoryService.saveOrUpdateCategory(category);
		return R.status(result);
	}

	/**
	 * 删除 广告分类
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = categoryService.deleteLogicCategory(ids);
		return R.status(result);
	}

	// ==================== 后台管理相关接口 ====================

	/**
	 * 获取分类列表（后台管理）
	 */

	@GetMapping("/admin/list")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取分类列表", description = "后台管理获取分类列表")
	public R<IPage<CategoryVO>> getCategoryList(@Parameter(description = "分类名称") @RequestParam(required = false) String name,
												@Parameter(description = "是否启用") @RequestParam(required = false) Integer enabled,
												@Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
												@Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
		IPage<CategoryVO> categories = categoryService.selectCategoryPage(Page.of(current, size), new CategoryVO());
		return R.data(categories);
	}

	/**
	 * 获取分类树形结构
	 */

	@GetMapping("/admin/tree")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取分类树", description = "获取分类树形结构")
	public R<List<Category>> getCategoryTree() {
		List<Category> tree = categoryService.getCategoryTree();
		return R.data(tree);
	}

	/**
	 * 获取分类详情（后台管理）
	 */

	@GetMapping("/admin/detail/{id}")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取分类详情", description = "根据ID获取分类详情")
	public R<Category> getCategoryDetail(@Parameter(description = "分类ID") @PathVariable Long id) {
		Category category = categoryService.getCategoryDetail(id);
		return R.data(category);
	}

	/**
	 * 启用/禁用分类
	 */
	@PostMapping("/admin/{id}/enable")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "启用/禁用分类", description = "启用或禁用分类")
	public R<Boolean> enableCategory(@Parameter(description = "分类ID") @PathVariable Long id,
									 @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
		boolean result = categoryService.enableCategory(id, enabled);
		return R.data(result);
	}

	/**
	 * 启用/禁用分类审核
	 */
	@PostMapping("/admin/{id}/enable-audit")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "启用/禁用分类审核", description = "启用或禁用分类的审核功能")
	public R<Boolean> enableCategoryAudit(@Parameter(description = "分类ID") @PathVariable Long id,
										  @Parameter(description = "是否启用审核") @RequestParam Boolean enableAudit) {
		boolean result = categoryService.enableCategoryAudit(id, enableAudit);
		return R.data(result);
	}

	/**
	 * 标签排序DTO
	 */
	@Setter
	@Getter
	public static class TagSortDTO {
		private Long tagId;
		private Integer sortOrder;
	}

}
