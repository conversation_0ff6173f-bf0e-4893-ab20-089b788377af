<template>
  <basic-container>
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalStats.totalInvites || 0 }}</div>
            <div class="stat-label">总邀请数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalStats.totalRegistered || 0 }}</div>
            <div class="stat-label">总注册数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalStats.totalRewards || 0 }}</div>
            <div class="stat-label">总奖励发放</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ getConversionRate() }}%</div>
            <div class="stat-label">转化率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.invitestatistics_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
        <el-button type="warning"
                   icon="el-icon-pie-chart"
                   plain
                   @click="handleViewChart">图表分析
        </el-button>
      </template>

      <!-- 邀请人ID插槽 -->
      <template #inviterUserId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.inviterUserId)"
          class="user-link">
          {{ row.inviterUserId }}
        </el-button>
      </template>

      <!-- 总邀请次数插槽 -->
      <template #totalInvites="{ row }">
        <span class="number-text primary">{{ row.totalInvites || 0 }}</span>
      </template>

      <!-- 成功注册数插槽 -->
      <template #successfulRegistrations="{ row }">
        <span class="number-text success">{{ row.successfulRegistrations || 0 }}</span>
      </template>

      <!-- 奖励发放数插槽 -->
      <template #rewardsIssued="{ row }">
        <span class="number-text warning">{{ row.rewardsIssued || 0 }}</span>
      </template>

      <!-- 转化率插槽 -->
      <template #conversionRate="{ row }">
        <el-progress
          :percentage="getRowConversionRate(row)"
          :color="getProgressColor(getRowConversionRate(row))"
          :show-text="true"
          :format="() => `${getRowConversionRate(row)}%`">
        </el-progress>
      </template>

      <!-- 操作菜单插槽 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewStatDetail(row)">
          详情
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="viewUserInviteHistory(row)">
          邀请历史
        </el-button>
      </template>
    </avue-crud>

    <!-- 统计详情对话框 -->
    <el-dialog v-model="statDetailVisible" title="邀请统计详情" width="800px">
      <div v-if="selectedStat">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="邀请人">{{ selectedStat.inviterUserId }}</el-descriptions-item>
          <el-descriptions-item label="统计日期">{{ selectedStat.statisticsDate }}</el-descriptions-item>
          <el-descriptions-item label="总邀请次数">
            <span class="number-text primary">{{ selectedStat.totalInvites || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="成功注册数">
            <span class="number-text success">{{ selectedStat.successfulRegistrations || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="奖励发放数">
            <span class="number-text warning">{{ selectedStat.rewardsIssued || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="转化率">
            <el-progress
              :percentage="getRowConversionRate(selectedStat)"
              :color="getProgressColor(getRowConversionRate(selectedStat))">
            </el-progress>
          </el-descriptions-item>
          <el-descriptions-item label="累计邀请数">{{ selectedStat.cumulativeInvites || 0 }}</el-descriptions-item>
          <el-descriptions-item label="累计注册数">{{ selectedStat.cumulativeRegistrations || 0 }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/invitestatistics";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        statDetailVisible: false,
        selectedStat: null,
        totalStats: {
          totalInvites: 0,
          totalRegistered: 0,
          totalRewards: 0
        },
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'statisticsDate',
            order: 'descending'
          },
          // 字段配置
          column: [
            // 1. 重要字段优先
            {
              label: "邀请人ID",
              prop: "inviterUserId",
              search: true,
              placeholder: "请输入邀请人ID",
              rules: [{ required: true, message: "请输入邀请人ID", trigger: "blur" }]
            },
            {
              label: "统计日期",
              prop: "statisticsDate",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              search: true,
              placeholder: "请选择统计日期",
              rules: [{ required: true, message: "请输入统计日期", trigger: "blur" }]
            },
            // 2. 统计相关字段
            {
              label: "总邀请次数",
              prop: "totalInvites",
              type: "number",
              formatter: row => row.totalInvites ? row.totalInvites.toLocaleString() : '',
              search: false,
              placeholder: "请输入总邀请次数",
              rules: [{ required: true, message: "请输入总邀请次数", trigger: "blur" }]
            },
            {
              label: "成功注册人数",
              prop: "successfulRegistrations",
              type: "number",
              formatter: row => row.successfulRegistrations ? row.successfulRegistrations.toLocaleString() : '',
              search: false,
              placeholder: "请输入成功注册人数",
              rules: [{ required: true, message: "请输入成功注册人数", trigger: "blur" }]
            },
            {
              label: "二维码邀请次数",
              prop: "qrcodeInvites",
              type: "number",
              formatter: row => row.qrcodeInvites ? row.qrcodeInvites.toLocaleString() : '',
              search: false,
              placeholder: "请输入二维码邀请次数",
              rules: [{ required: true, message: "请输入二维码邀请次数", trigger: "blur" }]
            },
            {
              label: "链接邀请次数",
              prop: "linkInvites",
              type: "number",
              formatter: row => row.linkInvites ? row.linkInvites.toLocaleString() : '',
              search: false,
              placeholder: "请输入链接邀请次数",
              rules: [{ required: true, message: "请输入链接邀请次数", trigger: "blur" }]
            },
            {
              label: "分享邀请次数",
              prop: "shareInvites",
              type: "number",
              formatter: row => row.shareInvites ? row.shareInvites.toLocaleString() : '',
              search: false,
              placeholder: "请输入分享邀请次数",
              rules: [{ required: true, message: "请输入分享邀请次数", trigger: "blur" }]
            },
            // 3. 奖励相关字段
            {
              label: "总奖励积分",
              prop: "totalRewardPoints",
              type: "number",
              formatter: row => row.totalRewardPoints ? row.totalRewardPoints.toLocaleString() : '',
              append: "积分",
              search: false,
              placeholder: "请输入总奖励积分",
              rules: [{ required: true, message: "请输入总奖励积分", trigger: "blur" }]
            },
            {
              label: "总奖励金额(分)",
              prop: "totalRewardAmount",
              type: "number",
              formatter: row => row.totalRewardAmount ? row.totalRewardAmount.toLocaleString() : '',
              append: "分",
              search: false,
              placeholder: "请输入总奖励金额(分)",
              rules: [{ required: true, message: "请输入总奖励金额(分)", trigger: "blur" }]
            },
            {
              label: "转化率(%)",
              prop: "conversionRate",
              type: "number",
              formatter: row => row.conversionRate ? row.conversionRate + '%' : '',
              search: false,
              placeholder: "请输入转化率(%)",
              rules: [{ required: true, message: "请输入转化率(%)", trigger: "blur" }]
            },
            // 4. 备注
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              search: false,
              placeholder: "请输入备注信息",
              rules: [{ required: false, message: "请输入备注信息", trigger: "blur" }]
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.invitestatistics_add, false),
          viewBtn: this.validData(this.permission.invitestatistics_view, false),
          delBtn: this.validData(this.permission.invitestatistics_delete, false),
          editBtn: this.validData(this.permission.invitestatistics_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 排序处理
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      // 计算总转化率
      getConversionRate() {
        if (!this.totalStats.totalInvites || this.totalStats.totalInvites === 0) return 0;
        return Math.round((this.totalStats.totalRegistered / this.totalStats.totalInvites) * 100);
      },

      // 计算行转化率
      getRowConversionRate(row) {
        if (!row.totalInvites || row.totalInvites === 0) return 0;
        return Math.round((row.successfulRegistrations / row.totalInvites) * 100);
      },

      // 获取进度条颜色
      getProgressColor(percentage) {
        if (percentage >= 80) return '#67c23a';
        if (percentage >= 60) return '#e6a23c';
        if (percentage >= 40) return '#f56c6c';
        return '#909399';
      },

      // 查看统计详情
      viewStatDetail(row) {
        this.selectedStat = row;
        this.statDetailVisible = true;
      },

      // 显示用户详情
      showUserDetail(userId) {
        if (!userId) {
          this.$message.warning('用户信息不存在');
          return;
        }
        this.$message.info(`查看用户详情: ${userId}`);
      },

      // 查看用户邀请历史
      viewUserInviteHistory(row) {
        this.$message.info(`查看用户 ${row.inviterUserId} 的邀请历史`);
        // 这里可以跳转到邀请记录页面并筛选该用户
      },

      // 查看图表分析
      handleViewChart() {
        this.$message.info('图表分析功能开发中...');
      },

      // 刷新数据
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },

      // 导出数据
      handleExport() {
        this.$message.info('导出功能开发中...');
      },

      // 计算总统计数据
      calculateTotalStats() {
        this.totalStats = {
          totalInvites: this.data.reduce((sum, item) => sum + (item.totalInvites || 0), 0),
          totalRegistered: this.data.reduce((sum, item) => sum + (item.successfulRegistrations || 0), 0),
          totalRewards: this.data.reduce((sum, item) => sum + (item.rewardsIssued || 0), 0)
        };
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
          // 计算总统计数据
          this.calculateTotalStats();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error('加载数据失败');
        });
      }
    }
  };
</script>

<style scoped>
/* 统计卡片样式 */
.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 数字文本样式 */
.number-text {
  font-weight: bold;
  font-size: 16px;
}

.number-text.primary {
  color: #409EFF;
}

.number-text.success {
  color: #67c23a;
}

.number-text.warning {
  color: #e6a23c;
}

/* 用户链接样式 */
.user-link {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
}

.user-link:hover {
  color: #66b1ff;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 搜索区域样式优化 */
:deep(.avue-crud__search) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
}

/* 进度条样式 */
:deep(.el-progress) {
  width: 100px;
}

:deep(.el-progress__text) {
  font-size: 12px !important;
}
</style>
