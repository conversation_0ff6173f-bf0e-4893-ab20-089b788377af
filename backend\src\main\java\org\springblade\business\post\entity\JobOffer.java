/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@TableName("urb_job_offer")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "JobOffer对象")
public class JobOffer extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 关联帖子表ID
     */
    @Schema(description = "关联帖子表ID")
    private Long postId;
    /**
     * 招聘类型
     */
    @Schema(description = "招聘类型")
    private String recruitType;
    /**
     * 工作地址
     */
    @Schema(description = "工作地址")
    private String workplace;
    /**
     * 职位名称
     */
    @Schema(description = "职位名称")
    private String jobTitle;
    /**
     * 招聘人数
     */
    @Schema(description = "招聘人数")
    private Integer headcount;
    /**
     * 工资
     */
    @Schema(description = "工资")
    private String salary;
    /**
     * 职位关键词
     */
    @Schema(description = "职位关键词")
    private String jobKeywords;

	/**
	 * 职位描述
	 */
	@Schema(description = "职位描述")
	private String jobDescription;

	/**
	 * 公司名字
	 */
	@Schema(description = "公司名字")
	private String companyName;


}
