/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.service.impl;

import jakarta.annotation.Resource;
import org.springblade.business.points.entity.PointsGoods;
import org.springblade.business.points.mapper.PointsGoodsMapper;
import org.springblade.business.points.service.IPointsGoodsService;
import org.springblade.business.points.vo.PointsGoodsVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.springblade.common.cache.CacheNames.WECHAT_POINTS_GOODS_LIST;

/**
 * 积分商城商品表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class PointsGoodsServiceImpl extends BaseServiceImpl<PointsGoodsMapper, PointsGoods> implements IPointsGoodsService {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;


@Override
public IPage<PointsGoodsVO> selectPointsGoodsPage(IPage<PointsGoodsVO> page, PointsGoodsVO pointsGoods) {
	// 构造缓存key，建议带上分页参数
	String cacheKey = WECHAT_POINTS_GOODS_LIST;

	// 1. 先查缓存
	Map<String, Object> cachedData = (Map<String, Object>) redisTemplate.opsForValue().get(cacheKey);
	if (cachedData != null) {
		// 2. 还原IPage对象
		page.setRecords((List<PointsGoodsVO>) cachedData.get("records"));
		page.setTotal((Long) cachedData.get("total"));
		page.setSize((Long) cachedData.get("size"));
		page.setCurrent((Long) cachedData.get("current"));
		page.setPages((Long) cachedData.get("pages"));
		return page;
	}

	// 3. 查数据库
	List<PointsGoodsVO> records = baseMapper.selectPointsGoodsPage(page, pointsGoods);
	page.setRecords(records);

	// 4. 转为Map结构缓存
	Map<String, Object> cacheData = new HashMap<>();
	cacheData.put("records", records);
	cacheData.put("total", page.getTotal());
	cacheData.put("size", page.getSize());
	cacheData.put("current", page.getCurrent());
	cacheData.put("pages", page.getPages());
	redisTemplate.opsForValue().set(cacheKey, cacheData, 30, TimeUnit.MINUTES);

	return page;
}

	@Override
	@Cacheable(cacheNames = WECHAT_POINTS_GOODS_LIST)
	public boolean savePointsGoods(PointsGoods pointsGoods) {
		return this.save(pointsGoods);
	}

	@Override
	@Cacheable(cacheNames = WECHAT_POINTS_GOODS_LIST)
	public boolean updatePointsGoodsById(PointsGoods pointsGoods) {
		return this.updateById(pointsGoods);
	}

	@Override
	@Cacheable(cacheNames = WECHAT_POINTS_GOODS_LIST)
	public boolean saveOrUpdatePointsGoods(PointsGoods pointsGoods) {
		return this.saveOrUpdate(pointsGoods);
	}

	@Override
	@Cacheable(cacheNames = WECHAT_POINTS_GOODS_LIST)
	public boolean deleteLogicPointsGoods(String ids) {
		return this.deleteLogic(Func.toLongList(ids));
	}

}
