/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.business.user.service.IUserBalanceService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 用户余额控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/api/user/balance")
@Tag(name = "用户余额", description = "用户钱包余额相关接口")
public class UserBalanceController extends BladeController {

    private final IUserBalanceService userBalanceService;

    /**
     * 获取当前用户钱包余额
     */
    @GetMapping("/wallet")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取钱包余额", description = "获取当前用户的钱包余额")
    public R<BigDecimal> getWalletBalance() {
        BladeUser user = AuthUtil.getUser();
        if (user == null) {
            return R.fail("用户未登录");
        }

        BigDecimal balance = userBalanceService.getUserWalletBalance(user.getUserId());
        return R.data(balance);
    }

    /**
     * 获取指定用户钱包余额（管理员接口）
     */
    @GetMapping("/wallet/{userId}")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "获取指定用户钱包余额", description = "管理员获取指定用户的钱包余额")
    public R<BigDecimal> getUserWalletBalance(@Parameter(description = "用户ID") @PathVariable Long userId) {
        BigDecimal balance = userBalanceService.getUserWalletBalance(userId);
        return R.data(balance);
    }

    /**
     * 检查钱包余额是否足够
     */
    @PostMapping("/wallet/check")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "检查余额是否足够", description = "检查当前用户钱包余额是否足够支付指定金额")
    public R<Boolean> checkWalletBalance(@Parameter(description = "需要检查的金额") @RequestParam BigDecimal amount) {
        BladeUser user = AuthUtil.getUser();
        if (user == null) {
            return R.fail("用户未登录");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return R.fail("金额参数无效");
        }

        boolean sufficient = userBalanceService.checkWalletBalance(user.getUserId(), amount);
        return R.data(sufficient);
    }

    /**
     * 管理员增加用户余额
     */
    @PostMapping("/wallet/add")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "增加用户余额", description = "管理员为用户增加钱包余额")
    public R<Boolean> addWalletBalance(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "增加金额") @RequestParam BigDecimal amount,
            @Parameter(description = "备注") @RequestParam(required = false) String remark) {

        if (userId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return R.fail("参数无效");
        }

        boolean result = userBalanceService.addWalletBalance(
            userId,
            amount,
            "REWARD",
            "SYSTEM_REWARD",
            null,
            null,
            remark != null ? remark : "管理员增加余额"
        );

        return R.status(result);
    }

    /**
     * 管理员扣减用户余额
     */
    @PostMapping("/wallet/deduct")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "扣减用户余额", description = "管理员扣减用户钱包余额")
    public R<Boolean> deductWalletBalance(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "扣减金额") @RequestParam BigDecimal amount,
            @Parameter(description = "备注") @RequestParam(required = false) String remark) {

        if (userId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return R.fail("参数无效");
        }

        boolean result = userBalanceService.deductWalletBalance(
            userId,
            amount,
            "DEDUCT",
            "SYSTEM_DEDUCT",
            null,
            null,
            remark != null ? remark : "管理员扣减余额"
        );

        return R.status(result);
    }
}
