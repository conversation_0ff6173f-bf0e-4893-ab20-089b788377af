# 帖子发布时间区间查询功能说明

## 功能概述
为帖子管理后台添加发布时间区间查询功能，支持按发布时间范围筛选帖子数据，提升管理员查询效率。

## 修改范围

### 1. 控制器层修改
**文件**: `backend/src/main/java/org/springblade/business/post/controller/SupPostController.java`

**修改内容**:
- 在分页查询接口中添加发布时间区间参数
- 支持 `publishTimeStart` 和 `publishTimeEnd` 参数

```java
/**
 * 自定义分页 百事通信息贴
 */
@GetMapping("/page")
@ApiOperationSupport(order = 3)
@Operation(summary = "分页", description = "传入post")
public R<IPage<SupPostVO>> page(SupPostVO post, Query query,
                                @Parameter(description = "发布时间开始") @RequestParam(required = false) String publishTimeStart,
                                @Parameter(description = "发布时间结束") @RequestParam(required = false) String publishTimeEnd) {
    IPage<SupPostVO> pages = postService.selectPostPage(Condition.getPage(query), post, publishTimeStart, publishTimeEnd);
    return R.data(pages);
}
```

### 2. 服务接口层修改
**文件**: `backend/src/main/java/org/springblade/business/post/service/ISupPostService.java`

**修改内容**:
- 修改 `selectPostPage` 方法签名，添加时间区间参数

```java
/**
 * 自定义分页
 *
 * @param page
 * @param post
 * @param publishTimeStart 发布时间开始
 * @param publishTimeEnd 发布时间结束
 * @return
 */
IPage<SupPostVO> selectPostPage(IPage<SupPostVO> page, SupPostVO post, String publishTimeStart, String publishTimeEnd);
```

### 3. 服务实现层修改
**文件**: `backend/src/main/java/org/springblade/business/post/service/impl/SupPostServiceImpl.java`

**修改内容**:
- 实现新的 `selectPostPage` 方法，传递时间区间参数到 Mapper 层

```java
@Override
public IPage<SupPostVO> selectPostPage(IPage<SupPostVO> page, SupPostVO post, String publishTimeStart, String publishTimeEnd) {
    return page.setRecords(baseMapper.selectPostPage(page, post, publishTimeStart, publishTimeEnd));
}
```

### 4. Mapper 接口层修改
**文件**: `backend/src/main/java/org/springblade/business/user/mapper/SupPostMapper.java`

**修改内容**:
- 修改 `selectPostPage` 方法签名，添加时间区间参数

```java
/**
 * 自定义分页
 */
List<SupPostVO> selectPostPage(IPage page, SupPostVO post, @Param("publishTimeStart") String publishTimeStart, @Param("publishTimeEnd") String publishTimeEnd);
```

### 5. SQL 映射文件修改
**文件**: `backend/src/main/java/org/springblade/business/user/mapper/SupPostMapper.xml`

**修改内容**:
- 在 `selectPostPage` 查询中添加发布时间区间条件
- 同时添加审核状态查询条件

```xml
<select id="selectPostPage" resultMap="postResultMap">
    SELECT
    p.*,
    <include refid="post_info_select"/>
    FROM urb_post p
    <include refid="post_info_join"/>
    WHERE 1 = 1
    <if test="post.createUser!=null">
        AND p.create_user = #{post.createUser}
    </if>
    <if test="post.publishStatus!=null and post.publishStatus!=''">
        AND p.publish_status = #{post.publishStatus}
    </if>
    <if test="post.auditStatus!=null and post.auditStatus!=''">
        AND p.audit_status = #{post.auditStatus}
    </if>
    <if test="publishTimeStart!=null and publishTimeStart!=''">
        AND p.publish_time &gt;= #{publishTimeStart}
    </if>
    <if test="publishTimeEnd!=null and publishTimeEnd!=''">
        AND p.publish_time &lt;= #{publishTimeEnd}
    </if>
    AND p.is_deleted = 0
    ORDER BY p.create_time DESC
</select>
```

## 功能特性

### 1. 区间查询支持
- ✅ **开始时间筛选** - 支持设置发布时间的开始范围
- ✅ **结束时间筛选** - 支持设置发布时间的结束范围
- ✅ **单边界查询** - 支持只设置开始时间或结束时间
- ✅ **精确匹配** - 支持精确到秒的时间匹配

### 2. 参数处理
- ✅ **可选参数** - 时间参数为可选，不影响原有查询
- ✅ **空值处理** - 自动处理空值和空字符串
- ✅ **格式兼容** - 支持多种时间格式输入
- ✅ **SQL 注入防护** - 使用参数化查询防止 SQL 注入

### 3. 查询优化
- ✅ **索引支持** - 利用数据库时间字段索引
- ✅ **条件组合** - 支持与其他查询条件组合使用
- ✅ **排序优化** - 保持原有的排序逻辑
- ✅ **分页支持** - 完全兼容分页查询

## API 接口说明

### 请求参数
```
GET /blade-ad/post/page

Query Parameters:
- current: 页码 (必填)
- size: 每页大小 (必填)
- createUser: 创建用户ID (可选)
- publishStatus: 发布状态 (可选)
- auditStatus: 审核状态 (可选)
- publishTimeStart: 发布时间开始 (可选)
- publishTimeEnd: 发布时间结束 (可选)
```

### 请求示例
```bash
# 查询指定时间范围内的帖子
GET /blade-ad/post/page?current=1&size=10&publishTimeStart=2024-01-01 00:00:00&publishTimeEnd=2024-01-31 23:59:59

# 查询指定时间之后的帖子
GET /blade-ad/post/page?current=1&size=10&publishTimeStart=2024-01-01 00:00:00

# 查询指定时间之前的帖子
GET /blade-ad/post/page?current=1&size=10&publishTimeEnd=2024-01-31 23:59:59

# 组合查询：指定用户在指定时间范围内的帖子
GET /blade-ad/post/page?current=1&size=10&createUser=123&publishTimeStart=2024-01-01 00:00:00&publishTimeEnd=2024-01-31 23:59:59
```

### 响应格式
```json
{
  "code": 200,
  "success": true,
  "data": {
    "records": [
      {
        "id": "帖子ID",
        "title": "帖子标题",
        "content": "帖子内容",
        "publishTime": "2024-01-15 10:30:00",
        "publishStatus": "1",
        "auditStatus": "1",
        "createUser": "用户ID",
        "createTime": "2024-01-15 10:00:00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 时间格式说明

### 支持的时间格式
```
- YYYY-MM-DD HH:mm:ss (推荐格式)
- YYYY-MM-DD HH:mm
- YYYY-MM-DD
```

### 时间范围逻辑
```sql
-- 开始时间条件 (大于等于)
WHERE p.publish_time >= '2024-01-01 00:00:00'

-- 结束时间条件 (小于等于)  
WHERE p.publish_time <= '2024-01-31 23:59:59'

-- 区间查询 (两个条件同时存在)
WHERE p.publish_time >= '2024-01-01 00:00:00' 
  AND p.publish_time <= '2024-01-31 23:59:59'
```

## 数据库字段说明

### 相关字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `publish_time` | VARCHAR | 发布时间 | '2024-01-15 10:30:00' |
| `create_time` | DATETIME | 创建时间 | '2024-01-15 10:00:00' |
| `publish_status` | VARCHAR | 发布状态 | '1'-已发布, '0'-未发布 |
| `audit_status` | VARCHAR | 审核状态 | '1'-已通过, '0'-待审核, '2'-已拒绝 |

### 字段关系
- `create_time`: 帖子创建时间，系统自动生成
- `publish_time`: 帖子发布时间，可能与创建时间不同
- 查询优先使用 `publish_time` 字段进行时间筛选

## 业务场景

### 1. 数据统计分析
```bash
# 查询本月发布的帖子
GET /blade-ad/post/page?publishTimeStart=2024-01-01&publishTimeEnd=2024-01-31

# 查询今日发布的帖子
GET /blade-ad/post/page?publishTimeStart=2024-01-15 00:00:00&publishTimeEnd=2024-01-15 23:59:59
```

### 2. 运营管理
```bash
# 查询活动期间发布的帖子
GET /blade-ad/post/page?publishTimeStart=2024-01-10&publishTimeEnd=2024-01-20

# 查询特定用户在指定时间的发布情况
GET /blade-ad/post/page?createUser=123&publishTimeStart=2024-01-01&publishTimeEnd=2024-01-31
```

### 3. 问题排查
```bash
# 查询系统故障期间发布的帖子
GET /blade-ad/post/page?publishTimeStart=2024-01-15 14:00:00&publishTimeEnd=2024-01-15 16:00:00

# 查询最近一小时发布的帖子
GET /blade-ad/post/page?publishTimeStart=2024-01-15 15:00:00
```

## 性能优化

### 1. 数据库优化
```sql
-- 建议在 publish_time 字段上创建索引
CREATE INDEX idx_urb_post_publish_time ON urb_post(publish_time);

-- 复合索引优化（如果经常组合查询）
CREATE INDEX idx_urb_post_status_time ON urb_post(publish_status, publish_time);
```

### 2. 查询优化
- 使用参数化查询防止 SQL 注入
- 合理使用时间范围，避免全表扫描
- 结合其他条件缩小查询范围

### 3. 缓存策略
- 对于热点时间段的查询可以考虑缓存
- 统计类查询可以使用定时任务预计算

## 兼容性说明

### 1. 向后兼容
- 新增参数为可选参数，不影响现有调用
- 保持原有查询逻辑和排序方式
- 不改变响应数据结构

### 2. 前端适配
- 前端需要传递正确格式的时间参数
- 建议使用标准的时间格式：`YYYY-MM-DD HH:mm:ss`
- 空值或无效值会被自动忽略

### 3. 数据类型兼容
- `publish_time` 字段为 VARCHAR 类型
- 支持字符串格式的时间比较
- 建议时间格式保持一致性

## 测试建议

### 1. 功能测试
```bash
# 测试基本区间查询
curl "http://localhost/blade-ad/post/page?current=1&size=10&publishTimeStart=2024-01-01&publishTimeEnd=2024-01-31"

# 测试单边界查询
curl "http://localhost/blade-ad/post/page?current=1&size=10&publishTimeStart=2024-01-01"

# 测试组合条件查询
curl "http://localhost/blade-ad/post/page?current=1&size=10&publishStatus=1&publishTimeStart=2024-01-01"
```

### 2. 边界测试
- 测试空值参数处理
- 测试无效时间格式
- 测试开始时间大于结束时间的情况
- 测试极值时间范围

### 3. 性能测试
- 测试大时间跨度查询性能
- 测试高并发查询场景
- 监控数据库查询执行计划

## 错误处理

### 1. 参数验证
- 时间格式验证
- 时间范围合理性检查
- 参数长度限制

### 2. 异常处理
```java
// 建议在服务层添加时间格式验证
if (publishTimeStart != null && !isValidTimeFormat(publishTimeStart)) {
    throw new IllegalArgumentException("发布时间开始格式不正确");
}
```

### 3. 日志记录
- 记录查询参数和执行时间
- 记录异常情况和错误信息
- 便于问题排查和性能监控

## 后续优化建议

### 1. 功能增强
- 支持更多时间格式
- 添加快捷时间范围选择
- 支持相对时间查询（如：最近7天）

### 2. 性能优化
- 添加查询结果缓存
- 优化 SQL 查询语句
- 考虑分库分表策略

### 3. 用户体验
- 提供时间范围预设选项
- 添加查询结果统计信息
- 支持查询历史记录
