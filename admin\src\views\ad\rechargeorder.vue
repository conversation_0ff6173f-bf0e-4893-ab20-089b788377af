<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @sort-change="sortChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.rechargeorder_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-download"
                   plain
                   @click="handleExport">导 出
        </el-button>
      </template>

      <!-- 订单状态插槽 -->
      <template #orderStatus="{ row }">
        <el-tag :type="getOrderStatusType(row.orderStatus)">
          {{ getOrderStatusText(row.orderStatus) }}
        </el-tag>
      </template>

      <!-- 支付状态插槽 -->
      <template #paymentStatus="{ row }">
        <el-tag :type="getPaymentStatusType(row.paymentStatus)">
          {{ getPaymentStatusText(row.paymentStatus) }}
        </el-tag>
      </template>

      <!-- 支付方式插槽 -->
      <template #paymentMethod="{ row }">
        <el-tag :type="getPaymentMethodType(row.paymentMethod)">
          {{ getPaymentMethodText(row.paymentMethod) }}
        </el-tag>
      </template>

      <!-- 充值金额插槽 -->
      <template #rechargeAmount="{ row }">
        <span style="color: #f56c6c; font-weight: bold;">
          ¥{{ row.rechargeAmount }}
        </span>
      </template>

      <!-- 用户信息插槽 -->
      <template #userId="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.userId)"
          class="user-link">
          {{ getUserNickname(row.userId) }}
        </el-button>
      </template>

      <!-- 订单详情按钮 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="viewOrderDetail(row)">
          详情
        </el-button>
        <el-button
          v-if="row.orderStatus === 'PENDING'"
          type="warning"
          size="small"
          @click="handleCancelOrder(row)">
          取消
        </el-button>
      </template>
    </avue-crud>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="orderDetailVisible" title="充值订单详情" width="800px">
      <div v-if="selectedOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedOrder.userId }}</el-descriptions-item>
          <el-descriptions-item label="充值金额">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ selectedOrder.rechargeAmount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag :type="getPaymentMethodType(selectedOrder.paymentMethod)">
              {{ getPaymentMethodText(selectedOrder.paymentMethod) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(selectedOrder.orderStatus)">
              {{ getOrderStatusText(selectedOrder.orderStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusType(selectedOrder.paymentStatus)">
              {{ getPaymentStatusText(selectedOrder.paymentStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ selectedOrder.paymentTime || '未支付' }}</el-descriptions-item>
          <el-descriptions-item label="充值成功时间">{{ selectedOrder.successTime || '未完成' }}</el-descriptions-item>
          <el-descriptions-item label="订单过期时间">{{ selectedOrder.expireTime }}</el-descriptions-item>
          <el-descriptions-item label="客户端IP">{{ selectedOrder.clientIp }}</el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <div v-if="selectedOrder.paymentMethod === 'WECHAT_PAY'">
          <h4>微信支付信息：</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="预支付ID">{{ selectedOrder.wxPrepayId || '无' }}</el-descriptions-item>
            <el-descriptions-item label="交易号">{{ selectedOrder.wxTransactionId || '无' }}</el-descriptions-item>
            <el-descriptions-item label="商户订单号">{{ selectedOrder.wxOutTradeNo || '无' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div v-if="selectedOrder.remark">
          <el-divider />
          <h4>备注：</h4>
          <p>{{ selectedOrder.remark }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model="userDetailVisible"
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/rechargeorder";
  import {mapGetters} from "vuex";
  import userInfoMixin from '@/mixins/userInfoMixin';

  export default {
    mixins: [userInfoMixin],
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        sortParams: {
          ascs: [],
          descs: []
        },
        orderDetailVisible: false,
        selectedOrder: null,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "订单号",
              prop: "orderNo",
              width: 200,
              search: true,
              rules: [{
                required: true,
                message: "请输入订单号",
                trigger: "blur"
              }]
            },
            {
              label: "用户",
              prop: "userId",
              type: "select",
              width: 150,
              search: true,
              slot: true,
              dicData: [],
              props: {
                label: "nickname",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择用户",
                trigger: "blur"
              }]
            },
            {
              label: "充值金额",
              prop: "rechargeAmount",
              type: "number",
              width: 120,
              sortable: true,
              slot: true,
              rules: [{
                required: true,
                message: "请输入充值金额",
                trigger: "blur"
              }]
            },
            {
              label: "支付方式",
              prop: "paymentMethod",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '微信支付', value: 'WECHAT_PAY' },
                { label: '支付宝', value: 'ALIPAY' }
              ],
              rules: [{
                required: true,
                message: "请选择支付方式",
                trigger: "blur"
              }]
            },
            {
              label: "订单状态",
              prop: "orderStatus",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '待支付', value: 'PENDING' },
                { label: '已支付', value: 'PAID' },
                { label: '充值成功', value: 'SUCCESS' },
                { label: '充值失败', value: 'FAILED' },
                { label: '已取消', value: 'CANCELLED' }
              ],
              rules: [{
                required: true,
                message: "请选择订单状态",
                trigger: "blur"
              }]
            },
            {
              label: "支付状态",
              prop: "paymentStatus",
              type: "select",
              width: 120,
              search: true,
              slot: true,
              dicData: [
                { label: '未支付', value: 'UNPAID' },
                { label: '已支付', value: 'PAID' },
                { label: '已退款', value: 'REFUNDED' }
              ],
              rules: [{
                required: true,
                message: "请选择支付状态",
                trigger: "blur"
              }]
            },
            {
              label: "支付时间",
              prop: "paymentTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              search: true,
              searchSpan: 12,
              searchRange: true,
              sortable: true
            },
            {
              label: "充值成功时间",
              prop: "successTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              hide: true
            },
            {
              label: "订单过期时间",
              prop: "expireTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              hide: true
            },
            {
              label: "客户端IP",
              prop: "clientIp",
              width: 120,
              search: true,
              hide: true
            },
            {
              label: "支付配置ID",
              prop: "payConfigId",
              width: 120,
              hide: true
            },
            {
              label: "微信预支付ID",
              prop: "wxPrepayId",
              width: 200,
              hide: true
            },
            {
              label: "微信交易号",
              prop: "wxTransactionId",
              width: 200,
              hide: true
            },
            {
              label: "商户订单号",
              prop: "wxOutTradeNo",
              width: 200,
              hide: true
            },
            {
              label: "支付回调地址",
              prop: "notifyUrl",
              hide: true
            },
            {
              label: "设备信息",
              prop: "deviceInfo",
              hide: true
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              hide: true
            },
            {
              label: "订单类型",
              prop: "type",
              type: "select",
              dicData: [
                { label: '购买商品', value: '0' },
                { label: '充值', value: '1' }
              ],
              hide: true
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              sortable: true,
              search: true,
              searchSpan: 12,
              searchRange: true
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              width: 160,
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.rechargeorder_add, false),
          viewBtn: this.validData(this.permission.rechargeorder_view, false),
          delBtn: this.validData(this.permission.rechargeorder_delete, false),
          editBtn: this.validData(this.permission.rechargeorder_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 排序处理
      sortChange(val) {
        this.sortParams.ascs = [];
        this.sortParams.descs = [];

        if (val.order === 'ascending') {
          this.sortParams.ascs.push(val.prop);
        } else if (val.order === 'descending') {
          this.sortParams.descs.push(val.prop);
        }

        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      // 订单状态相关方法
      getOrderStatusType(status) {
        const statusMap = {
          'PENDING': 'warning',
          'PAID': 'primary',
          'SUCCESS': 'success',
          'FAILED': 'danger',
          'CANCELLED': 'info'
        };
        return statusMap[status] || 'info';
      },

      getOrderStatusText(status) {
        const statusMap = {
          'PENDING': '待支付',
          'PAID': '已支付',
          'SUCCESS': '充值成功',
          'FAILED': '充值失败',
          'CANCELLED': '已取消'
        };
        return statusMap[status] || '未知';
      },

      // 支付状态相关方法
      getPaymentStatusType(status) {
        const statusMap = {
          'UNPAID': 'warning',
          'PAID': 'success',
          'REFUNDED': 'danger'
        };
        return statusMap[status] || 'info';
      },

      getPaymentStatusText(status) {
        const statusMap = {
          'UNPAID': '未支付',
          'PAID': '已支付',
          'REFUNDED': '已退款'
        };
        return statusMap[status] || '未知';
      },

      // 支付方式相关方法
      getPaymentMethodType(method) {
        const methodMap = {
          'WECHAT_PAY': 'success',
          'ALIPAY': 'primary'
        };
        return methodMap[method] || 'info';
      },

      getPaymentMethodText(method) {
        const methodMap = {
          'WECHAT_PAY': '微信支付',
          'ALIPAY': '支付宝'
        };
        return methodMap[method] || '未知';
      },

      // 查看订单详情
      viewOrderDetail(row) {
        this.selectedOrder = row;
        this.orderDetailVisible = true;
      },



      // 取消订单
      handleCancelOrder(row) {
        this.$confirm('确定要取消这个订单吗？', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 这里调用取消订单的API
          this.$message.success('订单取消成功');
          this.onLoad(this.page);
        });
      },

      // 刷新数据
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },

      // 导出数据
      handleExport() {
        this.$message.info('导出功能开发中...');
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;

        // 加载用户字典
        this.loadUserDict();

        // 合并查询参数和排序参数
        const queryParams = Object.assign({}, params, this.query, this.sortParams);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error('加载数据失败');
        });
      }
    }
  };
</script>

<style scoped>
/* 金额显示样式 */
.amount-text {
  color: #f56c6c;
  font-weight: bold;
  font-size: 14px;
}

/* 状态标签样式 */
.el-tag {
  font-size: 12px;
}

/* 用户链接样式 */
.user-link {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
}

.user-link:hover {
  color: #66b1ff;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 搜索区域样式优化 */
:deep(.avue-crud__search) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
}
</style>
