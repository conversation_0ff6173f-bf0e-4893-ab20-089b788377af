package org.springblade.business.user.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.entity.RechargeOrder;
import org.springblade.business.user.service.IRechargeService;
import org.springblade.business.user.service.IWechatPayService;
import org.springblade.business.user.task.PaymentStatusSyncTask;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付状态管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/payment/status")
@Tag(name = "支付状态管理", description = "支付状态查询和同步接口")
public class PaymentStatusController {

    private final IRechargeService rechargeService;
    private final IWechatPayService wechatPayService;
    private final PaymentStatusSyncTask paymentStatusSyncTask;

    /**
     * 手动同步订单状态
     */
    @PostMapping("/sync/{orderNo}")
    @Operation(summary = "同步订单状态", description = "手动同步指定订单的支付状态")
    public R<Map<String, Object>> syncOrderStatus(
            @Parameter(description = "订单号") @PathVariable String orderNo) {

        if (orderNo == null || orderNo.trim().isEmpty()) {
            return R.fail("订单号不能为空");
        }

        try {
            // 获取订单信息
            RechargeOrder order = rechargeService.getRechargeOrderByOrderNo(orderNo);
            if (order == null) {
                return R.fail("订单不存在");
            }

            // 查询微信订单状态
            Map<String, Object> wxOrderStatus = wechatPayService.queryOrderStatus(order.getWxOutTradeNo());
            if (wxOrderStatus == null) {
                return R.fail("查询微信订单状态失败");
            }

            String tradeState = (String) wxOrderStatus.get("trade_state");
            String transactionId = (String) wxOrderStatus.get("transaction_id");
            String timeEnd = (String) wxOrderStatus.get("time_end");

            Map<String, Object> result = new HashMap<>();
            result.put("orderNo", orderNo);
            result.put("localStatus", order.getOrderStatus());
            result.put("localPaymentStatus", order.getPaymentStatus());
            result.put("wxTradeState", tradeState);
            result.put("wxTransactionId", transactionId);
            result.put("syncTime", System.currentTimeMillis());

            // 根据微信状态处理订单
            boolean syncResult = false;
            String syncMessage = "";

            switch (tradeState) {
                case "SUCCESS":
                    if (RechargeOrder.OrderStatus.PENDING.getCode().equals(order.getOrderStatus())) {
                        syncResult = rechargeService.handlePaymentSuccess(orderNo, transactionId, timeEnd);
                        syncMessage = syncResult ? "支付成功，充值完成" : "支付成功，但充值处理失败";
                    } else {
                        syncMessage = "订单已处理，无需同步";
                        syncResult = true;
                    }
                    break;

                case "REFUND":
                    syncMessage = "订单已退款";
                    syncResult = true;
                    break;

                case "CLOSED":
                    syncMessage = "订单已关闭";
                    syncResult = true;
                    break;

                case "REVOKED":
                    syncMessage = "订单已撤销";
                    syncResult = true;
                    break;

                case "NOTPAY":
                case "USERPAYING":
                    syncMessage = "订单未支付或支付中";
                    syncResult = true;
                    break;

                case "PAYERROR":
                    syncMessage = "支付失败";
                    syncResult = true;
                    break;

                default:
                    syncMessage = "未知的支付状态: " + tradeState;
                    break;
            }

            result.put("syncResult", syncResult);
            result.put("syncMessage", syncMessage);

            return R.data(result);

        } catch (Exception e) {
            log.error("同步订单状态异常: orderNo={}", orderNo, e);
            return R.fail("同步订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量同步待支付订单状态
     */
    @PostMapping("/sync/batch")
    @Operation(summary = "批量同步状态", description = "批量同步待支付订单状态")
    public R<Map<String, Object>> batchSyncOrderStatus() {
        try {
            log.info("手动触发批量同步订单状态");

            // 调用定时任务的同步方法
            paymentStatusSyncTask.syncPendingOrderStatus();

            Map<String, Object> result = new HashMap<>();
            result.put("syncTime", System.currentTimeMillis());
            result.put("message", "批量同步任务已触发，请查看日志了解同步结果");

            return R.data(result);

        } catch (Exception e) {
            log.error("批量同步订单状态异常", e);
            return R.fail("批量同步失败: " + e.getMessage());
        }
    }

    /**
     * 立即同步指定订单
     */
    @PostMapping("/sync/immediate/{orderNo}")
    @Operation(summary = "立即同步订单", description = "立即查询微信支付状态并同步订单")
    public R<Map<String, Object>> immediateSyncOrder(
            @Parameter(description = "订单号") @PathVariable String orderNo) {

        if (orderNo == null || orderNo.trim().isEmpty()) {
            return R.fail("订单号不能为空");
        }

        try {
            log.info("立即同步订单状态: orderNo={}", orderNo);

            // 获取订单信息
            RechargeOrder order = rechargeService.getRechargeOrderByOrderNo(orderNo);
            if (order == null) {
                return R.fail("订单不存在");
            }

            // 记录同步前状态
            String beforeStatus = order.getOrderStatus();
            String beforePaymentStatus = order.getPaymentStatus();

            // 查询微信订单状态
            Map<String, Object> wxOrderStatus = wechatPayService.queryOrderStatus(order.getWxOutTradeNo());
            if (wxOrderStatus == null) {
                return R.fail("查询微信订单状态失败，请检查网络连接和支付配置");
            }

            String tradeState = (String) wxOrderStatus.get("trade_state");
            String tradeStateDesc = (String) wxOrderStatus.get("trade_state_desc");
            String transactionId = (String) wxOrderStatus.get("transaction_id");
            String timeEnd = (String) wxOrderStatus.get("time_end");

            // 处理状态同步
            boolean syncResult = false;
            String syncMessage = "";

            switch (tradeState) {
                case "SUCCESS":
                    if ("PENDING".equals(beforeStatus) || "PAID".equals(beforeStatus)) {
                        syncResult = rechargeService.handlePaymentSuccess(orderNo, transactionId, timeEnd);
                        syncMessage = syncResult ? "支付成功，充值完成" : "支付成功，但充值处理失败";
                    } else {
                        syncMessage = "订单已是成功状态";
                        syncResult = true;
                    }
                    break;

                case "REFUND":
                    syncMessage = "订单已退款";
                    syncResult = true;
                    break;

                case "CLOSED":
                    syncMessage = "订单已关闭";
                    syncResult = true;
                    break;

                case "REVOKED":
                    syncMessage = "订单已撤销";
                    syncResult = true;
                    break;

                case "NOTPAY":
                case "USERPAYING":
                    syncMessage = "订单未支付或支付中，状态：" + tradeStateDesc;
                    syncResult = true;
                    break;

                case "PAYERROR":
                    syncMessage = "支付失败：" + tradeStateDesc;
                    syncResult = true;
                    break;

                default:
                    syncMessage = "未知的支付状态: " + tradeState;
                    break;
            }

            // 获取同步后的订单状态
            RechargeOrder updatedOrder = rechargeService.getRechargeOrderByOrderNo(orderNo);

            Map<String, Object> result = new HashMap<>();
            result.put("orderNo", orderNo);
            result.put("beforeStatus", beforeStatus);
            result.put("beforePaymentStatus", beforePaymentStatus);
            result.put("afterStatus", updatedOrder.getOrderStatus());
            result.put("afterPaymentStatus", updatedOrder.getPaymentStatus());
            result.put("wxTradeState", tradeState);
            result.put("wxTradeStateDesc", tradeStateDesc);
            result.put("wxTransactionId", transactionId);
            result.put("syncResult", syncResult);
            result.put("syncMessage", syncMessage);
            result.put("syncTime", System.currentTimeMillis());
            result.put("statusChanged", !beforeStatus.equals(updatedOrder.getOrderStatus()));

            log.info("订单同步完成: orderNo={}, {} -> {}, result={}",
                orderNo, beforeStatus, updatedOrder.getOrderStatus(), syncResult);

            return R.data(result);

        } catch (Exception e) {
            log.error("立即同步订单异常: orderNo={}", orderNo, e);
            return R.fail("同步订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询订单详细状态
     */
    @GetMapping("/detail/{orderNo}")
    @Operation(summary = "查询订单详细状态", description = "查询订单的本地状态和微信状态")
    public R<Map<String, Object>> getOrderStatusDetail(
            @Parameter(description = "订单号") @PathVariable String orderNo) {

        if (orderNo == null || orderNo.trim().isEmpty()) {
            return R.fail("订单号不能为空");
        }

        try {
            // 获取本地订单状态
            Map<String, Object> localStatus = rechargeService.queryRechargeOrderStatus(orderNo);
            if (localStatus == null) {
                return R.fail("订单不存在");
            }

            // 获取订单信息
            RechargeOrder order = rechargeService.getRechargeOrderByOrderNo(orderNo);

            // 查询微信订单状态
            Map<String, Object> wxStatus = null;
            if (order != null && order.getWxOutTradeNo() != null) {
                wxStatus = wechatPayService.queryOrderStatus(order.getWxOutTradeNo());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("orderNo", orderNo);
            result.put("localStatus", localStatus);
            result.put("wxStatus", wxStatus);
            result.put("queryTime", System.currentTimeMillis());

            return R.data(result);

        } catch (Exception e) {
            log.error("查询订单详细状态异常: orderNo={}", orderNo, e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取支付统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取支付统计", description = "获取支付相关的统计信息")
    public R<Map<String, Object>> getPaymentStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 这里可以添加各种统计信息
            stats.put("totalOrders", "统计功能开发中");
            stats.put("successRate", "统计功能开发中");
            stats.put("todayAmount", "统计功能开发中");
            stats.put("queryTime", System.currentTimeMillis());

            return R.data(stats);

        } catch (Exception e) {
            log.error("获取支付统计异常", e);
            return R.fail("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 手动清理过期订单
     */
    @PostMapping("/clean/expired")
    @Operation(summary = "清理过期订单", description = "手动清理过期未支付的订单")
    public R<Map<String, Object>> cleanExpiredOrders() {
        try {
            log.info("手动触发清理过期订单");

            // 调用定时任务的清理方法
            paymentStatusSyncTask.cleanExpiredOrders();

            Map<String, Object> result = new HashMap<>();
            result.put("cleanTime", System.currentTimeMillis());
            result.put("message", "过期订单清理任务已触发");

            return R.data(result);

        } catch (Exception e) {
            log.error("清理过期订单异常", e);
            return R.fail("清理失败: " + e.getMessage());
        }
    }
}
