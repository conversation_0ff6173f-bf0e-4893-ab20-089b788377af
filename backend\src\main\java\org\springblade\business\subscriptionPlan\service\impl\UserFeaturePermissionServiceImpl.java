/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springblade.business.subscriptionPlan.entity.Feature;
import org.springblade.business.subscriptionPlan.entity.UserFeaturePermission;
import org.springblade.business.subscriptionPlan.mapper.UserFeaturePermissionMapper;
import org.springblade.business.subscriptionPlan.service.IUserFeaturePermissionService;
import org.springblade.business.subscriptionPlan.vo.UserFeaturePermissionVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 用户功能权限表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class UserFeaturePermissionServiceImpl extends BaseServiceImpl<UserFeaturePermissionMapper, UserFeaturePermission> implements IUserFeaturePermissionService {

	@Override
	public IPage<UserFeaturePermissionVO> selectUserFeaturePermissionPage(IPage<UserFeaturePermissionVO> page, UserFeaturePermissionVO userFeaturePermission) {
		return page.setRecords(baseMapper.selectUserFeaturePermissionPage(page, userFeaturePermission));
	}

	@Override
	public boolean hasPermission(Long userId, String featureCode) {
		QueryWrapper<UserFeaturePermission> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("user_id", userId);
		queryWrapper.eq("feature_code", featureCode);
		if (this.count(queryWrapper) > 0) {
			return true;
		}
		return false;
	}

	@Override
	public List<Feature> selectFeaturesByUser(Long userId) {
		return baseMapper.selectFeaturesByUser(userId);
	}

	@Override
	public void deleteBySubscriptionId(Long id) {
	}

}
