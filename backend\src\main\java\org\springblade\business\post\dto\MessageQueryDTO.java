

package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.core.tool.node.INode;
import org.springblade.core.mp.support.Query;

import java.io.Serializable;

/**
 * 消息查询DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "消息查询DTO")
public class MessageQueryDTO extends Query implements Serializable {

    private static final long serialVersionUID = 1L;


	/**
	 * 用户id
	 */
	@Schema(description = "用户id")
	private Long userId;

    /**
     * 标题
     */
    @Schema(description = "消息标题")
    private String title;

    /**
     * 内容
     */
    @Schema(description = "消息内容")
    private String content;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型")
    private String messageType;

    /**
     * 是否已读
     */
    @Schema(description = "是否已读")
    private Boolean isRead;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String endTime;
}
