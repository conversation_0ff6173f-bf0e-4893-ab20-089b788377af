package org.springblade.business.user.vo;

import org.springblade.business.user.entity.BusinessCardFavorite;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 名片收藏表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "名片收藏表")
public class BusinessCardFavoriteVO extends BusinessCardFavorite {
    
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 原始名片信息
     */
    @Schema(description = "原始名片信息")
    private BusinessCardVO originalCard;

    /**
     * 收藏用户昵称
     */
    @Schema(description = "收藏用户昵称")
    private String userNickname;

    /**
     * 收藏用户头像
     */
    @Schema(description = "收藏用户头像")
    private String userAvatar;

    /**
     * 是否为当前用户收藏
     */
    @Schema(description = "是否为当前用户收藏")
    private Boolean isFavorited;

    /**
     * 是否为当前用户点赞
     */
    @Schema(description = "是否为当前用户点赞")
    private Boolean isLikedByUser;

    /**
     * 点赞数量
     */
    @Schema(description = "点赞数量")
    private Integer likeCount;

    /**
     * 收藏数量
     */
    @Schema(description = "收藏数量")
    private Integer favoriteCount;
}
