package org.springblade.business.user.service;

import cn.hutool.core.date.DateUtil;
import org.springblade.core.cache.utils.CacheUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Service
public class RedisRequestStatsService{

    // 缓存名称
    private static final String REQUEST_STATS_CACHE = "blade:request:stats";

    // 缓存key前缀
    private static final String KEY_ACTIVE_USERS_PREFIX = "active:";
    private static final String KEY_REQUEST_COUNT_PREFIX = "count:";
    private static final String KEY_LAST_REQUEST_TIME_PREFIX = "last_time:";
    private static final int ACTIVE_DAYS_TO_KEEP = 7; // 保留7天数据

    /**
     * 记录用户请求信息
     */
    public void recordRequest(String userId) {
        try {
            String today = DateUtil.format(new Date(), "yyyyMMdd");

            // 1. 记录今日活跃用户 (使用HashSet存储活跃用户列表)
            String activeUsersKey = today;
            HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
            if (activeUsers == null) {
                activeUsers = new HashSet<>();
            }
            activeUsers.add(userId);
            CacheUtil.put(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey, activeUsers);

            // 2. 记录用户请求次数 (使用CacheUtil)
            String countKey = userId;
            Integer currentCount = CacheUtil.get(REQUEST_STATS_CACHE, KEY_REQUEST_COUNT_PREFIX, countKey, Integer.class);
            int newCount = (currentCount != null ? currentCount : 0) + 1;
            CacheUtil.put(REQUEST_STATS_CACHE, KEY_REQUEST_COUNT_PREFIX, countKey, newCount);

            // 3. 记录最后请求时间 (使用CacheUtil)
            long timestamp = System.currentTimeMillis() / 1000;
            CacheUtil.put(REQUEST_STATS_CACHE, KEY_LAST_REQUEST_TIME_PREFIX, userId, (int) timestamp);

        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响主业务
            System.err.println("记录用户请求信息失败: userId=" + userId + ", error=" + e.getMessage());
        }
    }

    /**
     * 获取今日活跃用户数
     */
    public Long getTodayActiveUserCount() {
        try {
            String today = DateUtil.format(new Date(), "yyyyMMdd");
            String activeUsersKey = today;

            HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
            return activeUsers != null ? (long) activeUsers.size() : 0L;
        } catch (Exception e) {
            System.err.println("获取今日活跃用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取指定日期的活跃用户数
     */
    public Long getActiveUserCount(String date) {
        try {
            String activeUsersKey = date;
            HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
            return activeUsers != null ? (long) activeUsers.size() : 0L;
        } catch (Exception e) {
            System.err.println("获取指定日期活跃用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取近N天的活跃用户数 (去重)
     */
    public Long getActiveUserCountInDays(int days) {
        try {
            HashSet<String> allActiveUsers = new HashSet<>();

            // 遍历近N天的数据，合并所有活跃用户
            for (int i = 0; i < days; i++) {
                Date targetDate = DateUtil.offsetDay(new Date(), -i);
                String date = DateUtil.format(targetDate, "yyyyMMdd");
                String activeUsersKey = date;

                HashSet<String> dayActiveUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
                if (dayActiveUsers != null) {
                    allActiveUsers.addAll(dayActiveUsers);
                }
            }

            return (long) allActiveUsers.size();
        } catch (Exception e) {
            System.err.println("获取近N天活跃用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取用户的总请求次数
     */
    public String getUserRequestCount(String userId) {
        try {
            Integer count = CacheUtil.get(REQUEST_STATS_CACHE, KEY_REQUEST_COUNT_PREFIX, userId, Integer.class);
            return count != null ? count.toString() : "0";
        } catch (Exception e) {
            System.err.println("获取用户请求次数失败: userId=" + userId + ", error=" + e.getMessage());
            return "0";
        }
    }


    /**
     * 获取用户的最后请求时间
     */
    public Long getUserLastRequestTime(String userId) {
        try {
            Integer timestamp = CacheUtil.get(REQUEST_STATS_CACHE, KEY_LAST_REQUEST_TIME_PREFIX, userId, Integer.class);
            return timestamp != null ? timestamp.longValue() : null;
        } catch (Exception e) {
            System.err.println("获取用户最后请求时间失败: userId=" + userId + ", error=" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取指定日期的活跃用户列表
     */
    public Set<Object> getActiveUserList(String date) {
        try {
            String activeUsersKey = date;
            HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);

            if (activeUsers != null) {
                // 将HashSet<String>转换为Set<Object>
                return new HashSet<>(activeUsers);
            } else {
                return new HashSet<>();
            }
        } catch (Exception e) {
            System.err.println("获取活跃用户列表失败: " + e.getMessage());
            return new HashSet<>();
        }
    }

    /**
     * 获取今日活跃用户列表
     */
    public Set<String> getTodayActiveUsers() {
        try {
            String today = DateUtil.format(new Date(), "yyyyMMdd");
            String activeUsersKey = today;

            HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
            return activeUsers != null ? activeUsers : new HashSet<>();
        } catch (Exception e) {
            System.err.println("获取今日活跃用户列表失败: " + e.getMessage());
            return new HashSet<>();
        }
    }

    /**
     * 生成今日活跃用户Key
     */
    private String getTodayActiveUsersKey() {
        return KEY_ACTIVE_USERS_PREFIX + DateUtil.format(new Date(),"YYYYMMdd");
    }

    /**
     * 生成近N天的活跃用户Key列表
     */
    private List<String> generateActiveUserKeys(int days) {
        List<String> keys = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < days; i++) {
            String date = DateUtil.format(new Date(),"YYYYMMdd");
            keys.add(KEY_ACTIVE_USERS_PREFIX + date);
        }
        return keys;
    }

    /**
     * 清理用户请求统计缓存
     */
    public void clearUserRequestStats(String userId) {
        try {
            // 清理请求次数缓存
            CacheUtil.evict(REQUEST_STATS_CACHE, KEY_REQUEST_COUNT_PREFIX, userId);

            // 清理最后请求时间缓存
            CacheUtil.evict(REQUEST_STATS_CACHE, KEY_LAST_REQUEST_TIME_PREFIX, userId);

            // 清理活跃用户缓存（从HashSet中移除该用户）
            for (int i = 0; i < ACTIVE_DAYS_TO_KEEP; i++) {
                String date = DateUtil.format(DateUtil.offsetDay(new Date(), -i), "yyyyMMdd");
                String activeUsersKey = date;

                HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
                if (activeUsers != null && activeUsers.contains(userId)) {
                    activeUsers.remove(userId);
                    if (activeUsers.isEmpty()) {
                        // 如果HashSet为空，删除整个缓存项
                        CacheUtil.evict(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
                    } else {
                        // 否则更新HashSet
                        CacheUtil.put(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey, activeUsers);
                    }
                }
            }

            System.out.println("清理用户请求统计缓存成功: userId=" + userId);
        } catch (Exception e) {
            System.err.println("清理用户请求统计缓存失败: userId=" + userId + ", error=" + e.getMessage());
        }
    }

    /**
     * 清理所有请求统计缓存
     */
    public void clearAllRequestStats() {
        try {
            CacheUtil.clear(REQUEST_STATS_CACHE);
            System.out.println("清理所有请求统计缓存成功");
        } catch (Exception e) {
            System.err.println("清理所有请求统计缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户今日是否活跃
     */
    public boolean isUserActiveToday(String userId) {
        try {
            String today = DateUtil.format(new Date(), "yyyyMMdd");
            return isUserActiveOnDate(userId, today);
        } catch (Exception e) {
            System.err.println("检查用户今日活跃状态失败: userId=" + userId + ", error=" + e.getMessage());
            return false;
        }
    }

    /**
     * 检查用户在指定日期是否活跃
     */
    public boolean isUserActiveOnDate(String userId, String date) {
        try {
            String activeUsersKey = date;
            HashSet<String> activeUsers = (HashSet<String>) CacheUtil.get(REQUEST_STATS_CACHE, KEY_ACTIVE_USERS_PREFIX, activeUsersKey);
            return activeUsers != null && activeUsers.contains(userId);
        } catch (Exception e) {
            System.err.println("检查用户指定日期活跃状态失败: userId=" + userId + ", date=" + date + ", error=" + e.getMessage());
            return false;
        }
    }

}
