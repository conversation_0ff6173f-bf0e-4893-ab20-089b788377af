-- 邀请推广记录表
CREATE TABLE `urb_invite_record` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `inviter_user_id` varchar(64) NOT NULL COMMENT '邀请人用户ID',
  `invitee_user_id` varchar(64) DEFAULT NULL COMMENT '被邀请人用户ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `invite_type` varchar(20) NOT NULL DEFAULT 'qrcode' COMMENT '邀请类型：qrcode-二维码邀请，link-链接邀请，share-分享邀请',
  `invite_source` varchar(20) NOT NULL DEFAULT 'miniapp' COMMENT '邀请来源：miniapp-小程序，h5-H5页面，app-APP',
  `register_status` tinyint NOT NULL DEFAULT '0' COMMENT '注册状态：0-未注册，1-已注册，2-注册失败',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `reward_status` tinyint NOT NULL DEFAULT '0' COMMENT '奖励状态：0-未发放，1-已发放，2-发放失败',
  `reward_type` varchar(20) DEFAULT NULL COMMENT '奖励类型：points-积分，coupon-优惠券，cash-现金',
  `reward_amount` int DEFAULT NULL COMMENT '奖励金额/数量',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `ext_info` text COMMENT '扩展信息（JSON格式）',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int NOT NULL DEFAULT '1' COMMENT '业务状态',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  UNIQUE KEY `uk_invitee_user_id` (`invitee_user_id`),
  KEY `idx_inviter_user_id` (`inviter_user_id`),
  KEY `idx_register_status` (`register_status`),
  KEY `idx_reward_status` (`reward_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邀请推广记录表';

-- 邀请推广统计表
CREATE TABLE `urb_invite_statistics` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `inviter_user_id` varchar(64) NOT NULL COMMENT '邀请人用户ID',
  `statistics_date` date NOT NULL COMMENT '统计日期',
  `total_invites` int NOT NULL DEFAULT '0' COMMENT '总邀请次数',
  `successful_registrations` int NOT NULL DEFAULT '0' COMMENT '成功注册人数',
  `qrcode_invites` int NOT NULL DEFAULT '0' COMMENT '二维码邀请次数',
  `link_invites` int NOT NULL DEFAULT '0' COMMENT '链接邀请次数',
  `share_invites` int NOT NULL DEFAULT '0' COMMENT '分享邀请次数',
  `total_reward_points` int NOT NULL DEFAULT '0' COMMENT '总奖励积分',
  `total_reward_amount` int NOT NULL DEFAULT '0' COMMENT '总奖励金额（分）',
  `conversion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '转化率（百分比）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int NOT NULL DEFAULT '1' COMMENT '业务状态',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`inviter_user_id`,`statistics_date`),
  KEY `idx_statistics_date` (`statistics_date`),
  KEY `idx_inviter_user_id` (`inviter_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邀请推广统计表';

-- 插入测试数据
INSERT INTO `urb_invite_record` (`id`, `inviter_user_id`, `invite_code`, `invite_type`, `invite_source`, `register_status`, `reward_status`, `ext_info`) VALUES
(1, '1899127984496861186', 'TEST123', 'qrcode', 'miniapp', 0, 0, '{"qrCodeUrl":"https://example.com/qrcode.png","path":"pages/register/register?inviteCode=TEST123"}'),
(2, '1899127984496861186', 'TEST456', 'qrcode', 'miniapp', 1, 1, '{"qrCodeUrl":"https://example.com/qrcode2.png","path":"pages/register/register?inviteCode=TEST456"}');

-- 更新第二条记录的注册和奖励信息
UPDATE `urb_invite_record` SET
  `invitee_user_id` = '1899127984496861187',
  `register_time` = NOW(),
  `reward_type` = 'points',
  `reward_amount` = 100,
  `reward_time` = NOW()
WHERE `id` = 2;

-- 插入统计数据
INSERT INTO `urb_invite_statistics` (`id`, `inviter_user_id`, `statistics_date`, `total_invites`, `successful_registrations`, `qrcode_invites`, `total_reward_points`, `conversion_rate`) VALUES
(1, '1899127984496861186', CURDATE(), 2, 1, 2, 100, 50.00);

-- ========================================
-- 用户余额和充值相关表结构
-- 作者: AI Assistant
-- 创建时间: 2025-01-08
-- 说明: 用于支持用户余额管理和微信支付充值功能
-- ========================================

-- 1. 为用户表添加钱包余额字段（单位：元）
ALTER TABLE `urb_user`
ADD COLUMN `wallet_balance` decimal(10,2) DEFAULT '0.00' COMMENT '钱包余额（单位：元）' AFTER `balance`;

-- 2. 用户余额变动记录表
CREATE TABLE `urb_user_balance_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID（充值订单、消费订单等）',
  `change_type` varchar(20) NOT NULL COMMENT '变动类型：RECHARGE-充值，CONSUME-消费，REFUND-退款，REWARD-奖励，DEDUCT-扣减',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额（单位：元，正数为增加，负数为减少）',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变动前余额（单位：元）',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额（单位：元）',
  `business_type` varchar(30) DEFAULT NULL COMMENT '业务类型：POST_PUBLISH-发帖，POST_TOP-置顶，INVITE_REWARD-邀请奖励等',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务关联ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_business` (`business_type`, `business_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额变动记录';

-- 3. 充值订单表
CREATE TABLE `urb_recharge_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `pay_config_id` bigint DEFAULT NULL COMMENT '支付配置ID',
  `recharge_amount` decimal(10,2) NOT NULL COMMENT '充值金额（单位：元）',
  `payment_method` varchar(20) NOT NULL DEFAULT 'WECHAT_PAY' COMMENT '支付方式：WECHAT_PAY-微信支付，ALIPAY-支付宝',
  `order_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态：PENDING-待支付，PAID-已支付，SUCCESS-充值成功，FAILED-充值失败，CANCELLED-已取消',
  `payment_status` varchar(20) DEFAULT 'UNPAID' COMMENT '支付状态：UNPAID-未支付，PAID-已支付，REFUNDED-已退款',
  `wx_prepay_id` varchar(64) DEFAULT NULL COMMENT '微信预支付ID',
  `wx_transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易号',
  `wx_out_trade_no` varchar(64) DEFAULT NULL COMMENT '商户订单号',
  `payment_time` datetime(6) DEFAULT NULL COMMENT '支付时间',
  `success_time` datetime(6) DEFAULT NULL COMMENT '充值成功时间',
  `expire_time` datetime(6) DEFAULT NULL COMMENT '订单过期时间',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '支付回调地址',
  `client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
  `device_info` varchar(255) DEFAULT NULL COMMENT '设备信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  UNIQUE KEY `uk_wx_out_trade_no` (`wx_out_trade_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_wx_transaction_id` (`wx_transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值订单';

-- 4. 微信支付配置表（支持多商户）
CREATE TABLE `urb_wechat_pay_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(50) NOT NULL COMMENT '配置名称',
  `app_id` varchar(32) NOT NULL COMMENT '小程序AppID',
  `mch_id` varchar(32) NOT NULL COMMENT '商户号',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `api_v3_key` varchar(64) DEFAULT NULL COMMENT 'APIv3密钥',
  `cert_path` varchar(255) DEFAULT NULL COMMENT '证书路径',
  `cert_serial_no` varchar(64) DEFAULT NULL COMMENT '证书序列号',
  `private_key_path` varchar(255) DEFAULT NULL COMMENT '私钥路径',
  `notify_url` varchar(255) NOT NULL COMMENT '支付回调地址',
  `is_sandbox` tinyint(1) DEFAULT '0' COMMENT '是否沙箱环境',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认配置',
  `priority` int DEFAULT '0' COMMENT '优先级（数字越大优先级越高）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name` (`config_name`),
  UNIQUE KEY `uk_app_mch` (`app_id`, `mch_id`),
  KEY `idx_mch_id` (`mch_id`),
  KEY `idx_enabled_priority` (`is_enabled`, `priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付配置（支持多商户）';



-- 创建视图：用户余额详情
CREATE VIEW `v_user_balance_detail` AS
SELECT
    u.id as user_id,
    u.nickname,
    u.mobile,
    u.avatar,
    u.balance as points_balance,
    u.wallet_balance,
    (SELECT COUNT(*) FROM urb_user_balance_log WHERE user_id = u.id) as balance_log_count,
    (SELECT COUNT(*) FROM urb_recharge_order WHERE user_id = u.id AND order_status = 'SUCCESS') as recharge_count,
    (SELECT COALESCE(SUM(recharge_amount), 0.00) FROM urb_recharge_order WHERE user_id = u.id AND order_status = 'SUCCESS') as total_recharge_amount,
    u.create_time as register_time
FROM urb_user u
WHERE u.is_deleted = 0;
