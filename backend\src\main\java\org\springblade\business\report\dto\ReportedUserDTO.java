package org.springblade.business.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 被举报用户信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Schema(description = "被举报用户信息")
public class ReportedUserDTO implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 用户昵称
	 */
	@Schema(description = "用户昵称")
	private String nickname;

	/**
	 * 用户手机号
	 */
	@Schema(description = "用户手机号")
	private String mobile;

	/**
	 * 用户头像
	 */
	@Schema(description = "用户头像")
	private String avatar;

	/**
	 * 该用户被举报的总次数
	 */
	@Schema(description = "用户被举报总次数")
	private Integer userReportedCount;

	@Schema(description = "帖子被举报总次数")
	private Integer postReportedCount;
}
