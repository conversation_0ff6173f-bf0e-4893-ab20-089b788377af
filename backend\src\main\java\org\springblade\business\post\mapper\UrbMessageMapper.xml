<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.UrbMessageMapper">

    <!-- 多条件分页查询 -->
    <select id="selectByCondition" resultType="org.springblade.business.post.entity.UrbMessage">
        SELECT * FROM urb_message
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="content != null and content != ''">
                AND content LIKE CONCAT('%', #{content}, '%')
            </if>
            <if test="messageType != null and messageType != ''">
                AND message_type = #{messageType}
            </if>
            <if test="isRead != null">
                AND is_read = #{isRead}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- insert、updateById、deleteBatchIds由MyBatis-Plus自动实现，无需手写 -->

</mapper>
