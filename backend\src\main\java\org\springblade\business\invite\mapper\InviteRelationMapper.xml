<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.invite.mapper.InviteRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inviteRelationResultMap" type="org.springblade.miniapp.entity.InviteRelation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="inviter_user_id" property="inviterUserId"/>
        <result column="invitee_user_id" property="inviteeUserId"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="invite_code_id" property="inviteCodeId"/>
        <result column="invite_type" property="inviteType"/>
        <result column="invite_source" property="inviteSource"/>
        <result column="relation_status" property="relationStatus"/>
        <result column="register_time" property="registerTime"/>
        <result column="first_interaction_time" property="firstInteractionTime"/>
        <result column="confirm_time" property="confirmTime"/>
        <result column="reward_status" property="rewardStatus"/>
        <result column="reward_type" property="rewardType"/>
        <result column="reward_amount" property="rewardAmount"/>
        <result column="reward_time" property="rewardTime"/>
        <result column="channel_details" property="channelDetails"/>
        <result column="device_info" property="deviceInfo"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="location_info" property="locationInfo"/>
        <result column="remark" property="remark"/>
        <result column="ext_info" property="extInfo"/>
        <result column="status_flag" property="statusFlag"/>
    </resultMap>


    <select id="selectInviteRelationPage" resultMap="inviteRelationResultMap">
        select * from urb_invite_relation where is_deleted = 0
    </select>

</mapper>
