<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.UrbJobSeekingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="jobSeekingResultMap" type="org.springblade.business.post.entity.JobSeeking">
        <result column="id" property="id"/>
        <result column="post_id" property="postId"/>
        <result column="user_id" property="userId"/>
        <result column="seeking_type" property="seekingType"/>
        <result column="expected_position" property="expectedPosition"/>
        <result column="salary_expectation" property="salaryExpectation"/>
        <result column="position_preference" property="positionPreference"/>
    </resultMap>
    <delete id="deleteByPostId">
            delete from urb_job_seeking where post_id = #{postId}
    </delete>


    <select id="selectJobSeekingPage" resultMap="jobSeekingResultMap">
        select * from urb_job_seeking where is_deleted = 0
    </select>

</mapper>
