/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.subscriptionPlan.entity.UserSubscription;
import org.springblade.business.subscriptionPlan.vo.UserSubscriptionVO;

import java.util.List;

/**
 * 用户订阅表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface UserSubscriptionMapper extends BaseMapper<UserSubscription> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userSubscription
	 * @return
	 */
	List<UserSubscriptionVO> selectUserSubscriptionPage(IPage page, UserSubscriptionVO userSubscription);

}
