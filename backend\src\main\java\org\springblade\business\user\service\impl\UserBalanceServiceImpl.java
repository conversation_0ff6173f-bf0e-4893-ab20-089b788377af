/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.entity.UserBalanceLog;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.mapper.UserBalanceLogMapper;
import org.springblade.business.user.mapper.WeUserMapper;
import org.springblade.business.user.service.IUserBalanceService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户余额服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserBalanceServiceImpl implements IUserBalanceService {

    private final WeUserMapper weUserMapper;
    private final UserBalanceLogMapper userBalanceLogMapper;

    @Override
    public BigDecimal getUserWalletBalance(Long userId) {
        WeUser user = weUserMapper.selectById(userId);
        return user != null ? user.getWalletBalance() : BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWalletBalance(Long userId, BigDecimal amount, String changeType, String businessType,
                                   String businessId, String orderId, String remark) {
        if (userId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("增加钱包余额参数无效: userId={}, amount={}", userId, amount);
            return false;
        }

        try {
            // 获取当前余额
            WeUser user = weUserMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return false;
            }

            BigDecimal currentBalance = user.getWalletBalance() != null ? user.getWalletBalance() : BigDecimal.ZERO;
            BigDecimal newBalance = currentBalance.add(amount);

            // 更新用户余额
            LambdaUpdateWrapper<WeUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WeUser::getId, userId)
                        .set(WeUser::getWalletBalance, newBalance);

            int updateCount = weUserMapper.update(null, updateWrapper);
            if (updateCount <= 0) {
                log.warn("更新用户钱包余额失败: userId={}", userId);
                return false;
            }

            // 记录余额变动日志
            boolean logResult = logWalletBalanceChange(userId, amount, currentBalance, newBalance,
                                                     changeType, businessType, businessId, orderId, remark);

            if (!logResult) {
                log.warn("记录钱包余额变动日志失败: userId={}", userId);
                // 注意：这里不回滚事务，因为余额已经更新成功，日志失败不应该影响业务
            }

            log.info("增加用户钱包余额成功: userId={}, amount={}, newBalance={}", userId, amount, newBalance);
            return true;

        } catch (Exception e) {
            log.error("增加用户钱包余额异常: userId={}, amount={}", userId, amount, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductWalletBalance(Long userId, BigDecimal amount, String changeType, String businessType,
                                      String businessId, String orderId, String remark) {
        if (userId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("扣减钱包余额参数无效: userId={}, amount={}", userId, amount);
            return false;
        }

        try {
            // 获取当前余额
            WeUser user = weUserMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return false;
            }

            BigDecimal currentBalance = user.getWalletBalance() != null ? user.getWalletBalance() : BigDecimal.ZERO;

            // 检查余额是否足够
            if (currentBalance.compareTo(amount) < 0) {
                log.warn("用户钱包余额不足: userId={}, currentBalance={}, deductAmount={}", userId, currentBalance, amount);
                return false;
            }

            BigDecimal newBalance = currentBalance.subtract(amount);

            // 更新用户余额
            LambdaUpdateWrapper<WeUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WeUser::getId, userId)
                        .set(WeUser::getWalletBalance, newBalance);

            int updateCount = weUserMapper.update(null, updateWrapper);
            if (updateCount <= 0) {
                log.warn("更新用户钱包余额失败: userId={}", userId);
                return false;
            }

            // 记录余额变动日志（扣减金额为负数）
            boolean logResult = logWalletBalanceChange(userId, amount.negate(), currentBalance, newBalance,
                                                     changeType, businessType, businessId, orderId, remark);

            if (!logResult) {
                log.warn("记录钱包余额变动日志失败: userId={}", userId);
            }

            log.info("扣减用户钱包余额成功: userId={}, amount={}, newBalance={}", userId, amount, newBalance);
            return true;

        } catch (Exception e) {
            log.error("扣减用户钱包余额异常: userId={}, amount={}", userId, amount, e);
            throw e;
        }
    }

    @Override
    public boolean checkWalletBalance(Long userId, BigDecimal amount) {
        if (userId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        BigDecimal currentBalance = getUserWalletBalance(userId);
        return currentBalance.compareTo(amount) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean freezeWalletBalance(Long userId, BigDecimal amount, String orderId, String remark) {
        // 冻结余额的实现可以通过增加一个冻结金额字段来实现
        // 这里简化处理，直接扣减余额
        return deductWalletBalance(userId, amount, UserBalanceLog.ChangeType.DEDUCT.getCode(),
                                  UserBalanceLog.BusinessType.SYSTEM_DEDUCT.getCode(), null, orderId,
                                  "冻结余额: " + remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfreezeWalletBalance(Long userId, BigDecimal amount, String orderId, String remark) {
        // 解冻余额的实现，直接增加余额
        return addWalletBalance(userId, amount, UserBalanceLog.ChangeType.REFUND.getCode(),
                               UserBalanceLog.BusinessType.SYSTEM_REWARD.getCode(), null, orderId,
                               "解冻余额: " + remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean logWalletBalanceChange(Long userId, BigDecimal changeAmount, BigDecimal balanceBefore,
                                         BigDecimal balanceAfter, String changeType, String businessType,
                                         String businessId, String orderId, String remark) {
        try {
            UserBalanceLog balanceLog = new UserBalanceLog();
            balanceLog.setUserId(userId);
            balanceLog.setOrderId(orderId);
            balanceLog.setChangeType(changeType);
            balanceLog.setChangeAmount(changeAmount);
            balanceLog.setBalanceBefore(balanceBefore);
            balanceLog.setBalanceAfter(balanceAfter);
            balanceLog.setBusinessType(businessType);
            balanceLog.setBusinessId(businessId);
            balanceLog.setRemark(remark);

            int insertCount = userBalanceLogMapper.insert(balanceLog);

            log.info("记录钱包余额变动日志: userId={}, changeAmount={}, changeType={}, businessType={}",
                    userId, changeAmount, changeType, businessType);

            return insertCount > 0;

        } catch (Exception e) {
            log.error("记录钱包余额变动日志异常: userId={}, changeAmount={}", userId, changeAmount, e);
            return false;
        }
    }
}
