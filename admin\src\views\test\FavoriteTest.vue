<template>
  <div class="favorite-test">
    <h2>收藏功能测试</h2>
    
    <el-card class="test-card">
      <h3>测试收藏记录查询</h3>
      <el-form inline>
        <el-form-item label="关联ID:">
          <el-input v-model="testParams.relevancyId" placeholder="请输入关联ID" />
        </el-form-item>
        <el-form-item label="类型:">
          <el-select v-model="testParams.type" placeholder="请选择类型">
            <el-option label="帖子" value="0" />
            <el-option label="机构" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testFavoriteRecords">测试查询</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="testResult">
        <h4>查询结果:</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <h3>测试数据记录对话框</h3>
      <el-button type="primary" @click="showTestDialog">打开收藏记录对话框</el-button>
    </el-card>

    <!-- 数据记录查看对话框 -->
    <DataRecordDialog
      v-if="dialogVisible && apiConfig"
      v-model="dialogVisible"
      record-type="favorite"
      :relevancy-id="testParams.relevancyId"
      :relevancy-type="testParams.type"
      :api-config="apiConfig" />
  </div>
</template>

<script>
import { getFavoriteRecords, dataRecordApiConfig } from '@/api/ad/datarecord'
import DataRecordDialog from '@/components/DataRecordDialog.vue'

export default {
  name: 'FavoriteTest',
  components: {
    DataRecordDialog
  },
  data() {
    return {
      testParams: {
        relevancyId: '',
        type: '0'
      },
      testResult: null,
      dialogVisible: false,
      apiConfig: dataRecordApiConfig.favorite
    }
  },
  methods: {
    async testFavoriteRecords() {
      if (!this.testParams.relevancyId) {
        this.$message.warning('请输入关联ID')
        return
      }
      
      try {
        const params = {
          current: 1,
          size: 10,
          relevancyId: this.testParams.relevancyId,
          type: this.testParams.type
        }
        
        console.log('测试参数:', params)
        const response = await getFavoriteRecords(params)
        console.log('API响应:', response)
        
        this.testResult = response
        this.$message.success('查询成功')
      } catch (error) {
        console.error('查询失败:', error)
        this.$message.error('查询失败: ' + error.message)
        this.testResult = { error: error.message }
      }
    },
    
    showTestDialog() {
      if (!this.testParams.relevancyId) {
        this.$message.warning('请先输入关联ID')
        return
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped>
.favorite-test {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
