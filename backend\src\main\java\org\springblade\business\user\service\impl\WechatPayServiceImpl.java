/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayOrderQueryRequest;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.entity.WechatPayConfig;
import org.springblade.business.user.service.IWechatPayConfigService;
import org.springblade.business.user.service.IWechatPayService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 微信支付服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class WechatPayServiceImpl implements IWechatPayService {

    private final IWechatPayConfigService wechatPayConfigService;

    @Override
    public Map<String, String> createMiniAppPayOrder(String orderNo, BigDecimal totalAmount,
                                                    String description, String openId, String clientIp) throws WxPayException {

        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            throw new WxPayException("未找到可用的微信支付配置");
        }

        // 构建统一下单请求
        WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
        request.setBody(description);
        request.setOutTradeNo(orderNo);
        request.setTotalFee(totalAmount.multiply(new BigDecimal("100")).intValue()); // 转换为分
        request.setSpbillCreateIp(clientIp);
        request.setOpenid(openId);
        request.setTradeType("JSAPI"); // 小程序支付

        // 调用统一下单
        WxPayUnifiedOrderResult result = unifiedOrder(request);

        // 构建小程序支付参数
        Map<String, String> payParams = new HashMap<>();
        payParams.put("appId", result.getAppid());
        payParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
        payParams.put("nonceStr", result.getNonceStr());
        payParams.put("package", "prepay_id=" + result.getPrepayId());
        payParams.put("signType", "MD5");

        // 生成小程序支付签名
        // 使用手动签名方法，因为WxJava的createJsapiSignature方法参数不匹配
        String paySign = generateManualPaySign(payParams);
        payParams.put("paySign", paySign);

        // 添加微信支付相关信息，用于更新订单
        payParams.put("prepayId", result.getPrepayId());
        payParams.put("outTradeNo", orderNo);
        payParams.put("mchId", result.getMchId());

        log.info("创建小程序支付订单成功: orderNo={}, prepayId={}", orderNo, result.getPrepayId());
        return payParams;
    }

    @Override
    public WxPayUnifiedOrderResult unifiedOrder(WxPayUnifiedOrderRequest request) throws WxPayException {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            throw new WxPayException("未找到可用的微信支付配置");
        }

        try {
            WxPayUnifiedOrderResult result = wxPayService.unifiedOrder(request);
            log.info("微信统一下单成功: outTradeNo={}, prepayId={}", request.getOutTradeNo(), result.getPrepayId());
            return result;
        } catch (WxPayException e) {
            log.error("微信统一下单失败: outTradeNo={}, error={}", request.getOutTradeNo(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Map<String, String> queryOrder(String transactionId, String outTradeNo) throws WxPayException {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            throw new WxPayException("未找到可用的微信支付配置");
        }

        try {
            // 优先使用微信交易号查询
            if (transactionId != null && !transactionId.trim().isEmpty()) {
                WxPayOrderQueryResult result = wxPayService.queryOrder(transactionId, null);
                return result.toMap();
            } else if (outTradeNo != null && !outTradeNo.trim().isEmpty()) {
                WxPayOrderQueryResult result = wxPayService.queryOrder(null, outTradeNo);
                return result.toMap();
            } else {
                throw new WxPayException("交易号和商户订单号不能同时为空");
            }
        } catch (WxPayException e) {
            log.error("查询微信支付订单失败: transactionId={}, outTradeNo={}", transactionId, outTradeNo, e);
            throw e;
        }
    }

    @Override
    public boolean closeOrder(String outTradeNo) throws WxPayException {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            throw new WxPayException("未找到可用的微信支付配置");
        }

        try {
            wxPayService.closeOrder(outTradeNo);
            log.info("关闭微信支付订单成功: outTradeNo={}", outTradeNo);
            return true;
        } catch (WxPayException e) {
            log.error("关闭微信支付订单失败: outTradeNo={}", outTradeNo, e);
            throw e;
        }
    }

    @Override
    public Map<String, String> refund(String outTradeNo, String outRefundNo,
                                      BigDecimal totalAmount, BigDecimal refundAmount,
                                      String reason) throws WxPayException {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            throw new WxPayException("未找到可用的微信支付配置");
        }

        try {
            // 构建退款请求
            WxPayRefundRequest refundRequest = new WxPayRefundRequest();
            refundRequest.setOutTradeNo(outTradeNo);
            refundRequest.setOutRefundNo(outRefundNo);
            refundRequest.setTotalFee(totalAmount.multiply(new BigDecimal("100")).intValue()); // 转换为分
            refundRequest.setRefundFee(refundAmount.multiply(new BigDecimal("100")).intValue()); // 转换为分
            refundRequest.setRefundDesc(reason);

            WxPayRefundResult result = wxPayService.refund(refundRequest);

            log.info("微信支付退款成功: outTradeNo={}, outRefundNo={}, refundAmount={}",
                    outTradeNo, outRefundNo, refundAmount);

            return result.toMap();
        } catch (WxPayException e) {
            log.error("微信支付退款失败: outTradeNo={}, outRefundNo={}", outTradeNo, outRefundNo, e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> handlePayNotify(String xmlData) throws WxPayException {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            throw new WxPayException("未找到可用的微信支付配置");
        }

        try {
            // 解析回调数据
            WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);

            log.info("处理微信支付回调成功: outTradeNo={}, transactionId={}, totalFee={}",
                    notifyResult.getOutTradeNo(), notifyResult.getTransactionId(), notifyResult.getTotalFee());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("outTradeNo", notifyResult.getOutTradeNo());
            result.put("transactionId", notifyResult.getTransactionId());
            result.put("totalFee", notifyResult.getTotalFee());
            result.put("timeEnd", notifyResult.getTimeEnd());
            result.put("returnCode", notifyResult.getReturnCode());
            result.put("resultCode", notifyResult.getResultCode());
            result.put("openid", notifyResult.getOpenid());

            return result;
        } catch (WxPayException e) {
            log.error("处理微信支付回调失败", e);
            throw e;
        }
    }

    @Override
    public boolean verifyNotifySign(String xmlData) {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            log.warn("未找到可用的微信支付配置，无法验证签名");
            return false;
        }

        try {
            // 验证签名
            WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);
            log.info("微信支付回调签名验证成功: outTradeNo={}", notifyResult.getOutTradeNo());
            return true;
        } catch (WxPayException e) {
            log.error("微信支付回调签名验证失败", e);
            return false;
        }
    }

    /**
     * 生成成功的回调响应
     *
     * @return 成功响应XML
     */
    public String generateSuccessResponse() {
        return WxPayNotifyResponse.success("OK");
    }

    /**
     * 生成失败的回调响应
     *
     * @param errorMsg 错误信息
     * @return 失败响应XML
     */
    public String generateFailResponse(String errorMsg) {
        return WxPayNotifyResponse.fail(errorMsg);
    }



    @Override
    public Map<String, Object> queryOrderStatus(String outTradeNo) {
        WxPayService wxPayService = wechatPayConfigService.getDefaultWxPayService();
        if (wxPayService == null) {
            log.warn("未找到可用的微信支付配置，无法查询订单状态");
            return null;
        }

        try {
            // 构建查询请求
            WxPayOrderQueryRequest queryRequest = new WxPayOrderQueryRequest();
            queryRequest.setOutTradeNo(outTradeNo);

            // 查询订单
            WxPayOrderQueryResult queryResult = wxPayService.queryOrder(queryRequest);

            Map<String, Object> result = new HashMap<>();
            result.put("return_code", queryResult.getReturnCode());
            result.put("return_msg", queryResult.getReturnMsg());
            result.put("result_code", queryResult.getResultCode());
            result.put("err_code", queryResult.getErrCode());
            result.put("err_code_des", queryResult.getErrCodeDes());
            result.put("out_trade_no", queryResult.getOutTradeNo());
            result.put("transaction_id", queryResult.getTransactionId());
            result.put("trade_state", queryResult.getTradeState());
            result.put("trade_state_desc", queryResult.getTradeStateDesc());
            result.put("time_end", queryResult.getTimeEnd());
            result.put("total_fee", queryResult.getTotalFee());
            result.put("cash_fee", queryResult.getCashFee());

            log.debug("查询微信订单状态成功: outTradeNo={}, tradeState={}",
                outTradeNo, queryResult.getTradeState());

            return result;

        } catch (WxPayException e) {
            log.error("查询微信订单状态失败: outTradeNo={}", outTradeNo, e);
            return null;
        }
    }

    /**
     * 手动生成小程序支付签名
     * 当WxPayService的createJsapiSignature方法不可用时使用
     *
     * @param payParams 支付参数
     * @return 签名字符串
     */
    private String generateManualPaySign(Map<String, String> payParams) {
        try {
            // 获取支付配置
            WechatPayConfig config = wechatPayConfigService.getDefaultConfig();
            if (config == null) {
                throw new RuntimeException("未找到默认支付配置");
            }

            // 构建签名参数
            TreeMap<String, String> signParams = new TreeMap<>();
            signParams.put("appId", payParams.get("appId"));
            signParams.put("timeStamp", payParams.get("timeStamp"));
            signParams.put("nonceStr", payParams.get("nonceStr"));
            signParams.put("package", payParams.get("package"));
            signParams.put("signType", payParams.get("signType"));

            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    if (signStr.length() > 0) {
                        signStr.append("&");
                    }
                    signStr.append(entry.getKey()).append("=").append(entry.getValue());
                }
            }

            // 添加API密钥
            signStr.append("&key=").append(config.getApiKey());

            log.debug("签名字符串: {}", signStr.toString());

            // 生成MD5签名
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.toString().getBytes("UTF-8"));

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            String signature = hexString.toString().toUpperCase();
            log.debug("生成的签名: {}", signature);

            return signature;

        } catch (Exception e) {
            log.error("手动生成支付签名失败", e);
            throw new RuntimeException("生成支付签名失败: " + e.getMessage());
        }
    }
}
