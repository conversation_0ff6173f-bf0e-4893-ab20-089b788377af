/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.report.entity.ReportTag;
import org.springblade.business.report.vo.ReportTagVO;
import org.springblade.business.report.wrapper.ReportTagWrapper;
import org.springblade.business.report.service.IReportTagService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 举报标签 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/reporttag")
@io.swagger.v3.oas.annotations.tags.Tag(name = "举报标签", description = "举报标签接口")
public class ReportTagController extends BladeController {

	private IReportTagService reportTagService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")

	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入reportTag")
	public R<ReportTagVO> detail(ReportTag reportTag) {
		ReportTag detail = reportTagService.getOne(Condition.getQueryWrapper(reportTag));
		return R.data(ReportTagWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 举报标签
	 */
	@GetMapping("/list")

	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入reportTag")
	public R<IPage<ReportTagVO>> list(ReportTag reportTag, Query query) {
		IPage<ReportTag> pages = reportTagService.page(Condition.getPage(query), Condition.getQueryWrapper(reportTag));
		return R.data(ReportTagWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 举报标签
	 */
	@GetMapping("/page")

	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入reportTag")
	public R<IPage<ReportTagVO>> page(ReportTagVO reportTag, Query query) {
		IPage<ReportTagVO> pages = reportTagService.selectReportTagPage(Condition.getPage(query), reportTag);
		return R.data(pages);
	}

	/**
	 * 新增 举报标签
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入reportTag")
	public R save(@Valid @RequestBody ReportTag reportTag) {
		return R.status(reportTagService.save(reportTag));
	}

	/**
	 * 修改 举报标签
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入reportTag")
	public R update(@Valid @RequestBody ReportTag reportTag) {
		return R.status(reportTagService.updateById(reportTag));
	}

	/**
	 * 新增或修改 举报标签
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入reportTag")
	public R submit(@Valid @RequestBody ReportTag reportTag) {
		return R.status(reportTagService.saveOrUpdate(reportTag));
	}


	/**
	 * 删除 举报标签
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(reportTagService.deleteLogic(Func.toLongList(ids)));
	}


}
