/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.service.impl;

import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springblade.business.points.entity.PointsRecord;
import org.springblade.business.points.vo.PointsRecordVO;
import org.springblade.business.points.mapper.PointsRecordMapper;
import org.springblade.business.points.service.IPointsRecordService;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 积分记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@AllArgsConstructor
@Service
public class PointsRecordServiceImpl extends BaseServiceImpl<PointsRecordMapper, PointsRecord> implements IPointsRecordService {
	private final IWeUserService weUserService;
	@Override
	public IPage<PointsRecordVO> selectPointsRecordPage(IPage<PointsRecordVO> page, PointsRecordVO pointsRecord) {
		return page.setRecords(baseMapper.selectPointsRecordPage(page, pointsRecord));
	}


}
