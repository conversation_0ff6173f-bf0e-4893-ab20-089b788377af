/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import org.springblade.business.user.service.IUserPointsService;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.business.user.entity.WeUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户积分服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
public class UserPointsServiceImpl implements IUserPointsService {

    @Autowired
    private IWeUserService weUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoints(Long userId, Integer points, String reason) {
        try {
            if (userId == null || points == null || points <= 0) {
                return false;
            }

            WeUser user = weUserService.getById(userId);
            if (user == null) {
                return false;
            }

            // 获取当前积分，如果为null则设为0
            Integer currentPoints = user.getBalance() != null ? user.getBalance() : 0;

            // 增加积分
            user.setBalance(currentPoints + points);
            
            // 更新用户信息
            boolean result = weUserService.updateById(user);
            
            if (result) {
                // 这里可以记录积分变动日志
                System.out.println("用户积分增加成功: userId=" + userId + ", points=" + points + ", reason=" + reason);
            }
            
            return result;
        } catch (Exception e) {
            System.err.println("增加用户积分失败: userId=" + userId + ", points=" + points + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductPoints(Long userId, Integer points, String reason) {
        try {
            if (userId == null || points == null || points <= 0) {
                return false;
            }

            WeUser user = weUserService.getById(userId);
            if (user == null) {
                return false;
            }

            // 获取当前积分，如果为null则设为0
            Integer currentPoints = user.getBalance() != null ? user.getBalance() : 0;

            // 检查积分是否足够
            if (currentPoints < points) {
                System.err.println("用户积分不足: userId=" + userId + ", currentPoints=" + currentPoints + ", deductPoints=" + points);
                return false;
            }

            // 扣除积分
            user.setBalance(currentPoints - points);
            
            // 更新用户信息
            boolean result = weUserService.updateById(user);
            
            if (result) {
                // 这里可以记录积分变动日志
                System.out.println("用户积分扣除成功: userId=" + userId + ", points=" + points + ", reason=" + reason);
            }
            
            return result;
        } catch (Exception e) {
            System.err.println("扣除用户积分失败: userId=" + userId + ", points=" + points + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    public Integer getUserPoints(Long userId) {
        try {
            if (userId == null) {
                return 0;
            }

            WeUser user = weUserService.getById(userId);
            if (user == null) {
                return 0;
            }

            return user.getBalance() != null ? user.getBalance() : 0;
        } catch (Exception e) {
            System.err.println("获取用户积分失败: userId=" + userId + ", error=" + e.getMessage());
            return 0;
        }
    }
}
