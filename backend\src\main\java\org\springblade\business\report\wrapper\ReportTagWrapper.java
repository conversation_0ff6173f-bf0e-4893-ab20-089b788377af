/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.report.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.business.report.entity.ReportTag;
import org.springblade.business.report.vo.ReportTagVO;

/**
 * 举报标签包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public class ReportTagWrapper extends BaseEntityWrapper<ReportTag, ReportTagVO>  {

    public static ReportTagWrapper build() {
        return new ReportTagWrapper();
    }

	@Override
	public ReportTagVO entityVO(ReportTag reportTag) {
		ReportTagVO reportTagVO = BeanUtil.copyProperties(reportTag, ReportTagVO.class);

		return reportTagVO;
	}

}
