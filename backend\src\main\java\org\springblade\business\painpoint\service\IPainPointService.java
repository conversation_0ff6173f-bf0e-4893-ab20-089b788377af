/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.painpoint.service;

import org.springblade.business.painpoint.entity.PainPoint;
import org.springblade.business.painpoint.vo.PainPointVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface IPainPointService extends BaseService<PainPoint> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param painPoint
	 * @return
	 */
	IPage<PainPointVO> selectPainPointPage(IPage<PainPointVO> page, PainPointVO painPoint);

	boolean handle(PainPoint painPoint);
}
