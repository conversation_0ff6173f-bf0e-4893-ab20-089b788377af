package org.springblade.business.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 帖子分类信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Schema(description = "帖子分类信息")
public class PostCategoryDTO implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 分类ID
	 */
	@Schema(description = "分类ID")
	private Long categoryId;

	/**
	 * 分类名称
	 */
	@Schema(description = "分类名称")
	private String categoryName;

	/**
	 * 分类描述
	 */
	@Schema(description = "分类描述")
	private String categoryDescription;
}
