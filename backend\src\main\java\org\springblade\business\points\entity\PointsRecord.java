/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 积分记录表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_points_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "积分记录表")
public class PointsRecord extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 用户OpenID
     */
    @Schema(description = "用户OpenID")
    private String userId;
    /**
     * 积分变动数量（1数为获得，0数为消费）
     */
    @Schema(description = "积分变动数量（1数为获得，0数为消费）")
    private Integer points;
    /**
     * 变动前积分
     */
    @Schema(description = "变动前积分")
    private Integer beforePoints;
    /**
     * 变动后积分
     */
    @Schema(description = "变动后积分")
    private Integer afterPoints;
    /**
     * 积分类型（0：签到，1：兑换，2：分享，3：邀请，4：管理员操作）
     */
    @Schema(description = "积分类型（0：签到，1：兑换，2：分享，3：邀请，4：管理员操作）")
    private String type;
    /**
     * 积分类型名称
     */
    @Schema(description = "积分类型名称")
    private String typeName;
    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID")
    private Long businessId;
    /**
     * 关联业务类型
     */
    @Schema(description = "关联业务类型")
    private String businessType;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private LocalDateTime operateTime;
    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private Long operator;


}
