/**
 * Copyright (c) 2018-2099, Chi<PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import org.springblade.business.points.entity.PointsGoods;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 用户优惠券表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_coupon")
@Schema(description = "用户优惠券表")
public class Coupon extends BaseEntity  {

    @Serial
	private static final long serialVersionUID = 1L;


    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 兑换记录ID
     */
    @Schema(description = "兑换记录ID")
    private Long exchangeId;
    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long goodsId;
    /**
     * 优惠券码
     */
    @Schema(description = "优惠券码")
    private String couponCode;
    /**
     * 有效期起
     */
    @Schema(description = "有效期起")
    private LocalDateTime validFrom;
    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    private LocalDateTime validTo;

	/**
	 * 积分商品相关字段
	 */
	@Schema(description = "积分商品相关字段")
	@TableField(exist = false)
	private PointsGoods pointsGoods;




}
