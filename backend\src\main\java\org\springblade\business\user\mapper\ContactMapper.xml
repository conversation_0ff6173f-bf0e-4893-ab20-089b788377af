<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.ContactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contactResultMap" type="org.springblade.business.user.entity.Contact">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="wechat" property="wechat"/>
        <result column="contact_type" property="contactType"/>
    </resultMap>


    <select id="selectContactPage" resultMap="contactResultMap">
        select * from urb_contact where is_deleted = 0
    </select>

    <!-- 获取用户联系方式 -->
    <select id="getUserContact" resultMap="contactResultMap">
        SELECT
            c.id,
            c.tenant_id,
            c.name,
            c.phone,
            c.wechat,
            c.contact_type,
            c.update_time,
            c.update_user
        FROM urb_contact c
        INNER JOIN urb_user_contact uc ON c.id = uc.contact_id
        WHERE uc.user_id = #{userId}
        AND c.is_deleted = 0
    </select>

    <!-- 更新或插入用户联系人关联 -->
    <insert id="insertUserContact">
        INSERT INTO urb_user_contact (
            user_id,
            contact_id
        ) VALUES (
            #{userId},
            #{contactId}
        ) ON DUPLICATE KEY UPDATE
            contact_id = VALUES(contact_id)
    </insert>

</mapper>
