# 分类ID查询功能集成说明

## 功能概述
在帖子管理页面中集成了分类ID查询功能，支持按分类筛选帖子数据。

## 实现的修改

### 1. API导入
在 `post.vue` 中导入了分类字典API：
```javascript
import {dicList as getCategoryDict} from "@/api/ad/category";
```

### 2. 分类字段配置
修改了分类字段的配置：
```javascript
{
  label: "分类",
  prop: "categoryId",
  type: "select",
  dicData: [],
  props: {
    label: "name",
    value: "id"
  },
  search: true,
  hide: false
}
```

### 3. 动态加载分类数据
在 `onLoad` 方法中添加了分类数据的动态加载：
```javascript
// 动态加载分类字典
getCategoryDict().then(res => {
  const categories = res.data.data || [];
  const categoryCol = this.option.column.find(c => c.prop === 'categoryId');
  if (categoryCol) categoryCol.dicData = categories;
}).catch(error => {
  console.error('加载分类字典失败:', error);
});
```

## 使用的API接口

### 分类字典接口
- **接口地址**: `/api/blade-ad/category/list`
- **请求方法**: GET
- **功能**: 获取所有分类的列表数据
- **返回格式**:
```json
{
  "code": 200,
  "success": true,
  "data": [
    {
      "id": "分类ID",
      "name": "分类名称",
      "description": "分类描述",
      "enabled": true
    }
  ]
}
```

## 功能特性

✅ **下拉选择** - 分类字段显示为下拉选择框
✅ **搜索支持** - 支持按分类进行搜索筛选
✅ **动态加载** - 分类数据从后端动态加载
✅ **错误处理** - 包含加载失败的错误处理
✅ **用户友好** - 显示分类名称而不是ID

## 测试步骤

1. **访问帖子管理页面**
   - 进入帖子管理页面
   - 检查表格中是否显示"分类"列

2. **测试搜索功能**
   - 点击搜索区域的分类下拉框
   - 应该显示所有可用的分类选项
   - 选择一个分类进行搜索
   - 验证搜索结果是否正确

3. **测试数据显示**
   - 检查表格中的分类列是否正确显示分类名称
   - 验证分类数据是否与后端数据一致

## 后端要求

确保后端的 `/api/blade-ad/category/list` 接口能够正常返回分类数据，数据格式应包含：
- `id`: 分类ID
- `name`: 分类名称
- 其他相关字段

## 注意事项

1. 分类数据在每次加载页面时都会重新获取
2. 如果分类数据加载失败，会在控制台输出错误信息
3. 分类字段支持搜索功能，可以按分类筛选帖子
4. 表格中显示的是分类名称，而不是分类ID
