/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.subscriptionPlan.entity.SubscriptionPlan;
import org.springblade.business.subscriptionPlan.vo.SubscriptionPlanVO;
import org.springblade.business.subscriptionPlan.wrapper.SubscriptionPlanWrapper;
import org.springblade.business.subscriptionPlan.service.ISubscriptionPlanService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 订阅计划表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/subscriptionplan")
@io.swagger.v3.oas.annotations.tags.Tag(name = "订阅计划表", description = "订阅计划表接口")
public class SubscriptionPlanController extends BladeController {

	private ISubscriptionPlanService subscriptionPlanService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入subscriptionPlan")
	public R<SubscriptionPlanVO> detail(SubscriptionPlan subscriptionPlan) {
		SubscriptionPlan detail = subscriptionPlanService.getOne(Condition.getQueryWrapper(subscriptionPlan));
		return R.data(SubscriptionPlanWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 订阅计划表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入subscriptionPlan")
	public R<IPage<SubscriptionPlanVO>> list(SubscriptionPlan subscriptionPlan, Query query) {
		IPage<SubscriptionPlan> pages = subscriptionPlanService.page(Condition.getPage(query), Condition.getQueryWrapper(subscriptionPlan));
		return R.data(SubscriptionPlanWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 订阅计划表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入subscriptionPlan")
	public R<IPage<SubscriptionPlanVO>> page(SubscriptionPlanVO subscriptionPlan, Query query) {
		IPage<SubscriptionPlanVO> pages = subscriptionPlanService.selectSubscriptionPlanPage(Condition.getPage(query), subscriptionPlan);
		return R.data(pages);
	}

	/**
	 * 新增 订阅计划表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入subscriptionPlan")
	public R save(@Valid @RequestBody SubscriptionPlan subscriptionPlan) {
		return R.status(subscriptionPlanService.save(subscriptionPlan));
	}

	/**
	 * 修改 订阅计划表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入subscriptionPlan")
	public R update(@Valid @RequestBody SubscriptionPlan subscriptionPlan) {
		return R.status(subscriptionPlanService.updateById(subscriptionPlan));
	}

	/**
	 * 新增或修改 订阅计划表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入subscriptionPlan")
	public R submit(@Valid @RequestBody SubscriptionPlan subscriptionPlan) {
		return R.status(subscriptionPlanService.saveOrUpdate(subscriptionPlan));
	}

	
	/**
	 * 删除 订阅计划表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(subscriptionPlanService.deleteLogic(Func.toLongList(ids)));
	}

	
}
