import request from '@/axios';

export const page = (current, size, params) => {
  return request({
    url: '/blade-ad/user/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const list = (current, size, params) => {
  return request({
    url: '/blade-ad/user/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/user/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/user/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/user/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/user/submit',
    method: 'post',
    data: row
  })
}

// 获取用户字典列表（用于下拉选择）
export const getUserDict = (params = {}) => {
  return request({
    url: '/blade-ad/user/dict',
    method: 'get'
  })
}

// 获取用户详细信息（用于用户详情弹窗）
export const getUserDetail = (userId) => {
  return request({
    url: '/blade-ad/user/detail',
    method: 'get',
    params: {
      id: userId
    }
  })
}

