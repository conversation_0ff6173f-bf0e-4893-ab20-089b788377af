<template>
  <div class="login-container"
       @keyup.enter="handleLogin">
    <div class="login-weaper">
      <div class="login-left animate__animated animate__fadeInLeft">
        <div class="brand-info">
          <h1 class="brand-title">百事通便民信息</h1>
          <p class="brand-subtitle">事事有看落</p>
        </div>
      </div>
      <div class="login-border animate__animated animate__fadeInRight">
        <div class="login-main">
          <h2 class="login-title">企业管理后台</h2>
          <p class="login-welcome">欢迎登录百事通便民信息管理系统</p>
          <userLogin v-if="activeName==='user'"></userLogin>
          <codeLogin v-else-if="activeName==='code'"></codeLogin>
          <faceLogin v-else-if="activeName==='face'"></faceLogin>
          <div class="login-menu">
            <a href="#"
               @click.stop="activeName='user'">{{ $t('login.userLogin') }}</a>
            <!-- <a href="#"
               @click.stop="activeName='code'">{{ $t('login.phoneLogin') }}</a> -->
            <a href="#"
               @click.stop="activeName='face'">{{ $t('login.faceLogin') }}</a>
          </div>
          <div class="forgot-password">
            <a href="#">忘记密码?</a>
          </div>
          <thirdLogin></thirdLogin>
          <div class="copyright">
            © 2024 百事通便民信息 版权所有
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import userLogin from "./userlogin.vue";
import codeLogin from "./codelogin.vue";
import thirdLogin from "./thirdlogin.vue";
import faceLogin from "./facelogin.vue";
import { validatenull } from "@/utils/validate";
export default {
  name: "login",
  components: {
    userLogin,
    codeLogin,
    thirdLogin,
    faceLogin
  },
  data () {
    return {
      activeName: "user"
    };
  },
  watch: {
    $route () {
      const params = this.$route.query;
      this.socialForm = params
      if (!validatenull(this.socialForm.state)) {
        const loading = this.$loading({
          lock: true,
          text: `${this.socialForm.state === "WX" ? "微信" : "QQ"
            }登录中,请稍后。。。`,
          spinner: "el-icon-loading"
        });
        setTimeout(() => {
          loading.close();
        }, 2000);
      }
    }
  },
  created () {
  },
  mounted () { },
  props: [],
  methods: {
  }
};
</script>
