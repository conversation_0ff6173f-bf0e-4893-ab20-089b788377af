/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.business.user.mapper.BusinessCardMapper;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.common.constant.bizz.AuditStatusEnum;
import org.springblade.common.constant.bizz.PublishStatusEnum;
import org.springblade.common.constant.bizz.StatsTypeEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.miniapp.utils.IpUtils;
import org.springblade.modules.system.service.IParamService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 名片信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class BusinessCardServiceImpl extends BaseServiceImpl<BusinessCardMapper, BusinessCard> implements IBusinessCardService {
	@Resource
	private IParamService paramService;
	@Resource
	private SupPostMapper supPostMapper;
	@Override
	public IPage<BusinessCardVO> selectBusinessCardPage(IPage<BusinessCardVO> page, BusinessCardVO businessCard) {
		return page.setRecords(baseMapper.selectBusinessCardPage(page, businessCard));
	}




	@Resource
	private HttpServletRequest request;
	@Override
	@Transactional
	public BusinessCardVO getDetail(BusinessCardVO businessCard) {
		List< BusinessCardVO>list =baseMapper.selectBusinessCardPage(Page.of(1, 1), businessCard);
		if(!list.isEmpty()){
			String ipAddr = IpUtils.getIpAddr(request);
			supPostMapper.insertViewHistory(Map.of("relevancyId",list.get(0).getId(),"userId", AuthUtil.getUserId(),"ip",ipAddr,"type", StatsTypeEnum.CARD_STATS.getValue()));
		}
		return list.get(0);
	}

	@Override
	public List<BusinessCard> listByUserId(Long userId) {
		return this.list(Wrappers.<BusinessCard>lambdaQuery().eq(BusinessCard::getOwnerId, userId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean auditBusinessCard(Long cardId, String auditStatus, String auditRemark) {
		BusinessCard card = Optional.ofNullable(getById(cardId))
			.orElseThrow(() -> new ServiceException("找不到记录"));

		BusinessCard updateCard = new BusinessCard();
		updateCard.setId(cardId);
		updateCard.setAuditStatus(auditStatus);
		return updateById(updateCard);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchAuditBusinessCards(List<Long> cardIds, String auditStatus, String auditRemark) {
		if (cardIds == null || cardIds.isEmpty()) {
			return false;
		}

		// 验证审核状态值
		if (!Objects.equals(auditStatus, AuditStatusEnum.APPROVED.getCode()) && !Objects.equals(auditStatus, AuditStatusEnum.REJECTED.getCode())) {
			throw new ServiceException("审核状态值无效，只能是1（通过）或2（拒绝）");
		}

		// 批量更新
		LambdaUpdateWrapper<BusinessCard> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.in(BusinessCard::getId, cardIds)
			.eq(BusinessCard::getAuditStatus, AuditStatusEnum.PENDING.getCode()) // 只更新待审核状态的名片
			.set(BusinessCard::getAuditStatus, auditStatus)
			.set(BusinessCard::getUpdateTime, LocalDateTime.now())
			.set(BusinessCard::getUpdateUser, AuthUtil.getUserId())
			.set(BusinessCard::getPublishTime, auditStatus.equals(AuditStatusEnum.APPROVED.getCode()) ? LocalDateTime.now() : null);

		return this.update(updateWrapper);
	}

	@Override
	public Boolean updatePublishStatus(Long id, String publishStatus) {
		if (id == null || publishStatus == null) {
			return false;
		}
		BusinessCard card = new BusinessCard();
		card.setId(id);
		card.setPublishStatus(publishStatus);
		return this.updateById(card);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdatePublishStatus(List<Long> cardIds, String publishStatus) {
		if (cardIds == null || cardIds.isEmpty() || publishStatus == null) {
			return false;
		}

		// 验证发布状态值
		if (!Objects.equals(publishStatus, PublishStatusEnum.DRAFT.getValue()) && !Objects.equals(publishStatus, PublishStatusEnum.PUBLISHED.getValue())) {
			throw new ServiceException("发布状态值无效，只能是0（下架）或1（上架）");
		}

		// 批量更新
		LambdaUpdateWrapper<BusinessCard> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.in(BusinessCard::getId, cardIds)
			.set(BusinessCard::getPublishStatus, publishStatus)
			.set(BusinessCard::getUpdateTime, LocalDateTime.now())
			.set(BusinessCard::getUpdateUser, AuthUtil.getUserId());

		return this.update(updateWrapper);
	}

}
