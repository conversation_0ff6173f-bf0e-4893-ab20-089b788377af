<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.points.mapper.CouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="couponResultMap" type="org.springblade.business.points.entity.Coupon">
        <result column="status" property="status"/>
        <result column="user_id" property="userId"/>
        <result column="exchange_id" property="exchangeId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="coupon_code" property="couponCode"/>
        <result column="valid_from" property="validFrom"/>
        <result column="valid_to" property="validTo"/>
    </resultMap>


    <select id="selectCouponPage" resultMap="couponResultMap">
        select * from urb_coupon where is_deleted = 0
    </select>

</mapper>
