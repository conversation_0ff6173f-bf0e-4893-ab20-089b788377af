package org.springblade.business.institution.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 帖子统计数据信息
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/24
 */@Data
@Schema(description = "机构统计信息")
public class InstitutionStatsDTO {

	private Boolean isLiked;  // 是否点赞
	private Boolean isFavorite;  // 是否收藏
	private Boolean isView;   // 是否浏览
	/**
	 * 点赞数
	 */
	@Schema(description = "点赞数")
	private Integer likeCount;

	/**
	 * 收藏数
	 */
	@Schema(description = "收藏数")
	private Integer favoriteCount;

	private Integer viewCount;

	/**
	 * 距离
	 */
	@Schema(description = "距离 单位为km")
	private BigDecimal distance;

}
