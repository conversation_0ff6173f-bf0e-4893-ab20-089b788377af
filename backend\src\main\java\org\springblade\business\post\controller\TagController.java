/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.AdminTagCreateRequest;
import org.springblade.business.post.service.ICategoryTagService;
import org.springblade.business.post.service.ITagService;
import org.springblade.business.post.vo.TagVO;
import org.springblade.business.post.wrapper.TagWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 信息标签 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/tag")
@io.swagger.v3.oas.annotations.tags.Tag(name = "信息标签", description = "信息标签接口")
@Validated
public class TagController extends BladeController {

	private ITagService tagService;
	private ICategoryTagService categoryTagService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入tag")
	public R<TagVO> detail(Tag tag) {
		Tag detail = tagService.getOne(Condition.getQueryWrapper(tag));
		return R.data(TagWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 信息标签
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入tag")
	public R<IPage<TagVO>> list(Tag tag, Query query) {
		IPage<Tag> pages = tagService.page(Condition.getPage(query), Condition.getQueryWrapper(tag));
		return R.data(TagWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 信息标签
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入tag")
	public R<IPage<TagVO>> page(TagVO tag, Query query) {
		IPage<TagVO> pages = tagService.selectTagPage(Condition.getPage(query), tag);
		return R.data(pages);
	}

	@Data
	public static class TagCreateRequest {
		@NotNull
		private Long categoryId;
		@NotNull
		private Long tagId;
		@NotNull
		private Integer type;

	}

	/**
	 * 为分类添加标签
	 */
	@PostMapping
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "为分类添加标签", description = "为指定分类添加标签")
	public R<Boolean> addTagToCategory(@RequestBody @Validated TagCreateRequest tag) {
		Boolean result = categoryTagService.addTagToCategory(tag);
		return R.data(result);
	}

	/**
	 * 新增 信息标签
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入tag")
	public R save(@Valid @RequestBody Tag tag) {
		boolean result  = tagService.saveTag(tag);
		return R.status(result);
	}

	/**
	 * 修改 信息标签
	 */
	@PutMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入tag")
	public R update(@Valid @RequestBody Tag tag) {
		boolean result = tagService.updateTagById(tag);
		return R.status(result);
	}

	/**
	 * 新增或修改 信息标签
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入tag")
	public R submit(@Valid @RequestBody Tag tag) {
		boolean result = tagService.saveOrUpdateTag(tag);
		return R.status(result);
	}

	/**
	 * 删除 信息标签
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = tagService.removeClearBind(Func.toLongList(ids));
		return R.status(result);
	}

	// ==================== 后台管理相关接口 ====================

	/**
	 * 获取标签列表（后台管理）
	 */
	@GetMapping("/admin/list")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取标签列表", description = "后台管理获取标签列表")
	public R<IPage<TagVO>> getTagList(@Parameter(description = "标签名称") @RequestParam(required = false) String tagName,
									  @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
									  @Parameter(description = "是否启用") @RequestParam(required = false) Integer enabled,
									  @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
									  @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
		TagVO queryTag = new TagVO();
		queryTag.setTagName(tagName);
		queryTag.setCategoryId(categoryId);
		queryTag.setEnabled(enabled);
		IPage<TagVO> tags = tagService.selectTagPage(Page.of(current, size), queryTag);
		return R.data(tags);
	}

	/**
	 * 根据分类和标签类型获取标签
	 */
	@GetMapping("/admin/categorisation")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "根据分类和标签类型获取标签", description = "根据分类ID和标签类型获取该分类下的标签")
	public R<List<Tag>> getTagsByCategoryAndType(@Parameter(description = "分类ID") @RequestParam Long categoryId,
												 @Parameter(description = "标签类型") @RequestParam Integer type) {
		List<Tag> tags = categoryTagService.getTagsByCategoryAndType(categoryId, type);
		return R.data(tags);
	}

	/**
	 * 获取热门标签
	 */
	@GetMapping("/admin/hot")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取热门标签", description = "获取使用次数最多的标签")
	public R<List<Tag>> getHotTags(@Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
		List<Tag> tags = tagService.getHotTags(limit);
		return R.data(tags);
	}

	/**
	 * 启用/禁用标签
	 */
	@PostMapping("/admin/{id}/enable")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "启用/禁用标签", description = "启用或禁用标签")
	public R<Boolean> enableTag(@Parameter(description = "标签ID") @PathVariable Long id,
								@Parameter(description = "是否启用") @RequestParam Boolean enabled) {
		boolean result = tagService.enableTag(id, enabled);
		return R.data(result);
	}

	/**
	 * 创建标签并添加到分类
	 */
	@PostMapping("/admin/create")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "创建标签", description = "创建新标签")
	public R<Tag> createTag(@RequestBody AdminTagCreateRequest tagReq) {
		Tag tag = tagService.createTag(tagReq);
		return R.data(tag);
	}


	/**
	 * 从分类移除标签
	 */
	@DeleteMapping("/admin/remove")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "从分类移除标签", description = "从指定分类移除标签")
	public R<Boolean> removeTagFromCategory(@RequestBody TagCreateRequest tagRequest) {
		return R.data(categoryTagService.removeFeedbackTagFromCategory(tagRequest));
	}
}
