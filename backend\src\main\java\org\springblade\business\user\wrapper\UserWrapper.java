/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.wrapper;

import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.vo.WeUserVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 用户信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public class UserWrapper extends BaseEntityWrapper<WeUser, WeUserVO>  {

    public static UserWrapper build() {
        return new UserWrapper();
    }

	@Override
	public WeUserVO entityVO(WeUser user) {
		WeUserVO userVO = BeanUtil.copyProperties(user, WeUserVO.class);
		// 如果需要额外处理字段，可以在这里添加
		return userVO;
	}

}
