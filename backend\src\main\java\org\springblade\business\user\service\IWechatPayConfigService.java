/**
 * Copyright (c) 2018-2099, Chill <PERSON>ang 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

import com.github.binarywang.wxpay.service.WxPayService;
import org.springblade.business.user.entity.WechatPayConfig;

import java.util.List;

/**
 * 微信支付配置服务接口（支持多商户）
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IWechatPayConfigService {

    /**
     * 获取默认支付配置
     *
     * @return 默认支付配置
     */
    WechatPayConfig getDefaultConfig();

    /**
     * 根据配置名称获取支付配置
     *
     * @param configName 配置名称
     * @return 支付配置
     */
    WechatPayConfig getConfigByName(String configName);

    /**
     * 根据商户号获取支付配置
     *
     * @param mchId 商户号
     * @return 支付配置
     */
    WechatPayConfig getConfigByMchId(String mchId);

    /**
     * 获取所有启用的支付配置
     *
     * @return 启用的支付配置列表
     */
    List<WechatPayConfig> getEnabledConfigs();

    /**
     * 根据优先级获取最佳支付配置
     *
     * @return 最佳支付配置
     */
    WechatPayConfig getBestConfig();

    /**
     * 创建微信支付服务
     *
     * @param config 支付配置
     * @return 微信支付服务
     */
    WxPayService createWxPayService(WechatPayConfig config);

    /**
     * 获取默认的微信支付服务
     *
     * @return 默认微信支付服务
     */
    WxPayService getDefaultWxPayService();

    /**
     * 根据配置名称获取微信支付服务
     *
     * @param configName 配置名称
     * @return 微信支付服务
     */
    WxPayService getWxPayServiceByName(String configName);

    /**
     * 验证支付配置是否有效
     *
     * @param config 支付配置
     * @return 是否有效
     */
    boolean validateConfig(WechatPayConfig config);

    /**
     * 测试支付配置连接
     *
     * @param config 支付配置
     * @return 测试结果
     */
    boolean testConfig(WechatPayConfig config);
}
