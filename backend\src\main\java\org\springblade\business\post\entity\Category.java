/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.util.List;

/**
 * 广告分类实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_category")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "广告分类")
public class Category extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 分类名称
	 */
	@Schema(description = "分类名称")
	private String name;

	/**
	 * 上级分类ID
	 */
	@Schema(description = "上级分类ID")
	private Long parentId;

	/**
	 * 分类图标
	 */
	@Schema(description = "分类图标")
	private String icon;

	/**
	 * 分类描述
	 */
	@Schema(description = "分类描述")
	private String description;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sort;

	/**
	 * 是否启用：0-否，1-是
	 */
	@Schema(description = "是否启用")
	private Integer enabled;

	/**
	 * 是否启用审核：0-否，1-是
	 */
	@Schema(description = "是否启用审核")
	private Integer enableAudit;

	/**
	 * 提示信息
	 */
	@Schema(description = "提示信息")
	private String tip;

	/**
	 * 最大图片数
	 */
	@Schema(description = "最大图片数")
	private Integer maxImages;

	/**
	 * 允许的标签
	 */
	@Schema(description = "允许的标签")
	private String allowTags;

	/**
	 * 标签列表（非数据库字段）
	 */
	@TableField(exist = false)
	private List<Tag> tags;

	/**
	 * 子分类列表（非数据库字段）
	 */
	@TableField(exist = false)
	private List<Category> children;

	/**
	 * 帖子数量（非数据库字段）
	 */
	@TableField(exist = false)
	private Integer postCount;

	/**
	 * 父级分类名称（非数据库字段）
	 */
	@TableField(exist = false)
	private String parentName;


}
