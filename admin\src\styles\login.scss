.login-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
}

.login-weaper {
  margin: 0 auto;
  width: 100%;
  display: flex;
  height: 100%;
}

.login-left,
.login-border {
  position: relative;
  min-height: 500px;
  align-items: center;
  display: flex;
}

.login-left {
  padding-top: 100px;
  justify-content: center;
  flex-direction: column;
  color: #fff;
  width: 33.33%;
  position: relative;
  box-sizing: border-box;
  background-color: #ff8080;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 20%;
    right: 10%;
    width: 150px;
    height: 150px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
}

.brand-info {
  text-align: center;
  z-index: 1;
  position: relative;
}

.brand-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #fff;
  letter-spacing: 2px;
}

.brand-subtitle {
  font-size: 18px;
  color: #fff;
  font-weight: normal;
  letter-spacing: 1px;
}

.login-border {
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #333;
  width: 66.67%;
  box-sizing: border-box;
  background-color: #fff;
}

.login-main {
  margin: 0 auto;
  padding: 40px 60px;
  width: 80%;
  box-sizing: border-box;
}

.login-title {
  color: #000;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 28px;
  text-align: center;
  letter-spacing: 2px;
}

.login-welcome {
  color: #666;
  margin-bottom: 40px;
  font-size: 16px;
  text-align: center;
  font-weight: normal;
}

.login-menu {
  margin-top: 20px;
  width: 100%;
  text-align: center;
  a {
    color: #999;
    font-size: 12px;
    margin: 0px 8px;
    text-decoration: none;
    
    &:hover {
      color: #ff8080;
    }
  }
}

.forgot-password {
  margin-top: 20px;
  text-align: right;
  
  a {
    color: #999;
    font-size: 12px;
    text-decoration: none;
    
    &:hover {
      color: #ff8080;
    }
  }
}

.copyright {
  margin-top: 40px;
  text-align: center;
  color: #999;
  font-size: 12px;
}

// 隐藏第三方登录组件
.third {
  display: none;
}

.login-submit {
  width: 100%;
  height: 45px;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 300;
  cursor: pointer;
  margin-top: 30px;
  font-family: "neo";
  transition: 0.25s;
  background-color: #ff8080;
  border-color: #ff8080;
  border-radius: 0;
  
  &:hover {
    background-color: #ff6b6b;
    border-color: #ff6b6b;
  }
  
  &:focus {
    background-color: #ff8080;
    border-color: #ff8080;
  }
}

.login-form {
  margin: 20px 0;
  i {
    color: #333;
  }
  .el-input {
    margin-bottom: 15px;
    input {
      text-indent: 5px;
    }
    .el-input__wrapper{
      padding: 5px 10px;
      border-radius: 0;
      border: 1px solid #ddd;
      
      &:hover {
        border-color: #ff8080;
      }
      
      &.is-focus {
        border-color: #ff8080;
        box-shadow: 0 0 0 1px #ff8080;
      }
    }
    .el-input__suffix,.el-input__prefix{
      display: flex;
      align-items: center;
      text-align: center;
    }
    .el-input__prefix{
      margin-left: 8px;
    }
  }
}

.login-code {
  width: 100%;
  height: 100%;
  .el-input-group__append{
    background-color: #fff;
    border: 1px solid #ddd;
    border-left: none;
  }
}

.login-code-box{
  display: flex;
  align-items: center;
}

.login-code-img {
  min-width: 100px;
  padding: 0 5px;
  height: 30px;
  color: #333;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 3px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;
  box-sizing: border-box;
  cursor: pointer;
}

// 响应式设计
@media (max-width: 768px) {
  .login-weaper {
    flex-direction: column;
  }
  
  .login-left,
  .login-border {
    width: 100%;
    min-height: auto;
  }
  
  .login-left {
    padding: 40px 20px;
  }
  
  .login-main {
    padding: 30px 20px;
    width: 90%;
  }
  
  .brand-title {
    font-size: 24px;
  }
  
  .brand-subtitle {
    font-size: 16px;
  }
  
  .login-title {
    font-size: 24px;
  }
}