/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.subscriptionPlan.wrapper;

import lombok.AllArgsConstructor;
import org.springblade.business.subscriptionPlan.entity.SubscriptionPlanFeature;
import org.springblade.business.subscriptionPlan.vo.SubscriptionPlanFeatureVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;


/**
 * 订阅计划功能关联表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public class SubscriptionPlanFeatureWrapper extends BaseEntityWrapper<SubscriptionPlanFeature, SubscriptionPlanFeatureVO>  {

    public static SubscriptionPlanFeatureWrapper build() {
        return new SubscriptionPlanFeatureWrapper();
    }

	@Override
	public SubscriptionPlanFeatureVO entityVO(SubscriptionPlanFeature subscriptionPlanFeature) {
		SubscriptionPlanFeatureVO subscriptionPlanFeatureVO = BeanUtil.copyProperties(subscriptionPlanFeature, SubscriptionPlanFeatureVO.class);

		return subscriptionPlanFeatureVO;
	}

}
