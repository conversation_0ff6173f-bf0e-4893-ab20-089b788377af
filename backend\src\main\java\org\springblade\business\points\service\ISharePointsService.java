package org.springblade.business.points.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.points.entity.PointsRecord;
import org.springblade.business.points.vo.PointsRecordVO;

import java.util.Map;

/**
 * 分享积分服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/3
 */
public interface ISharePointsService {

    /**
     * 分享获得积分
     *
     * @param userId 用户ID
     * @param shareType 分享类型（post-帖子分享，institution-机构分享，qrcode-二维码分享）
     * @param businessId 业务ID（帖子ID、机构ID等）
     * @param shareChannel 分享渠道（wechat-微信，moments-朋友圈，qq-QQ等）
     * @return 分享结果
     */
    Map<String, Object> shareForPoints(String userId, String shareType, Long businessId, String shareChannel);

    /**
     * 检查今日分享次数
     *
     * @param userId 用户ID
     * @param shareType 分享类型
     * @return 今日分享次数
     */
    int getTodayShareCount(String userId, String shareType);

    /**
     * 检查是否可以分享获得积分
     *
     * @param userId 用户ID
     * @param shareType 分享类型
     * @param businessId 业务ID
     * @return 是否可以分享
     */
    boolean canShareForPoints(String userId, String shareType, Long businessId);

    /**
     * 获取分享积分记录
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param shareType 分享类型
     * @return 分享积分记录
     */
    IPage<PointsRecordVO> getSharePointsRecords(IPage<PointsRecord> page, String userId, String shareType);

    /**
     * 获取分享积分统计
     *
     * @param userId 用户ID
     * @return 分享积分统计
     */
    Map<String, Object> getSharePointsStats(String userId);

    /**
     * 获取分享积分配置
     *
     * @return 分享积分配置
     */
    Map<String, Object> getSharePointsConfig();

    /**
     * 记录分享行为（不一定获得积分）
     *
     * @param userId 用户ID
     * @param shareType 分享类型
     * @param businessId 业务ID
     * @param shareChannel 分享渠道
     * @return 是否记录成功
     */
    boolean recordShareAction(String userId, String shareType, Long businessId, String shareChannel);

    /**
     * 清理用户的分享缓存数据
     *
     * @param userId 用户ID
     */
    void clearUserShareCache(String userId);

    /**
     * 清理所有损坏的分享缓存数据
     */
    void clearCorruptedShareCache();
}
