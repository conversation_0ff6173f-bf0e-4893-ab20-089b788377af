/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.controller;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.post.dto.PostAuditDTO;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.request.PostSaveOrUpdateRequest;
import org.springblade.business.post.service.ISupPostService;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.post.wrapper.PostWrapper;
import org.springblade.common.constant.bizz.PublishStatusEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.Map;


/**
 * 百事通信息贴 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/post")
@io.swagger.v3.oas.annotations.tags.Tag(name = "百事通信息贴", description = "百事通信息贴接口")
@Validated
public class SupPostController extends BladeController {

	private ISupPostService postService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入post")
	public R<SupPostVO> detail(SupPostVO post) {
		SupPostVO detail = postService.getPostDetail(post.getId());
		return R.data(detail);
	}


	/**
	 * 自定义分页 百事通信息贴
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入post")
	public R<IPage<SupPostVO>> page(SupPostVO post, Query query) {
		IPage<SupPostVO> pages = postService.selectPostPage(Condition.getPage(query), post);
		return R.data(pages);
	}
	/**
	 * 批量审核帖子
	 */
	@PostMapping("/batch-audit")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "批量审核帖子", description = "后台批量审核帖子")
	public R<Boolean> batchAuditPosts(@Parameter(description = "帖子ID列表") @RequestParam String postIds,
									  @Parameter(description = "审核状态") @RequestParam String auditStatus,
									  @Parameter(description = "审核备注") @RequestParam(required = false) String auditRemark) {
		boolean result = postService.batchAuditPosts(Func.toLongList(postIds), auditStatus, auditRemark);
		return R.data(result);
	}

	/**
	 * 删除帖子（后台管理）
	 */
	@DeleteMapping("/{id}")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "删除帖子", description = "后台删除帖子")
	public R<Boolean> deletePost(@Parameter(description = "帖子ID") @PathVariable Long id) {
		boolean result = postService.removeById(id);
		return R.data(result);
	}

	/**
	 * 批量删除帖子
	 */
	@DeleteMapping("/batch")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "批量删除帖子", description = "后台批量删除帖子")
	public R<Boolean> batchDeletePosts(@Parameter(description = "帖子ID列表") @RequestParam String postIds) {
		return R.data(postService.remove(Func.toLongList(postIds)));
	}

	/**
	 * 置顶帖子
	 */
	@PostMapping("/{id}/top")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "置顶帖子", description = "设置帖子置顶状态")
	public R<Boolean> topPost(@Parameter(description = "帖子ID") @PathVariable Long id,
							  @Parameter(description = "是否置顶") @RequestParam Boolean isTop) {
		boolean result = postService.topPost(id, isTop);
		return R.data(result);
	}

	/**
	 * 获取审核统计
	 */
	@GetMapping("/audit-stats")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取审核统计", description = "获取帖子审核统计数据")
	public R<Map<String, Object>> getAuditStats() {
		Map<String, Object> stats = postService.getAuditStats();
		return R.data(stats);
	}

	/**
	 * 获取帖子统计
	 */
	@GetMapping("/stats")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "获取帖子统计", description = "获取帖子统计数据")
	public R<Map<String, Object>> getPostStats() {
		Map<String, Object> stats = postService.getPostStats();
		return R.data(stats);
	}

	/**
	 * 修改帖子发布状态
	 */
	@PostMapping("/{id}/publish-status")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "修改帖子发布状态", description = "修改单个帖子的发布状态（上架/下架）")
	public R<Boolean> updatePublishStatus(@Parameter(description = "帖子ID") @PathVariable Long id,
										  @Parameter(description = "发布状态：2-上架，1-下架") @RequestParam String publishStatus) {
		boolean result = postService.updatePublishStatus(id, publishStatus);
		return R.data(result);
	}

	/**
	 * 批量修改帖子发布状态
	 */
	@PostMapping("/batch-publish-status")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "批量修改帖子发布状态", description = "批量修改帖子的发布状态（上架/下架）")
	public R<Boolean> batchUpdatePublishStatus(@Parameter(description = "帖子ID列表，逗号分隔") @RequestParam String postIds,
											   @Parameter(description = "发布状态：1-上架，0-下架") @RequestParam String publishStatus) {
		boolean result = postService.batchUpdatePublishStatus(Func.toLongList(postIds), publishStatus);
		return R.data(result);
	}

	/**
	 * 批量上架帖子
	 */
	@PostMapping("/batch-online")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "批量上架帖子", description = "批量将帖子设置为上架状态")
	public R<Boolean> batchOnlinePosts(@Parameter(description = "帖子ID列表，逗号分隔") @RequestParam String postIds) {
		boolean result = postService.batchUpdatePublishStatus(Func.toLongList(postIds), PublishStatusEnum.PUBLISHED.getValue());
		return R.data(result);
	}

	/**
	 * 批量下架帖子
	 */
	@PostMapping("/batch-offline")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@Operation(summary = "批量下架帖子", description = "批量将帖子设置为下架状态")
	public R<Boolean> batchOfflinePosts(@Parameter(description = "帖子ID列表，逗号分隔") @RequestParam String postIds) {
		boolean result = postService.batchUpdatePublishStatus(Func.toLongList(postIds), PublishStatusEnum.DRAFT.getValue());
		return R.data(result);
	}
}
