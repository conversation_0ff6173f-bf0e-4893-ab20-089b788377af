/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 积分兑换记录表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_points_exchange")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "积分兑换记录表")
public class PointsExchange extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 用户OpenID
     */
    @Schema(description = "用户OpenID")
    private String openId;
    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long goodsId;
    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String goodsName;
    /**
     * 兑换数量
     */
    @Schema(description = "兑换数量")
    private Integer quantity;
    /**
     * 消耗积分
     */
    @Schema(description = "消耗积分")
    private Integer pointsCost;
    /**
     * 兑换时间
     */
    @Schema(description = "兑换时间")
    private LocalDateTime exchangeTime;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


	/**
	 * 商品信息字段
	 *
	 * @return 主键值
	 */
	@Schema(description = "商品信息字段")
	@TableField(exist = false)
	private PointsGoods pointsGoods;


}
