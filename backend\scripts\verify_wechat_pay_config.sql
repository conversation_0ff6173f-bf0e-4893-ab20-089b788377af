-- 微信支付配置验证脚本

-- 1. 检查表是否存在
SELECT
    TABLE_NAME,
    TABLE_COMMENT,
    CREATE_TIME
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'urb_wechat_pay_config';

-- 2. 检查表结构
DESCRIBE urb_wechat_pay_config;

-- 3. 检查现有配置
SELECT
    id,
    config_name,
    app_id,
    mch_id,
    CONCAT(LEFT(api_key, 4), '****', RIGHT(api_key, 4)) as api_key_masked,
    CONCAT(LEFT(api_v3_key, 4), '****', RIGHT(api_v3_key, 4)) as api_v3_key_masked,
    cert_path,
    cert_serial_no,
    private_key_path,
    notify_url,
    is_sandbox,
    is_enabled,
    is_default,
    priority,
    remark,
    create_time,
    tenant_id
FROM urb_wechat_pay_config
ORDER BY priority DESC, create_time DESC;

-- 4. 检查默认配置
SELECT
    COUNT(*) as default_config_count,
    GROUP_CONCAT(config_name) as default_configs
FROM urb_wechat_pay_config
WHERE is_default = 1 AND is_enabled = 1;

-- 5. 检查启用的配置
SELECT
    COUNT(*) as enabled_config_count,
    GROUP_CONCAT(config_name ORDER BY priority DESC) as enabled_configs
FROM urb_wechat_pay_config
WHERE is_enabled = 1;

-- 6. 检查配置完整性
SELECT
    config_name,
    CASE
        WHEN app_id IS NULL OR app_id = '' THEN 'Missing app_id'
        WHEN mch_id IS NULL OR mch_id = '' THEN 'Missing mch_id'
        WHEN api_key IS NULL OR api_key = '' OR api_key = 'YOUR_API_KEY_32_CHARACTERS_HERE' THEN 'Missing or placeholder api_key'
        WHEN api_v3_key IS NULL OR api_v3_key = '' THEN 'Missing api_v3_key'
        WHEN cert_path IS NULL OR cert_path = '' THEN 'Missing cert_path'
        WHEN cert_serial_no IS NULL OR cert_serial_no = '' THEN 'Missing cert_serial_no'
        WHEN private_key_path IS NULL OR private_key_path = '' THEN 'Missing private_key_path'
        WHEN notify_url IS NULL OR notify_url = '' OR notify_url LIKE '%your-domain.com%' THEN 'Missing or placeholder notify_url'
        ELSE 'OK'
    END as config_status
FROM urb_wechat_pay_config
WHERE is_enabled = 1;

-- 7. 检查证书序列号格式
SELECT
    config_name,
    cert_serial_no,
    CASE
        WHEN cert_serial_no REGEXP '^[A-F0-9]{40}$' THEN 'Valid'
        ELSE 'Invalid format (should be 40 hex characters)'
    END as serial_no_status
FROM urb_wechat_pay_config
WHERE cert_serial_no IS NOT NULL;

-- 8. 检查API密钥长度
SELECT
    config_name,
    LENGTH(api_key) as api_key_length,
    CASE
        WHEN LENGTH(api_key) = 32 THEN 'Valid'
        ELSE 'Invalid (should be 32 characters)'
    END as api_key_status
FROM urb_wechat_pay_config
WHERE api_key IS NOT NULL AND api_key != 'YOUR_API_KEY_32_CHARACTERS_HERE';

-- 9. 检查回调URL格式
SELECT
    config_name,
    notify_url,
    CASE
        WHEN notify_url LIKE 'https://%' THEN 'Valid HTTPS'
        WHEN notify_url LIKE 'http://%' THEN 'HTTP (should use HTTPS in production)'
        WHEN notify_url LIKE '%your-domain.com%' THEN 'Placeholder URL'
        ELSE 'Invalid format'
    END as notify_url_status
FROM urb_wechat_pay_config
WHERE notify_url IS NOT NULL;

-- 10. 生成配置报告
SELECT
    '=== 微信支付配置验证报告 ===' as report_title,
    NOW() as report_time;

SELECT
    CONCAT('总配置数: ', COUNT(*)) as summary
FROM urb_wechat_pay_config
UNION ALL
SELECT
    CONCAT('启用配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_enabled = 1
UNION ALL
SELECT
    CONCAT('默认配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_default = 1 AND is_enabled = 1
UNION ALL
SELECT
    CONCAT('沙箱配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_sandbox = 1
UNION ALL
SELECT
    CONCAT('生产配置数: ', COUNT(*))
FROM urb_wechat_pay_config WHERE is_sandbox = 0;
