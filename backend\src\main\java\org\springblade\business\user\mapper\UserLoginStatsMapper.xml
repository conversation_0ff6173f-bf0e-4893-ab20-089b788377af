<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.UserLoginStatsMapper">

    <insert id="upsert">
        INSERT INTO urb_user_request_stats
            (user_id, last_request_time, today_request_count, sync_date, create_time)
        VALUES (#{userId}, #{lastLoginTime}, #{todayLoginCount}, #{syncDate}, NOW())
            ON DUPLICATE KEY UPDATE
                                 last_request_time = VALUES(last_request_time),
                                 today_request_count = VALUES(today_request_count),
                                 update_time = NOW()
    </insert>

    <insert id="batchUpsert">
        INSERT INTO urb_user_request_stats
        (user_id, last_request_time, today_request_count , sync_time,create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.lastRequestTime}, #{item.todayRequestCount},#{item.syncTime}, NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        last_request_time = VALUES(last_request_time),
        today_request_count = VALUES(today_request_count),
        update_time = NOW()
    </insert>
</mapper>
