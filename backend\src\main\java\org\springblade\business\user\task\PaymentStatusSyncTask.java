package org.springblade.business.user.task;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.entity.RechargeOrder;
import org.springblade.business.user.mapper.RechargeOrderMapper;
import org.springblade.business.user.service.IRechargeService;
import org.springblade.business.user.service.IWechatPayService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 支付状态同步定时任务
 * 用于同步微信支付状态，处理回调丢失的情况
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
@AllArgsConstructor
public class PaymentStatusSyncTask {

    private final RechargeOrderMapper rechargeOrderMapper;
    private final IRechargeService rechargeService;
    private final IWechatPayService wechatPayService;

    /**
     * 同步待支付订单状态
     * 每2分钟执行一次，查询最近10分钟内的待支付订单
     */
    @Scheduled(fixedRate = 2 * 60 * 1000) // 2分钟
    public void syncPendingOrderStatus() {
        try {
            log.debug("开始同步待支付订单状态");

            // 查询最近10分钟内的待支付订单（更频繁的检查）
            List<RechargeOrder> pendingOrders = rechargeOrderMapper.selectPendingOrdersForSync(10, 50);

            if (pendingOrders.isEmpty()) {
                log.debug("没有需要同步的待支付订单");
                return;
            }

            log.info("找到 {} 个待支付订单需要同步状态", pendingOrders.size());

            int successCount = 0;
            int failCount = 0;
            int updatedCount = 0;

            for (RechargeOrder order : pendingOrders) {
                try {
                    boolean syncResult = syncSingleOrderStatus(order);
                    if (syncResult) {
                        successCount++;
                        // 检查订单状态是否有变化
                        RechargeOrder updatedOrder = rechargeOrderMapper.selectByOrderNo(order.getOrderNo());
                        if (updatedOrder != null && !order.getOrderStatus().equals(updatedOrder.getOrderStatus())) {
                            updatedCount++;
                            log.info("订单状态已更新: orderNo={}, {} -> {}",
                                order.getOrderNo(), order.getOrderStatus(), updatedOrder.getOrderStatus());
                        }
                    } else {
                        failCount++;
                    }

                    // 避免频繁调用微信接口，间隔200ms
                    Thread.sleep(200);

                } catch (Exception e) {
                    log.error("同步订单状态异常: orderNo={}", order.getOrderNo(), e);
                    failCount++;
                }
            }

            log.info("订单状态同步完成: 成功={}, 失败={}, 状态更新={}", successCount, failCount, updatedCount);

        } catch (Exception e) {
            log.error("同步待支付订单状态任务异常", e);
        }
    }

    /**
     * 同步单个订单状态
     *
     * @param order 充值订单
     * @return 是否同步成功
     */
    private boolean syncSingleOrderStatus(RechargeOrder order) {
        try {
            log.debug("同步订单状态: orderNo={}, currentStatus={}", order.getOrderNo(), order.getOrderStatus());

            // 检查订单是否需要查询微信状态
            if (!needsWechatQuery(order)) {
                log.debug("订单无需查询微信状态: orderNo={}, status={}", order.getOrderNo(), order.getOrderStatus());
                return true;
            }

            // 调用微信查询订单接口
            Map<String, Object> queryResult = wechatPayService.queryOrderStatus(order.getWxOutTradeNo());

            if (queryResult == null) {
                log.warn("查询微信订单状态失败: orderNo={}, 可能是网络问题或配置问题", order.getOrderNo());
                return false;
            }

            String tradeState = (String) queryResult.get("trade_state");
            String transactionId = (String) queryResult.get("transaction_id");
            String timeEnd = (String) queryResult.get("time_end");
            String tradeStateDesc = (String) queryResult.get("trade_state_desc");

            log.info("微信订单状态查询结果: orderNo={}, tradeState={}, desc={}",
                order.getOrderNo(), tradeState, tradeStateDesc);

            // 根据微信订单状态更新本地订单
            return handleWechatTradeState(order, tradeState, transactionId, timeEnd, tradeStateDesc);

        } catch (Exception e) {
            log.error("同步单个订单状态异常: orderNo={}", order.getOrderNo(), e);
            return false;
        }
    }

    /**
     * 检查订单是否需要查询微信状态
     */
    private boolean needsWechatQuery(RechargeOrder order) {
        // 只有待支付状态的订单才需要查询
        return RechargeOrder.OrderStatus.PENDING.getCode().equals(order.getOrderStatus()) ||
               (RechargeOrder.OrderStatus.PAID.getCode().equals(order.getOrderStatus()) &&
                !RechargeOrder.OrderStatus.SUCCESS.getCode().equals(order.getOrderStatus()));
    }

    /**
     * 处理微信交易状态
     */
    private boolean handleWechatTradeState(RechargeOrder order, String tradeState,
                                         String transactionId, String timeEnd, String tradeStateDesc) {
        try {
            switch (tradeState) {
                case "SUCCESS":
                    return handleSuccessState(order, transactionId, timeEnd);

                case "REFUND":
                    return handleRefundState(order);

                case "CLOSED":
                    return handleClosedState(order);

                case "REVOKED":
                    return handleRevokedState(order);

                case "NOTPAY":
                case "USERPAYING":
                    return handlePayingState(order, tradeState);

                case "PAYERROR":
                    return handlePayErrorState(order, tradeStateDesc);

                default:
                    log.warn("未知的微信订单状态: orderNo={}, tradeState={}", order.getOrderNo(), tradeState);
                    return false;
            }
        } catch (Exception e) {
            log.error("处理微信交易状态异常: orderNo={}, tradeState={}", order.getOrderNo(), tradeState, e);
            return false;
        }
    }

    /**
     * 处理支付成功状态
     */
    private boolean handleSuccessState(RechargeOrder order, String transactionId, String timeEnd) {
        if (RechargeOrder.OrderStatus.PENDING.getCode().equals(order.getOrderStatus()) ||
            RechargeOrder.OrderStatus.PAID.getCode().equals(order.getOrderStatus())) {

            boolean handleResult = rechargeService.handlePaymentSuccess(
                order.getOrderNo(), transactionId, timeEnd);

            if (handleResult) {
                log.info("同步处理支付成功: orderNo={}, transactionId={}", order.getOrderNo(), transactionId);
                return true;
            } else {
                log.error("处理支付成功失败: orderNo={}", order.getOrderNo());
                return false;
            }
        }

        log.debug("订单已是成功状态，无需处理: orderNo={}", order.getOrderNo());
        return true;
    }

    /**
     * 处理退款状态
     */
    private boolean handleRefundState(RechargeOrder order) {
        if (!RechargeOrder.PaymentStatus.REFUNDED.getCode().equals(order.getPaymentStatus())) {
            order.setPaymentStatus(RechargeOrder.PaymentStatus.REFUNDED.getCode());
            order.setOrderStatus(RechargeOrder.OrderStatus.FAILED.getCode());
            order.setRemark(order.getRemark() + " [系统同步: 订单已退款]");
            rechargeOrderMapper.updateById(order);
            log.info("同步订单退款状态: orderNo={}", order.getOrderNo());
            return true;
        }
        return true;
    }

    /**
     * 处理订单关闭状态
     */
    private boolean handleClosedState(RechargeOrder order) {
        if (!RechargeOrder.OrderStatus.CANCELLED.getCode().equals(order.getOrderStatus())) {
            order.setOrderStatus(RechargeOrder.OrderStatus.CANCELLED.getCode());
            order.setRemark(order.getRemark() + " [系统同步: 订单已关闭]");
            rechargeOrderMapper.updateById(order);
            log.info("同步订单关闭状态: orderNo={}", order.getOrderNo());
            return true;
        }
        return true;
    }

    /**
     * 处理订单撤销状态
     */
    private boolean handleRevokedState(RechargeOrder order) {
        if (!RechargeOrder.OrderStatus.CANCELLED.getCode().equals(order.getOrderStatus())) {
            order.setOrderStatus(RechargeOrder.OrderStatus.CANCELLED.getCode());
            order.setRemark(order.getRemark() + " [系统同步: 订单已撤销]");
            rechargeOrderMapper.updateById(order);
            log.info("同步订单撤销状态: orderNo={}", order.getOrderNo());
            return true;
        }
        return true;
    }

    /**
     * 处理支付中状态
     */
    private boolean handlePayingState(RechargeOrder order, String tradeState) {
        log.debug("订单仍在支付中: orderNo={}, tradeState={}", order.getOrderNo(), tradeState);
        // 支付中状态不需要更新订单，保持原状态
        return true;
    }

    /**
     * 处理支付错误状态
     */
    private boolean handlePayErrorState(RechargeOrder order, String tradeStateDesc) {
        if (!RechargeOrder.OrderStatus.FAILED.getCode().equals(order.getOrderStatus())) {
            order.setOrderStatus(RechargeOrder.OrderStatus.FAILED.getCode());
            order.setRemark(order.getRemark() + " [系统同步: 支付失败 - " + tradeStateDesc + "]");
            rechargeOrderMapper.updateById(order);
            log.info("同步订单支付失败状态: orderNo={}, desc={}", order.getOrderNo(), tradeStateDesc);
            return true;
        }
        return true;
    }

    /**
     * 清理过期订单
     * 每小时执行一次，将超过2小时未支付的订单标记为过期
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void cleanExpiredOrders() {
        try {
            log.debug("开始清理过期订单");

            // 查询超过2小时未支付的订单
            List<RechargeOrder> expiredOrders = rechargeOrderMapper.selectExpiredOrders(120, 500);

            if (expiredOrders.isEmpty()) {
                log.debug("没有需要清理的过期订单");
                return;
            }

            log.info("找到 {} 个过期订单需要清理", expiredOrders.size());

            int cleanCount = 0;
            for (RechargeOrder order : expiredOrders) {
                try {
                    // 只处理待支付状态的订单
                    if (RechargeOrder.OrderStatus.PENDING.getCode().equals(order.getOrderStatus())) {
                        boolean cancelResult = rechargeService.cancelRechargeOrder(
                            order.getOrderNo(), "订单超时自动取消");
                        if (cancelResult) {
                            cleanCount++;
                            log.debug("清理过期订单: orderNo={}", order.getOrderNo());
                        }
                    }
                } catch (Exception e) {
                    log.error("清理过期订单异常: orderNo={}", order.getOrderNo(), e);
                }
            }

            log.info("过期订单清理完成: 清理数量={}", cleanCount);

        } catch (Exception e) {
            log.error("清理过期订单任务异常", e);
        }
    }

    /**
     * 统计支付数据
     * 每天凌晨1点执行一次
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generatePaymentStats() {
        try {
            log.info("开始生成支付统计数据");

            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            LocalDateTime startOfDay = yesterday.withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = yesterday.withHour(23).withMinute(59).withSecond(59);

            // 这里可以添加统计逻辑
            // 例如：统计昨日充值金额、成功率、失败原因等

            log.info("支付统计数据生成完成");

        } catch (Exception e) {
            log.error("生成支付统计数据异常", e);
        }
    }
}
