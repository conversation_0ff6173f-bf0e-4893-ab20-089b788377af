import request from '@/axios';

/**
 * 获取浏览记录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getViewRecords = (params) => {
  return request({
    url: '/api/blade-ad/datarecord/view/list',
    method: 'get',
    params
  })
}

/**
 * 获取点赞记录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getLikeRecords = (params) => {
  return request({
    url: '/api/blade-ad/datarecord/like/list',
    method: 'get',
    params
  })
}

/**
 * 获取反馈记录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getFeedbackRecords = (params) => {
  return request({
    url: '/api/blade-ad/datarecord/feedback/list',
    method: 'get',
    params
  })
}

/**
 * 获取收藏记录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getFavoriteRecords = (params) => {
  return request({
    url: '/api/blade-ad/datarecord/favorite/list',
    method: 'get',
    params
  })
}

/**
 * 数据记录API配置
 * 用于DataRecordDialog组件
 */
export const dataRecordApiConfig = {
  view: {
    getList: getViewRecords
  },
  like: {
    getList: getLikeRecords
  },
  feedback: {
    getList: getFeedbackRecords
  },
  favorite: {
    getList: getFavoriteRecords
  }
}