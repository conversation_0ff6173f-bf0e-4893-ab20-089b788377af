/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.invite.wrapper;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.business.invite.vo.InviteRelationVO;
import org.springblade.miniapp.entity.InviteRelation;

/**
 * 邀请关系表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
public class InviteRelationWrapper extends BaseEntityWrapper<InviteRelation, InviteRelationVO>  {

    public static InviteRelationWrapper build() {
        return new InviteRelationWrapper();
    }

	@Override
	public InviteRelationVO entityVO(InviteRelation inviteRelation) {
		InviteRelationVO inviteRelationVO = BeanUtil.copyProperties(inviteRelation, InviteRelationVO.class);

		return inviteRelationVO;
	}

}
