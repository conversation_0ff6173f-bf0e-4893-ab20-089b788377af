<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.businesscard_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-check"
                   plain
                   @click="handleBatchAudit">批量审核
        </el-button>
        <el-button type="warning"
                   icon="el-icon-download"
                   plain
                   @click="handleBatchOffline">批量下架
        </el-button>
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.auditStatus === 0"
                   type="success"
                   size="small"
                   @click="handleAudit(row, 1)">
          通过
        </el-button>
        <el-button v-if="row.auditStatus === 0"
                   type="danger"
                   size="small"
                   @click="handleAudit(row, 2)">
          拒绝
        </el-button>
        <el-button v-if="row.publishStatus == 1"
                   type="warning"
                   size="small"
                   @click="handleStatusChange(row, 0)">
          下架
        </el-button>
        <el-button v-if="row.publishStatus == 0 || !row.publishStatus"
                   type="primary"
                   size="small"
                   @click="handleStatusChange(row, 1)">
          上架
        </el-button>
      </template>
      <template #createUser="{ row }">
        <el-button
          type="text"
          @click="showUserDetail(row.createUser)"
          style="color: #409EFF; text-decoration: underline;">
          {{ getUserNickname(row) }}
        </el-button>
      </template>

      <!-- 浏览量插槽 -->
      <template #viewCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'view')"
          :disabled="!getViewCount(row)">
          {{ getViewCount(row) }}
        </el-button>
      </template>

      <!-- 点赞数插槽 -->
      <template #likeCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'like')"
          :disabled="!getLikeCount(row)">
          {{ getLikeCount(row) }}
        </el-button>
      </template>

      <!-- 反馈数插槽 -->
      <template #feedbackCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'feedback')"
          :disabled="!getFeedbackCount(row)">
          {{ getFeedbackCount(row) }}
        </el-button>
      </template>

      <!-- 收藏数插槽 -->
      <template #favoriteCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'favorite')"
          :disabled="!getFavoriteCount(row)">
          {{ getFavoriteCount(row) }}
        </el-button>
      </template>
    </avue-crud>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="名片审核" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditRemark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入审核备注（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model="userDetailVisible"
      :user-id="selectedUserId"
      @manage-user="handleManageUser" />

    <!-- 数据记录查看对话框 -->
    <DataRecordDialog
      v-if="dataRecordDialogVisible && dataRecordApiConfig[currentRecordType]"
      v-model="dataRecordDialogVisible"
      :record-type="currentRecordType"
      :relevancy-id="currentRelevancyId"
      :relevancy-type="'1'"
      :api-config="dataRecordApiConfig[currentRecordType]" />
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, auditBusinessCard, batchAuditBusinessCards, updatePublishStatus, batchOfflineCards} from "@/api/ad/businesscard";
  import {getUserDict} from "@/api/ad/user";
import { baseUrl } from "@/config/env";
  import {mapGetters} from "vuex";
  import UserDetailDialog from '@/components/UserDetailDialog.vue';
  import DataRecordDialog from '@/components/DataRecordDialog.vue';
  import { dataRecordApiConfig } from '@/api/ad/datarecord';

  export default {
    components: {
      UserDetailDialog,
      DataRecordDialog
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        auditDialogVisible: false,
        auditForm: {
          cardIds: null,
          auditStatus: 1,
          auditRemark: ''
        },
        userDetailVisible: false,
        selectedUserId: null,
        // 数据记录对话框相关
        dataRecordDialogVisible: false,
        currentRecordType: '',
        currentRelevancyId: null,
        dataRecordApiConfig: dataRecordApiConfig,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          menu: true,
          defaultSort: {
            prop: 'createTime',
            order: 'descending'
          },
          column: [
            {
              label: "用户",
              prop: "createUser",
              type: "select",
              dicData: [],
              props: {
                label: "nickname",
                value: "id"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              slot: true
            },
            {
              label: "公司名称",
              prop: "company",
              rules: [{
                required: true,
                message: "请输入公司名称",
                trigger: "blur"
              }]
            },
            {
              label: "职位",
              prop: "jobTitle",
              rules: [{
                required: true,
                message: "请输入职位",
                trigger: "blur"
              }]
            },
            {
              label: "业务简介",
              prop: "businessProfile",
              rules: [{
                required: true,
                message: "请输入业务简介",
                trigger: "blur"
              }]
            },
            {
              label: "姓名",
              prop: "fullName",
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }]
            },
            {
              label: "性别(0-保密，1-男，2-女)",
              prop: "gender",
              type:'select',
               props: {
                label: "dictValue",
                value: "dictKey"
              },
              search:true,
              dicUrl: baseUrl + '/blade-system/dict/dictionary?code=sex',
              rules: [{
                required: true,
                message: "请输入性别(0-保密，1-男，2-女)",
                trigger: "blur"
              }]
            },
            {
              label: "电话",
              prop: "phone",
              rules: [{
                required: true,
                message: "请输入电话",
                trigger: "blur"
              }]
            },
            {
              label: "地址",
              prop: "address",
              rules: [{
                required: true,
                message: "请输入地址",
                trigger: "blur"
              }]
            },
            {
              label: "微信",
              prop: "weixin",
              rules: [{
                required: true,
                message: "请输入微信",
                trigger: "blur"
              }]
            },
            {
              label: "头像",
              prop: "avatar",
              rules: [{
                required: true,
                message: "请输入头像",
                trigger: "blur"
              }]
            },
            {
              label: "图片",
              prop: "images",
              rules: [{
                required: true,
                message: "请输入图片",
                trigger: "blur"
              }]
            },
            {
              label: "发布状态",
              prop: "publishStatus",
              type: "select",
              dataType: 'number',
              dicUrl: baseUrl + "/blade-system/dict/dictionary?code=common_publish_status",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
            },
            {
              label: "备注信息",
              prop: "description",
              rules: [{
                required: true,
                message: "请输入备注信息",
                trigger: "blur"
              }]
            },
            {
              label: "是否公开（0-否，1-是）",
              prop: "isPublic",
              dataType:'number',
              dicData: [
                { label: '否', value: 0 },
                { label: '是', value: 1 }
              ],
              rules: [{
                required: true,
                message: "请输入是否公开（0-否，1-是）",
                trigger: "blur"
              }]
            },
            {
              label: "审核状态",
              prop: "auditStatus",
              type: "select",
              dicData: [
                { label: '待审核', value: 0 },
                { label: '已通过', value: 1 },
                { label: '已拒绝', value: 2 }
              ],
              search: true,
              addDisplay: false,
              editDisplay: false
            },
            {
              label: "发布状态",
              prop: "publishStatus",
              type: "select",
              dicUrl: baseUrl + "/blade-system/dict/dictionary?code=common_publish_status",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
              addDisplay: false,
              editDisplay: false
            },
            {
              label: "浏览量",
              prop: "viewCount",
              type: "number",
              sortable: true,
              slot: true,
              addDisplay: false,
              editDisplay: false,
              hide: false
            },
            {
              label: "点赞数",
              prop: "likeCount",
              type: "number",
              sortable: true,
              slot: true,
              addDisplay: false,
              editDisplay: false,
              hide: false
            },
            {
              label: "反馈数",
              prop: "feedbackCount",
              type: "number",
              sortable: true,
              slot: true,
              addDisplay: false,
              editDisplay: false,
              hide: false
            },
            {
              label: "收藏数",
              prop: "favoriteCount",
              type: "number",
              sortable: true,
              slot: true,
              addDisplay: false,
              editDisplay: false,
              hide: false
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetimerange",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              search: true,
              searchSpan: 12,
              searchRange: true,

              addDisplay: false,
              editDisplay: false,
              sortable: true
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.businesscard_add, false),
          viewBtn: this.validData(this.permission.businesscard_view, false),
          delBtn: this.validData(this.permission.businesscard_delete, false),
          editBtn: this.validData(this.permission.businesscard_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 获取用户昵称
      getUserNickname(row) {
        // 从用户字典中查找昵称
        const userCol = this.option.column.find(c => c.prop === 'createUser');
        if (userCol && userCol.dicData) {
          const user = userCol.dicData.find(u => u.id === row.createUser);
          return user ? user.nickname : '未知用户';
        }
        return '未知用户';
      },
      // 获取浏览量
      getViewCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.viewCount !== undefined) {
          return row.stats.viewCount;
        }
        if (row.viewCount !== undefined) {
          return row.viewCount;
        }
        return 0;
      },
      // 获取点赞数
      getLikeCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.likeCount !== undefined) {
          return row.stats.likeCount;
        }
        if (row.likeCount !== undefined) {
          return row.likeCount;
        }
        return 0;
      },
      // 获取反馈数
      getFeedbackCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.feedbackCount !== undefined) {
          return row.stats.feedbackCount;
        }
        if (row.feedbackCount !== undefined) {
          return row.feedbackCount;
        }
        return 0;
      },
      // 获取收藏数
      getFavoriteCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.favoriteCount !== undefined) {
          return row.stats.favoriteCount;
        }
        if (row.favoriteCount !== undefined) {
          return row.favoriteCount;
        }
        return 0;
      },
      // 显示数据记录对话框
      showDataRecord(row, recordType) {
        if (!row.id) {
          this.$message.warning('名片信息不存在');
          return;
        }

        // 检查对应的数量是否大于0
        let count = 0;
        if (recordType === 'view') {
          count = this.getViewCount(row);
        } else if (recordType === 'like') {
          count = this.getLikeCount(row);
        } else if (recordType === 'feedback') {
          count = this.getFeedbackCount(row);
        } else if (recordType === 'favorite') {
          count = this.getFavoriteCount(row);
        }

        if (count === 0) {
          this.$message.info('暂无相关记录');
          return;
        }

        this.currentRecordType = recordType;
        this.currentRelevancyId = row.id;
        this.dataRecordDialogVisible = true;
      },
      // 显示用户详情
      showUserDetail(userId) {
        if (!userId) {
          this.$message.warning('用户信息不存在');
          return;
        }
        this.selectedUserId = userId;
        this.userDetailVisible = true;
      },
      // 处理管理用户事件
      handleManageUser(userDetail) {
        this.$message.info(`管理用户: ${userDetail.nickname}`);
        // 这里可以跳转到用户管理页面或执行其他管理操作
        // this.$router.push(`/admin/user/detail/${userDetail.id}`);
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      // 审核相关方法
      handleAudit(row, status) {
        this.auditForm.cardIds = row.id;
        this.auditForm.auditStatus = status;
        this.auditForm.auditRemark = '';
        this.auditDialogVisible = true;
      },
      handleBatchAudit() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.auditForm.cardIds = this.ids;
        this.auditForm.auditStatus = 1;
        this.auditForm.auditRemark = '';
        this.auditDialogVisible = true;
      },
      submitAudit() {
        batchAuditBusinessCards(this.auditForm).then(() => {
          this.auditDialogVisible = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "审核操作成功!"
          });
        }).catch(error => {
          this.$message.error("审核操作失败");
        });
      },
      // 处理状态变更（上架/下架）
      handleStatusChange(row, publishStatus) {
        const action = publishStatus == 1 ? '上架' : '下架';
        this.$confirm(`确定要${action}这张名片吗？`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          updatePublishStatus(row.id, publishStatus).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: `${action}操作成功!`
            });
          }).catch(() => {
            this.$message.error(`${action}操作失败`);
          });
        });
      },
      // 批量下架
      handleBatchOffline() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定要下架选中的名片吗？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          const cardIds = this.selectionList.map(item => item.id);
          batchOfflineCards(cardIds).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "批量下架操作成功!"
            });
            this.$refs.crud.toggleSelection();
          }).catch(() => {
            this.$message.error("批量下架操作失败");
          });
        });
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        // 动态加载用户字典
        getUserDict().then(res => {
          const users = res.data.data || [];
          const userCol = this.option.column.find(c => c.prop === 'createUser');
          if (userCol) userCol.dicData = users;
        }).catch(error => {
          console.error('加载用户字典失败:', error);
        });

        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
