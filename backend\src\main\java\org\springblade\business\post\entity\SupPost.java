/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.convert.ListToStringTypeHandler;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 百事通信息贴实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_post")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "百事通信息贴")
public class SupPost extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;


	/**
	 *
	 */
	@Schema(description = "地址")
	private String location;

	/**
	 * 经度
	 */
	@Schema(description = "经度")
	private BigDecimal longitude;

	/**
	 * 纬度
	 */
	@Schema(description = "纬度")
	private BigDecimal latitude;



	/**
	 * 联系信息类型
	 */
	@Schema(description = "联系信息类型")
	private String contactType;

	/**
	 * 联系编号
	 */
	@Schema(description = "联系编号")
	private String contactPhone;

	/**
	 * 标题
	 */
	@Schema(description = "标题")
	private String title;
	/**
	 * 内容
	 */
	@Schema(description = "内容")
	private String content;
	/**
	 * 图片
	 */
	@Schema(description = "图片")
	private String images;
	/**
	 * 发布地址
	 */
	@Schema(description = "发布地址")
	private String address;


	/**
	 * 发布时间
	 */
	@Schema(description = "发布时间")
	private String publishTime;


	/**
	 * 发布状态
	 */
	@Schema(description = "发布状态")
	private String publishStatus;

	/**
	 * 时效状态
	 */
	@Schema(description = "时效状态")
	private String timeStatus;

	/**
	 * 审核状态
	 */
	@Schema(description = "审核状态")
	private String auditStatus;
	/**
	 * 地理位置
	 */
	@Schema(description = "地理位置")
	@TableField(typeHandler = JacksonTypeHandler.class)
	private String geoLocation;

	/**
	 * 标签
	 */
	@Schema(description = "标签")
	@TableField(typeHandler = ListToStringTypeHandler.class)
	private List<String> tags;


	private String contactName;

	@Schema(description = "是否置顶")
	private String top;


	@Schema(description = "是否已完成")
	private Integer completed;

	/**
	 * 分类ID
	 */
	@Schema(description = "分类ID")
	private Long categoryId;

	/**
	 * 审核备注
	 */
	@Schema(description = "审核备注")
	private String auditRemark;

	/**
	 * 审核时间
	 */
	@Schema(description = "审核时间")
	private LocalDateTime auditTime;

	/**
	 * 审核人ID
	 */
	@Schema(description = "审核人ID")
	private Long auditUserId;

	/**
	 * 是否启用审核：0-否，1-是
	 */
	@Schema(description = "是否启用审核")
	private Integer enableAudit;

	/**
	 * 是否匿名
	 */
	@Schema(description = "是否匿名")
	private String isAnonymity = "0";

	/**
	 * 帖子类型
	 */
	@Schema(description = "帖子类型")
	private String businessType;

	/**
	 * 机构id
	 */
	@Schema(description = "机构id")
	private Long institutionId;

}
