package org.springblade.business.post.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 帖子统计数据信息
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/24
 */@Data
@Schema(description = "反馈统计信息")
public class FeedBackStatsDTO {

	private Boolean isLiked;  // 是否点赞
	/**
	 * 点赞数
	 */
	@Schema(description = "点赞数")
	private Integer likeCount;

}
