/**
 * Copyright (c) 2018-2099, <PERSON>ll <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.painpoint.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@TableName("urb_pain_point")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "PainPoint对象")
public class PainPoint extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 反馈内容
     */
    @Schema(description = "反馈内容")
    private String content;
    /**
     * 图片路径或链接
     */
    @Schema(description = "图片路径或链接")
    private String image;
    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactInfo;


	@Schema(description = "处理状态")
	private String auditStatus;

	@Schema(description = "处理结果")
	private String auditResult;

	@Schema(description = "反馈人姓名")
	@TableField(exist = false)
	private String nickname;
}
