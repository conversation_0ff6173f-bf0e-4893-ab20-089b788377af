/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.user.entity.Contact;
import org.springblade.business.user.vo.ContactVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 联系人 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ContactMapper extends BaseMapper<Contact> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contact
	 * @return
	 */
	List<ContactVO> selectContactPage(IPage page, ContactVO contact);

	/**
	 * 获取用户联系方式
	 *
	 * @param userId 用户ID
	 * @return Contact
	 */
	Contact getUserContact(@Param("userId") Long userId);


	/**
	 * 插入用户联系人关联
	 *
	 * @param userId    用户ID
	 * @param contactId 联系人ID
	 * @return int
	 */
	int insertUserContact(@Param("userId") Long userId, @Param("contactId") Long contactId);
}
