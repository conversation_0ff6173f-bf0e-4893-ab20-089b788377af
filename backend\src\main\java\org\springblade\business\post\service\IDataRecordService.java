package org.springblade.business.post.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Map;

/**
 * 数据记录 服务类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface IDataRecordService {

	/**
	 * 分页查询浏览记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @param type 类型
	 * @return 浏览记录分页数据
	 */
	IPage<Map<String, Object>> getViewRecords(Page<Map<String, Object>> page, Long relevancyId, String type);

	/**
	 * 分页查询点赞记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @param type 类型
	 * @return 点赞记录分页数据
	 */
	IPage<Map<String, Object>> getLikeRecords(Page<Map<String, Object>> page, Long relevancyId, String type);

	/**
	 * 分页查询反馈记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @return 反馈记录分页数据
	 */
	IPage<Map<String, Object>> getFeedbackRecords(Page<Map<String, Object>> page, Long relevancyId);

	/**
	 * 分页查询收藏记录
	 *
	 * @param page 分页参数
	 * @param relevancyId 关联ID
	 * @param type 类型
	 * @return 收藏记录分页数据
	 */
	IPage<Map<String, Object>> getFavoriteRecords(Page<Map<String, Object>> page, Long relevancyId, String type);

}
