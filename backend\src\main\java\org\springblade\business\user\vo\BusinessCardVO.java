/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.vo;

import org.springblade.business.user.dto.cardStatsDTO;
import org.springblade.business.user.entity.BusinessCard;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.math.BigDecimal;

/**
 * 名片信息表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "名片信息表")
public class BusinessCardVO extends BusinessCard {
	@Serial
	private static final long serialVersionUID = 1L;
	/**
	 * 昵称
	 */
	@Schema(description = "昵称")
	private String appNickname;
	/**
	 * 手机号
	 */
	@Schema(description = "手机号")
	private String appMobile;

	@Schema(description = "浏览量")
	private Integer viewCount;

	/**
	 * 查询范围
	 */
	 @Schema(description = "查询范围")
	private Integer scope;

	 @Schema(description = "名片统计信息")
	private cardStatsDTO stats;

	 @Schema(description = "距离")
	private BigDecimal distance;


	 @Schema(description = "收藏标签")
	private String category;

	/**
	 * 关键词搜索
	 */
	@Schema(description = "关键词搜索")
	private String searchKeyWord;

}
