package org.springblade.business.post.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.business.post.entity.*;

import java.math.BigDecimal;

/**
 * 百事通信息贴
 * @TableName urb_post
 */
@Data
public class PostCreateRequest   {


	/**
	 * 图片
	 */
	private String images;

	/**
	 * 标签
	 */
	private String tags;

	/**
	 * 联系人
	 */
	private String contactName;

	/**
	 * 联系方式类型
	 */
	private String contactType;

	/**
	 * 联系电话
	 */
	private String contactNumber;


	/**
	 * 内容
	 */
	private String content;

	/**
	 * 分类id
	 */
	private Long categoryId;


	/**
	 * 地址
	 */
	private String location;

	/**
	 * 详细地址
	 */
	private String address;


	/**
	 * 经度
	 */
	private BigDecimal longitude;

	/**
	 * 纬度
	 */
	private BigDecimal latitude;


	/**
	 * 是否匿名
	 */
	private String  isAnonymity = "0";

	/**
	 * 帖子类型
	 */
	@Schema(description = "帖子类型")
	private String businessType;

	/**
	 * 顺风车帖子信息
	 */
	private PostCarpool carpool;

	/**
	 * 招聘帖子信息
	 */
	private JobOffer jobOffer;

	/**
	 * 求职帖子信息
	 */
	private JobSeeking jobSeek;

	/**
	 * 租房帖子信息
	 */
	private RentHouse rentHouse;

	/**
	 * 二手车帖子信息
	 */
	private UsedCar usedCar;

	/**
	 * 机构id
	 */
	private Long institutionId;
}
