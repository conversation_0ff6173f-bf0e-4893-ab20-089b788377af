/**
 * Copyright (c) 2018-2099, Chill <PERSON>ang 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

/**
 * 用户积分服务接口
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface IUserPointsService {

    /**
     * 给用户增加积分
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @param reason 积分原因
     * @return 是否成功
     */
    boolean addPoints(Long userId, Integer points, String reason);

    /**
     * 扣除用户积分
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @param reason 扣除原因
     * @return 是否成功
     */
    boolean deductPoints(Long userId, Integer points, String reason);

    /**
     * 获取用户积分
     *
     * @param userId 用户ID
     * @return 用户积分
     */
    Integer getUserPoints(Long userId);
}
