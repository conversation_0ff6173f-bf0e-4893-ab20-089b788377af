/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.wrapper;

import org.springblade.business.points.entity.PointsGoods;
import org.springblade.business.points.vo.PointsGoodsVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 积分商城商品表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public class PointsGoodsWrapper extends BaseEntityWrapper<PointsGoods, PointsGoodsVO>  {

    public static PointsGoodsWrapper build() {
        return new PointsGoodsWrapper();
    }

	@Override
	public PointsGoodsVO entityVO(PointsGoods pointsGoods) {
		PointsGoodsVO pointsGoodsVO = BeanUtil.copyProperties(pointsGoods, PointsGoodsVO.class);

		return pointsGoodsVO;
	}

}
