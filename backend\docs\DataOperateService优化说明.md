# DataOperateService 优化说明

## 概述

本次优化主要针对 `WeChatDataOperateService` 进行了重构，使其支持切换状态操作（如点赞/取消点赞、收藏/取消收藏），提供了更完善的API接口和更好的用户体验。

## 主要改进

### 1. 支持状态切换
- **点赞切换**：用户点击点赞按钮时，如果已点赞则取消，未点赞则添加
- **收藏切换**：用户点击收藏按钮时，如果已收藏则取消，未收藏则添加
- **返回状态**：接口返回操作后的实际状态，便于前端更新UI

### 2. 新增状态检查接口
- `isLiked(Long id, String type)` - 检查是否已点赞
- `isFavorited(Long id, String type)` - 检查是否已收藏

### 3. 完善的数据库操作
- 新增检查存在性的查询方法
- 新增删除操作方法
- 保持向后兼容的插入方法

### 4. 标准化响应格式
- 创建了 `DataOperateResultDTO` 统一响应格式
- 包含操作状态、消息、动作类型等信息

## API 接口

### 新接口（推荐使用）

#### 1. 切换点赞状态
```http
POST /blade-chat/data-operate/toggle-like/{type}/{id}
```

**响应示例：**
```json
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "currentState": true,
    "action": "like",
    "message": "点赞成功",
    "targetId": 123,
    "type": "post"
  }
}
```

#### 2. 切换收藏状态
```http
POST /blade-chat/data-operate/toggle-favorite/{type}/{id}
```

#### 3. 检查点赞状态
```http
GET /blade-chat/data-operate/check-like/{type}/{id}
```

#### 4. 检查收藏状态
```http
GET /blade-chat/data-operate/check-favorite/{type}/{id}
```

### 兼容接口（已标记为废弃）

原有的接口仍然可用，但建议迁移到新接口：
- `POST /blade-chat/post/like/{type}/{id}`
- `POST /blade-chat/post/favorite/{type}/{id}`

## 数据库变更

### 新增 SQL 操作

```xml
<!-- 检查点赞是否存在 -->
<select id="checkLikeExists" resultType="int">
    SELECT COUNT(1) FROM urb_like 
    WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
</select>

<!-- 删除点赞 -->
<delete id="deleteLike">
    DELETE FROM urb_like 
    WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
</delete>

<!-- 检查收藏是否存在 -->
<select id="checkFavoriteExists" resultType="int">
    SELECT COUNT(1) FROM urb_favorite 
    WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
</select>

<!-- 删除收藏 -->
<delete id="deleteFavorite">
    DELETE FROM urb_favorite 
    WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
</delete>
```

## 使用示例

### 前端调用示例

```javascript
// 切换点赞状态
async function toggleLike(id, type) {
    try {
        const response = await fetch(`/blade-chat/data-operate/toggle-like/${type}/${id}`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            const { currentState, message } = result.data;
            // 更新UI状态
            updateLikeButton(currentState);
            showMessage(message);
        }
    } catch (error) {
        console.error('操作失败:', error);
    }
}

// 检查初始状态
async function checkLikeStatus(id, type) {
    try {
        const response = await fetch(`/blade-chat/data-operate/check-like/${type}/${id}`);
        const result = await response.json();
        
        if (result.success) {
            const { isLiked } = result.data;
            updateLikeButton(isLiked);
        }
    } catch (error) {
        console.error('检查状态失败:', error);
    }
}
```

## 事务处理

所有的切换操作都使用了 `@Transactional` 注解，确保数据一致性：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean toggleLike(Long id, String type) {
    // 原子性操作，要么全部成功，要么全部回滚
}
```

## 日志记录

增加了详细的日志记录，便于调试和监控：

```java
log.debug("用户 {} 点赞 {} ID: {}, 结果: {}", userId, type, id, result > 0);
log.debug("用户 {} 取消点赞 {} ID: {}, 结果: {}", userId, type, id, result > 0);
```

## 向后兼容性

- 保留了原有的接口和方法，标记为 `@Deprecated`
- 原有的调用方式仍然可用，但会调用新的切换逻辑
- 建议逐步迁移到新的接口

## 性能优化

1. **减少数据库查询**：切换操作只需要一次检查 + 一次操作
2. **索引优化**：建议在相关表上添加复合索引
3. **缓存支持**：可以结合 Redis 缓存用户的点赞/收藏状态

## 建议的数据库索引

```sql
-- 点赞表索引
CREATE INDEX idx_urb_like_user_relevancy_type ON urb_like(user_id, relevancy_id, type);

-- 收藏表索引  
CREATE INDEX idx_urb_favorite_user_relevancy_type ON urb_favorite(user_id, relevancy_id, type);
```

## 总结

本次优化提供了更完善的数据操作服务，支持状态切换、状态检查等功能，提升了用户体验和开发效率。同时保持了向后兼容性，可以平滑升级。
