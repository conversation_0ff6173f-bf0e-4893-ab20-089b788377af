/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.MapKey;
import org.springblade.business.statistics.entity.UserRequestStats;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.vo.WeUserVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface WeUserMapper extends BaseMapper<WeUser> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param user
	 * @return
	 */
	List<WeUserVO> selectUserPage(IPage page, WeUserVO user);

	/**
	 * 获取用户统计数据
	 *
	 * @param userId 用户ID
	 * @return Map 包含帖子数、点赞数、收藏数
	 */
	Map<String, Object> getUserStats(@Param("userId") Long userId);

    List<Map<String, Object>> getUserActiveCount(String startDate, String endDate);

	List<UserRequestStats> getUserRequestStatsTop20(String date);
}
