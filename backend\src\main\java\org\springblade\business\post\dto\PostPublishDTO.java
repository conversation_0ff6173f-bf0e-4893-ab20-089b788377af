/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springblade.business.post.entity.PostCarpool;

import java.util.List;

/**
 * 小程序发布帖子DTO
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@Schema(description = "小程序发布帖子DTO")
public class PostPublishDTO {

	/**
	 * 标题
	 */
	@NotBlank(message = "标题不能为空")
	@Schema(description = "标题")
	private String title;

	/**
	 * 内容
	 */
	@NotBlank(message = "内容不能为空")
	@Schema(description = "内容")
	private String content;

	/**
	 * 图片列表
	 */
	@Schema(description = "图片列表")
	private List<String> images;

	/**
	 * 发布地址
	 */
	@Schema(description = "发布地址")
	private String address;

	/**
	 * 地理位置
	 */
	@Schema(description = "地理位置")
	private String geoLocation;

	/**
	 * 标签列表
	 */
	@Schema(description = "标签列表")
	private List<String> tags;

	/**
	 * 联系人姓名
	 */
	@Schema(description = "联系人姓名")
	private String contactName;

	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	private String contactPhone;

	/**
	 * 分类ID
	 */
	@NotNull(message = "分类不能为空")
	@Schema(description = "分类ID")
	private Long categoryId;

	/**
	 * 小程序用户OpenID
	 */
	@NotBlank(message = "用户OpenID不能为空")
	@Schema(description = "小程序用户OpenID")
	private String openId;

	/**
	 * 用户昵称
	 */
	@Schema(description = "用户昵称")
	private String nickName;

	/**
	 * 用户头像
	 */
	@Schema(description = "用户头像")
	private String avatarUrl;

	/**
	 * 是否为匿名
	 */
	@Schema(description = "是否为匿名")
	private String isAnonymity;

	private PostCarpool carpool;
}
