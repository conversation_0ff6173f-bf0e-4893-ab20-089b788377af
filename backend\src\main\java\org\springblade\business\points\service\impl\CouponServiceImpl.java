/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.points.service.impl;

import jakarta.annotation.Resource;
import org.springblade.business.points.entity.Coupon;
import org.springblade.business.points.mapper.CouponMapper;
import org.springblade.business.points.service.ICouponService;
import org.springblade.business.points.entity.PointsRecord;
import org.springblade.business.points.mapper.PointsRecordMapper;
import org.springblade.business.points.entity.PointsExchange;
import org.springblade.business.points.mapper.PointsExchangeMapper;
import org.springblade.business.points.entity.PointsGoods;
import org.springblade.business.points.mapper.PointsGoodsMapper;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.mapper.WeUserMapper;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * 用户优惠券表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class CouponServiceImpl extends BaseServiceImpl<CouponMapper, Coupon> implements ICouponService {

	@Resource
	private PointsGoodsMapper pointsGoodsMapper;
	@Resource
	private WeUserMapper weUserMapper;
	@Resource
	private CouponMapper couponMapper;
	@Resource
	private PointsExchangeMapper pointsExchangeMapper;
	@Resource
	private PointsRecordMapper pointsRecordMapper;

	@Override
	public IPage<Coupon> pageCoupon(Coupon coupon, Query query) {
		IPage<Coupon> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(coupon));
		//给page补充字段
		pages.getRecords().forEach(item -> {
			PointsGoods pointsGoods = pointsGoodsMapper.getById(item.getGoodsId());
			item.setPointsGoods(pointsGoods);
		});
		return pages;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean exchange(Coupon coupon) {
		//1.先获取当前用户
		Long userId = AuthUtil.getUserId();
		//2.校验用户
		WeUser weuser = weUserMapper.selectById(userId);
		if (weuser == null) {
			throw new SecureException("请先登录");
		}
		//2.查询商品
		PointsGoods pointsGoods = pointsGoodsMapper.getById(coupon.getGoodsId());
		if (pointsGoods == null) {
			throw new ServiceException("商品不存在");
		}
		//3.校验库存
		if (pointsGoods.getStock() <= 0) {
			throw new ServiceException("商品已售罄");
		}
		//4.查询用户积分
		WeUser user = weUserMapper.selectById(userId);
		Integer beforeBalance = user.getBalance();
		if (user.getBalance() < pointsGoods.getPointsPrice()) {
			throw new ServiceException("积分不足，当前可用积分：" + user.getBalance());
		}
		//5.扣除库存
		pointsGoods.setStock(pointsGoods.getStock() - 1);
		pointsGoodsMapper.updateById(pointsGoods);
		//6.扣除用户积分
		user.setBalance(user.getBalance() - pointsGoods.getPointsPrice());
		weUserMapper.updateById(user);
		//7.记录积分变动表
		PointsRecord pointsRecord = new PointsRecord();
		pointsRecord.setUserId(String.valueOf(userId));
		pointsRecord.setPoints(-pointsGoods.getPointsPrice());
		pointsRecord.setBeforePoints(beforeBalance);
		pointsRecord.setAfterPoints(user.getBalance());
		pointsRecord.setType("1");
		pointsRecord.setTypeName("兑换");
		pointsRecord.setDescription("兑换积分商品 :" + pointsGoods.getGoodsName());
		pointsRecordMapper.insert(pointsRecord);
		//8.记录积分商品兑换记录
		PointsExchange pointsExchange = new PointsExchange();
		pointsExchange.setGoodsId(pointsGoods.getId());
		pointsExchange.setGoodsName(pointsGoods.getGoodsName());
		pointsExchange.setQuantity(1);
		pointsExchange.setPointsCost(pointsGoods.getPointsPrice());
		pointsExchangeMapper.insert(pointsExchange);

		//生成优惠卷
		Coupon newcoupon = new Coupon();
		newcoupon.setUserId(userId);
		newcoupon.setGoodsId(pointsGoods.getId());
		newcoupon.setExchangeId(pointsExchange.getId()); // 关联兑换记录ID
		newcoupon.setCouponCode(generateCouponCode()); // 生成唯一优惠券码
		// 设置有效期（默认30天）
		LocalDateTime now = LocalDateTime.now();
		newcoupon.setValidFrom(now);
		newcoupon.setValidTo(now.plusDays(30));
		// 无需设置pointsGoods（非存储字段）
		couponMapper.insert(newcoupon);

		return true;
	}
	/**
	 * 生成唯一优惠券码（8位字母数字组合）
	 */
	private String generateCouponCode() {
		String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
		StringBuilder code = new StringBuilder();
		Random random = new Random();
		for (int i = 0; i < 8; i++) {
			int index = random.nextInt(base.length());
			code.append(base.charAt(index));
		}
		return code.toString();
	}
}
