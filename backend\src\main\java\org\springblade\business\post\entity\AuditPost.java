/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审核日志实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_audit_post")
@Schema(description = "审核日志")
public class AuditPost implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	@TableId
	private Long id;
	/**
	 * 信息贴ID
	 */
	@Schema(description = "信息贴ID")
	private Long postId;
	/**
	 * 审核时间
	 */
	@Schema(description = "审核时间")
	private LocalDateTime auditTime;
	/**
	 * 审核状态
	 */
	@Schema(description = "审核状态")
	private String auditStatus;
	/**
	 * 审核人ID
	 */
	@Schema(description = "审核人ID")
	private Long auditUser;
	/**
	 * 审核备注
	 */
	@Schema(description = "审核备注")
	private String auditRemark;


}
