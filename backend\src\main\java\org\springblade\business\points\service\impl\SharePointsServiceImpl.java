package org.springblade.business.points.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.points.entity.PointsRecord;
import org.springblade.business.points.mapper.PointsRecordMapper;
import org.springblade.business.points.service.IPointsRecordService;
import org.springblade.business.points.service.ISharePointsService;
import org.springblade.business.points.vo.PointsRecordVO;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.core.cache.utils.CacheUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 分享积分服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/3
 */
@Slf4j
@Service
@AllArgsConstructor
public class SharePointsServiceImpl implements ISharePointsService {

    private final IPointsRecordService pointsRecordService;
    private final IWeUserService weUserService;
    private final PointsRecordMapper pointsRecordMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    // 分享积分配置
    private static final Map<String, Integer> SHARE_POINTS_CONFIG = Map.of(
        "post", 5,           // 帖子分享获得5积分
        "institution", 3,    // 机构分享获得3积分
        "qrcode", 2          // 二维码分享获得2积分
    );

    // 每日分享次数限制
    private static final Map<String, Integer> DAILY_SHARE_LIMIT = Map.of(
        "post", 10,          // 帖子每日最多分享10次获得积分
        "institution", 5,    // 机构每日最多分享5次获得积分
        "qrcode", 20         // 二维码每日最多分享20次获得积分
    );

    // 缓存名称
    private static final String SHARE_CACHE = "blade:share";

    // 缓存key前缀
    private static final String SHARE_COUNT_KEY_PREFIX = "count:";
    private static final String SHARE_RECORD_KEY_PREFIX = "record:";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> shareForPoints(String userId, String shareType, Long businessId, String shareChannel) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 检查是否可以分享获得积分
            if (!canShareForPoints(userId, shareType, businessId)) {
                result.put("success", false);
                result.put("message", "今日该类型分享次数已达上限或重复分享同一内容");
                result.put("points", 0);
                return result;
            }

            // 2. 获取分享积分
            Integer sharePoints = SHARE_POINTS_CONFIG.getOrDefault(shareType, 0);
            if (sharePoints <= 0) {
                result.put("success", false);
                result.put("message", "该分享类型暂不支持积分奖励");
                result.put("points", 0);
                return result;
            }

            // 3. 获取用户信息
            WeUser user = weUserService.getById(userId);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                result.put("points", 0);
                return result;
            }

            // 4. 更新用户积分
            Integer currentPoints = user.getBalance() != null ? user.getBalance() : 0;
            Integer newPoints = currentPoints + sharePoints;
            user.setBalance(newPoints);
            weUserService.updateById(user);

            // 5. 记录积分变动
            PointsRecord pointsRecord = new PointsRecord();
            pointsRecord.setUserId(userId);
            pointsRecord.setPoints(sharePoints);
            pointsRecord.setBeforePoints(currentPoints);
            pointsRecord.setAfterPoints(newPoints);
            pointsRecord.setType("2"); // 分享类型
            pointsRecord.setTypeName("分享奖励");
            pointsRecord.setBusinessId(businessId);
            pointsRecord.setBusinessType(shareType);
            pointsRecord.setDescription(getShareDescription(shareType, shareChannel));
            pointsRecord.setRemark("分享渠道: " + shareChannel);
            pointsRecord.setOperateTime(LocalDateTime.now());
            pointsRecord.setOperator(user.getId());
            pointsRecordService.save(pointsRecord);

            // 6. 更新缓存计数
            updateShareCount(userId, shareType);
            recordShareBusiness(userId, shareType, businessId);

            // 7. 记录分享行为
            recordShareAction(userId, shareType, businessId, shareChannel);

            result.put("success", true);
            result.put("message", "分享成功，获得" + sharePoints + "积分");
            result.put("points", sharePoints);
            result.put("totalPoints", newPoints);
            result.put("shareType", shareType);
            result.put("shareChannel", shareChannel);

            log.info("用户分享获得积分成功: userId={}, shareType={}, businessId={}, points={}",
                userId, shareType, businessId, sharePoints);

        } catch (Exception e) {
            log.error("用户分享获得积分失败: userId={}, shareType={}, businessId={}",
                userId, shareType, businessId, e);
            result.put("success", false);
            result.put("message", "分享失败: " + e.getMessage());
            result.put("points", 0);
        }

        return result;
    }

    @Override
    public int getTodayShareCount(String userId, String shareType) {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String cacheKey = userId + ":" + shareType + ":" + today;

        try {
            Integer count = CacheUtil.get(SHARE_CACHE, SHARE_COUNT_KEY_PREFIX, cacheKey, Integer.class);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取今日分享次数失败, userId: {}, shareType: {}, error: {}", userId, shareType, e.getMessage());
            // 发生异常时，清除可能损坏的缓存数据
            try {
                CacheUtil.evict(SHARE_CACHE, SHARE_COUNT_KEY_PREFIX, cacheKey);
            } catch (Exception deleteEx) {
                log.warn("清除损坏的缓存数据失败: {}", deleteEx.getMessage());
            }
            return 0;
        }
    }

    @Override
    public boolean canShareForPoints(String userId, String shareType, Long businessId) {
        try {
            // 1. 检查今日分享次数是否达到上限
            int todayCount = getTodayShareCount(userId, shareType);
            int dailyLimit = DAILY_SHARE_LIMIT.getOrDefault(shareType, 0);

            if (todayCount >= dailyLimit) {
                log.debug("用户今日分享次数已达上限: userId={}, shareType={}, todayCount={}, dailyLimit={}",
                    userId, shareType, todayCount, dailyLimit);
                return false;
            }

            // 2. 检查是否重复分享同一内容（同一天内）
            if (businessId != null) {
                String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String recordKey = userId + ":" + shareType + ":" + businessId + ":" + today;

                try {
                    String record = CacheUtil.get(SHARE_CACHE, SHARE_RECORD_KEY_PREFIX, recordKey, String.class);
                    if (record != null) {
                        log.debug("用户今日已分享过该内容: userId={}, shareType={}, businessId={}",
                            userId, shareType, businessId);
                        return false;
                    }
                } catch (Exception e) {
                    log.warn("检查重复分享记录失败: userId={}, shareType={}, businessId={}, error={}",
                        userId, shareType, businessId, e.getMessage());
                    // 如果缓存检查失败，为了安全起见，允许分享
                }
            }

            return true;
        } catch (Exception e) {
            log.error("检查分享权限失败: userId={}, shareType={}, businessId={}, error={}",
                userId, shareType, businessId, e.getMessage());
            // 发生异常时，为了不影响用户体验，允许分享
            return true;
        }
    }

    @Override
    public IPage<PointsRecordVO> getSharePointsRecords(IPage<PointsRecord> page, String userId, String shareType) {
        LambdaQueryWrapper<PointsRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PointsRecord::getUserId, userId)
                   .eq(PointsRecord::getType, "2") // 分享类型
                   .eq(shareType != null, PointsRecord::getBusinessType, shareType)
                   .orderByDesc(PointsRecord::getOperateTime);

        IPage<PointsRecord> recordPage = pointsRecordService.page(page, queryWrapper);

        // 转换为VO
        IPage<PointsRecordVO> voPage = page.convert(record -> {
            PointsRecordVO vo = new PointsRecordVO();
            vo.setId(record.getId());
            vo.setUserId(record.getUserId());
            vo.setPoints(record.getPoints());
            vo.setBeforePoints(record.getBeforePoints());
            vo.setAfterPoints(record.getAfterPoints());
            vo.setType(record.getType());
            vo.setTypeName(record.getTypeName());
            vo.setBusinessId(record.getBusinessId());
            vo.setBusinessType(record.getBusinessType());
            vo.setDescription(record.getDescription());
            vo.setRemark(record.getRemark());
            vo.setOperateTime(record.getOperateTime());
            vo.setOperator(record.getOperator());
            vo.setCreateTime(record.getCreateTime());
            return vo;
        });

        return voPage;
    }

    @Override
    public Map<String, Object> getSharePointsStats(String userId) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 今日分享统计
            Map<String, Object> todayStats = new HashMap<>();
            for (String shareType : SHARE_POINTS_CONFIG.keySet()) {
                int todayCount = getTodayShareCount(userId, shareType);
                int dailyLimit = DAILY_SHARE_LIMIT.getOrDefault(shareType, 0);

                Map<String, Object> typeStats = new HashMap<>();
                typeStats.put("count", todayCount);
                typeStats.put("limit", dailyLimit);
                typeStats.put("remaining", Math.max(0, dailyLimit - todayCount));
                typeStats.put("points", SHARE_POINTS_CONFIG.get(shareType));

                todayStats.put(shareType, typeStats);
            }
            stats.put("today", todayStats);

            // 总分享积分统计
            LambdaQueryWrapper<PointsRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PointsRecord::getUserId, userId)
                       .eq(PointsRecord::getType, "2"); // 分享类型

            // 总分享获得积分
            Integer totalSharePoints = pointsRecordMapper.selectList(queryWrapper)
                .stream()
                .mapToInt(PointsRecord::getPoints)
                .sum();

            stats.put("totalSharePoints", totalSharePoints);
            stats.put("totalShareCount", pointsRecordMapper.selectCount(queryWrapper));

            // 本月分享统计
            LocalDateTime monthStart = LocalDate.now().withDayOfMonth(1).atStartOfDay();
            queryWrapper.ge(PointsRecord::getOperateTime, monthStart);

            Integer monthSharePoints = pointsRecordMapper.selectList(queryWrapper)
                .stream()
                .mapToInt(PointsRecord::getPoints)
                .sum();

            stats.put("monthSharePoints", monthSharePoints);
            stats.put("monthShareCount", pointsRecordMapper.selectCount(queryWrapper));

        } catch (Exception e) {
            log.error("获取分享积分统计失败: userId={}", userId, e);
            stats.put("error", "获取统计信息失败");
        }

        return stats;
    }

    @Override
    public Map<String, Object> getSharePointsConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("sharePoints", SHARE_POINTS_CONFIG);
        config.put("dailyLimit", DAILY_SHARE_LIMIT);
        config.put("shareTypes", Map.of(
            "post", "帖子分享",
            "institution", "机构分享",
            "qrcode", "二维码分享"
        ));
        config.put("shareChannels", Map.of(
            "wechat", "微信好友",
            "moments", "朋友圈",
            "qq", "QQ好友",
            "qzone", "QQ空间",
            "weibo", "微博",
            "copy", "复制链接"
        ));
        return config;
    }

    @Override
    public boolean recordShareAction(String userId, String shareType, Long businessId, String shareChannel) {
        try {
            // 这里可以记录到专门的分享行为表，用于数据分析
            // 暂时使用日志记录
            log.info("记录分享行为: userId={}, shareType={}, businessId={}, shareChannel={}",
                userId, shareType, businessId, shareChannel);
            return true;
        } catch (Exception e) {
            log.error("记录分享行为失败: userId={}, shareType={}, businessId={}",
                userId, shareType, businessId, e);
            return false;
        }
    }

    /**
     * 更新分享计数缓存
     */
    private void updateShareCount(String userId, String shareType) {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String cacheKey = userId + ":" + shareType + ":" + today;

        try {
            // 获取当前计数
            Integer currentCount = CacheUtil.get(SHARE_CACHE, SHARE_COUNT_KEY_PREFIX, cacheKey, Integer.class);
            int newCount = (currentCount != null ? currentCount : 0) + 1;

            // 更新计数
            CacheUtil.put(SHARE_CACHE, SHARE_COUNT_KEY_PREFIX, cacheKey, newCount);

            log.debug("更新分享计数成功: userId={}, shareType={}, count={}", userId, shareType, newCount);
        } catch (Exception e) {
            log.error("更新分享计数失败: userId={}, shareType={}, error={}", userId, shareType, e.getMessage());
            // 如果更新失败，尝试直接设置为1
            try {
                CacheUtil.put(SHARE_CACHE, SHARE_COUNT_KEY_PREFIX, cacheKey, 1);
            } catch (Exception setEx) {
                log.error("设置分享计数失败: userId={}, shareType={}, error={}", userId, shareType, setEx.getMessage());
            }
        }
    }

    /**
     * 记录分享业务缓存
     */
    private void recordShareBusiness(String userId, String shareType, Long businessId) {
        if (businessId != null) {
            try {
                String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String recordKey = userId + ":" + shareType + ":" + businessId + ":" + today;

                CacheUtil.put(SHARE_CACHE, SHARE_RECORD_KEY_PREFIX, recordKey, "1");
                log.debug("记录分享业务缓存成功: userId={}, shareType={}, businessId={}", userId, shareType, businessId);
            } catch (Exception e) {
                log.error("记录分享业务缓存失败: userId={}, shareType={}, businessId={}, error={}",
                    userId, shareType, businessId, e.getMessage());
            }
        }
    }

    /**
     * 清理用户的分享缓存数据
     */
    public void clearUserShareCache(String userId) {
        try {
            // 由于CacheUtil不支持模式匹配删除，我们清理整个分享缓存
            // 在生产环境中，可以考虑实现更精确的清理逻辑
            log.warn("清理用户分享缓存: userId={}, 注意：将清理整个分享缓存", userId);

            // 可以选择清理整个缓存或者保留Redis操作
            // 这里保留Redis操作以支持精确清理
            String pattern = SHARE_COUNT_KEY_PREFIX + userId + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清理用户分享计数缓存成功: userId={}, 清理数量={}", userId, keys.size());
            }

            String recordPattern = SHARE_RECORD_KEY_PREFIX + userId + ":*";
            Set<String> recordKeys = redisTemplate.keys(recordPattern);
            if (recordKeys != null && !recordKeys.isEmpty()) {
                redisTemplate.delete(recordKeys);
                log.info("清理用户分享记录缓存成功: userId={}, 清理数量={}", userId, recordKeys.size());
            }
        } catch (Exception e) {
            log.error("清理用户分享缓存失败: userId={}, error={}", userId, e.getMessage());
        }
    }

    /**
     * 清理所有损坏的分享缓存数据
     */
    public void clearCorruptedShareCache() {
        try {
            // 使用CacheUtil清理整个分享缓存
            CacheUtil.clear(SHARE_CACHE);
            log.info("清理分享缓存完成，已清理整个缓存: {}", SHARE_CACHE);
        } catch (Exception e) {
            log.error("清理分享缓存失败: error={}", e.getMessage());
            // 如果CacheUtil清理失败，尝试使用Redis直接清理
            try {
                Set<String> countKeys = redisTemplate.keys(SHARE_COUNT_KEY_PREFIX + "*");
                if (countKeys != null && !countKeys.isEmpty()) {
                    redisTemplate.delete(countKeys);
                    log.info("使用Redis清理分享计数缓存: 清理数量={}", countKeys.size());
                }

                Set<String> recordKeys = redisTemplate.keys(SHARE_RECORD_KEY_PREFIX + "*");
                if (recordKeys != null && !recordKeys.isEmpty()) {
                    redisTemplate.delete(recordKeys);
                    log.info("使用Redis清理分享记录缓存: 清理数量={}", recordKeys.size());
                }
            } catch (Exception redisEx) {
                log.error("使用Redis清理缓存也失败: error={}", redisEx.getMessage());
            }
        }
    }

    /**
     * 获取分享描述
     */
    private String getShareDescription(String shareType, String shareChannel) {
        String typeDesc = switch (shareType) {
            case "post" -> "帖子";
            case "institution" -> "机构";
            case "qrcode" -> "二维码";
            default -> "内容";
        };

        String channelDesc = switch (shareChannel) {
            case "wechat" -> "微信好友";
            case "moments" -> "朋友圈";
            case "qq" -> "QQ好友";
            case "qzone" -> "QQ空间";
            case "weibo" -> "微博";
            case "copy" -> "复制链接";
            default -> shareChannel;
        };

        return "分享" + typeDesc + "到" + channelDesc;
    }
}
