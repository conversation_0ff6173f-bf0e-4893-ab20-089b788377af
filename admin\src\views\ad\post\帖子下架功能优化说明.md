# 帖子下架功能优化说明

## 功能概述
为帖子管理系统添加下架功能支持，管理员可以通过状态变更来控制帖子的显示与隐藏。

## 修改内容

### 1. 前端管理页面优化

#### 文件：`admin/src/views/ad/post/post.vue`

**新增功能：**
- 添加批量下架按钮
- 添加单个帖子上架/下架操作按钮
- 添加发布状态字段显示和搜索

**主要修改：**

1. **批量操作按钮**
```vue
<el-button type="warning"
           icon="el-icon-download"
           plain
           @click="handleBatchOffline">批量下架
</el-button>
```

2. **状态字段配置**
```javascript
{
  label: "发布状态",
  prop: "publishStatus",
  type: "select",
  dicData: [
    { label: '正常', value: '1' },
    { label: '下架', value: '0' }
  ],
  search: true,
  slot: true
}
```

3. **操作按钮**
```vue
<el-button v-if="row.publishStatus === '1'" 
           type="warning" 
           size="small"
           @click="handleStatusChange(row, '0')">
  下架
</el-button>
<el-button v-if="row.publishStatus === '0'" 
           type="primary" 
           size="small"
           @click="handleStatusChange(row, '1')">
  上架
</el-button>
```

4. **新增方法**
- `handleStatusChange(row, publishStatus)` - 处理单个帖子状态变更
- `handleBatchOffline()` - 批量下架处理
- 更新 `getStatusText()` 和 `getStatusType()` 方法

### 2. 后端查询优化

#### 文件：`backend/src/main/java/org/springblade/miniapp/service/impl/WeChatPostServiceImpl.java`

**修改内容：**
```java
// 修改前
params.put("status", 1);

// 修改后  
params.put("publishStatus", "1"); // 只查询上架的帖子
```

#### 文件：`backend/src/main/java/org/springblade/business/post/service/impl/SupPostServiceImpl.java`

**修改内容：**
```java
LambdaQueryWrapper<SupPost> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(SupPost::getAuditStatus, "APPROVED")
    .eq(SupPost::getPublishStatus, "1") // 只查询上架的帖子
    // ... 其他条件
```

#### 文件：`backend/src/main/java/org/springblade/business/user/mapper/SupPostMapper.xml`

**修改内容：**
1. **管理后台分页查询**
```xml
<select id="selectPostPage" resultMap="postResultMap">
    <!-- ... -->
    WHERE 1 = 1
    <if test="post.publishStatus!=null and post.publishStatus!=''">
        AND p.publish_status = #{post.publishStatus}
    </if>
    <!-- ... -->
</select>
```

2. **小程序端查询**
```xml
<if test="model.publishStatus != null">
    AND p.publish_status = #{model.publishStatus}
</if>
```

## 字段说明

### publishStatus 字段
- **数据类型**: String
- **字段含义**: 帖子发布状态
- **取值范围**:
  - `"1"` - 正常（上架）
  - `"0"` - 下架

### 数据库字段映射
- **实体字段**: `publishStatus`
- **数据库字段**: `publish_status`
- **字段类型**: VARCHAR

## 功能特性

### 1. 管理后台功能
- ✅ **状态显示**: 在帖子列表中显示发布状态
- ✅ **状态搜索**: 支持按发布状态筛选帖子
- ✅ **单个操作**: 支持单个帖子上架/下架
- ✅ **批量操作**: 支持批量下架帖子
- ✅ **状态标识**: 不同状态用不同颜色标签显示

### 2. 小程序端功能
- ✅ **自动过滤**: 分页查询自动过滤下架帖子
- ✅ **首页列表**: 只显示上架状态的帖子
- ✅ **搜索结果**: 搜索结果只包含上架帖子
- ✅ **地图显示**: 地图上只显示上架帖子

### 3. API接口功能
- ✅ **home-list接口**: 自动过滤下架帖子
- ✅ **分页查询**: 支持状态参数过滤
- ✅ **详情查询**: 保持原有逻辑不变

## 操作流程

### 管理员下架帖子
1. 登录管理后台
2. 进入帖子管理页面
3. 选择需要下架的帖子
4. 点击"下架"按钮或使用批量下架
5. 确认操作
6. 帖子状态变更为下架

### 管理员上架帖子
1. 在帖子列表中筛选下架状态
2. 找到需要上架的帖子
3. 点击"上架"按钮
4. 确认操作
5. 帖子状态变更为正常

### 用户端体验
1. 用户在小程序中浏览帖子
2. 只能看到状态为"正常"的帖子
3. 下架的帖子不会在任何列表中显示
4. 搜索结果也不包含下架帖子

## 技术实现

### 前端实现
- 使用 Element UI 的 el-tag 组件显示状态
- 使用 el-button 组件实现操作按钮
- 使用 el-confirm 确认框防止误操作
- 支持批量选择和批量操作

### 后端实现
- 使用 MyBatis-Plus 的 LambdaQueryWrapper 构建查询条件
- 在 Service 层添加状态过滤逻辑
- 在 Mapper XML 中添加动态 SQL 条件
- 保持 API 接口的向后兼容性

### 数据库设计
- 使用 `publish_status` 字段存储发布状态
- 字段类型为 VARCHAR，支持扩展更多状态
- 建议添加数据库索引提高查询性能

## 注意事项

### 1. 数据一致性
- 确保所有查询都添加状态过滤条件
- 避免直接操作数据库绕过状态检查
- 定期检查数据一致性

### 2. 性能优化
- 建议在 `publish_status` 字段上添加索引
- 考虑使用缓存减少数据库查询
- 批量操作时注意事务处理

### 3. 用户体验
- 状态变更后及时刷新列表
- 提供明确的操作反馈
- 支持撤销误操作

### 4. 权限控制
- 确保只有管理员可以执行上架/下架操作
- 记录操作日志便于审计
- 考虑添加操作权限细分

## 扩展建议

### 1. 状态扩展
可以考虑添加更多状态：
- `"2"` - 待审核
- `"3"` - 审核拒绝
- `"4"` - 过期下架

### 2. 操作日志
记录帖子状态变更历史：
- 操作时间
- 操作人员
- 变更原因
- 前后状态

### 3. 定时任务
添加自动下架功能：
- 过期自动下架
- 违规内容自动下架
- 批量状态同步

### 4. 通知机制
状态变更通知：
- 通知帖子发布者
- 管理员操作通知
- 系统状态报告

## 测试建议

### 1. 功能测试
- 测试单个帖子上架/下架
- 测试批量下架功能
- 测试状态筛选功能
- 测试小程序端过滤效果

### 2. 性能测试
- 大量数据下的查询性能
- 批量操作的响应时间
- 并发操作的数据一致性

### 3. 兼容性测试
- 现有功能是否受影响
- API接口向后兼容性
- 不同浏览器的兼容性

## 部署注意事项

### 1. 数据库迁移
- 确保 `publish_status` 字段存在
- 为现有数据设置默认状态
- 添加必要的数据库索引

### 2. 配置更新
- 更新前端路由配置
- 检查API接口配置
- 验证权限配置

### 3. 回滚方案
- 准备数据库回滚脚本
- 保留原有代码版本
- 制定应急处理方案
