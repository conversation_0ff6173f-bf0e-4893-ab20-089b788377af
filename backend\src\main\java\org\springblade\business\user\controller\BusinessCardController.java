/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.common.constant.bizz.PublishStatusEnum;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.business.user.wrapper.BusinessCardWrapper;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 名片信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/businesscard")
@io.swagger.v3.oas.annotations.tags.Tag(name = "名片信息表", description = "名片信息表接口")
public class BusinessCardController extends BladeController {

	private IBusinessCardService businessCardService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入businessCard")
	public R<BusinessCardVO> detail(BusinessCard businessCard) {
		BusinessCard detail = businessCardService.getOne(Condition.getQueryWrapper(businessCard));
		return R.data(BusinessCardWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 名片信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入businessCard")
	public R<IPage<BusinessCardVO>> list(BusinessCard businessCard, Query query) {
		//根据创建时间倒序
		query.setDescs("create_time");
		IPage<BusinessCard> pages = businessCardService.page(Condition.getPage(query), Condition.getQueryWrapper(businessCard));
		return R.data(BusinessCardWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 名片信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入businessCard")
	public R<IPage<BusinessCardVO>> page(BusinessCardVO businessCard, Query query) {
		IPage<BusinessCardVO> pages = businessCardService.selectBusinessCardPage(Condition.getPage(query), businessCard);
		return R.data(pages);
	}

	/**
	 * 新增 名片信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入businessCard")
	public R save(@Valid @RequestBody BusinessCard businessCard) {
		return R.status(businessCardService.save(businessCard));
	}

	/**
	 * 修改 名片信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入businessCard")
	public R update(@Valid @RequestBody BusinessCard businessCard) {
		return R.status(businessCardService.updateById(businessCard));
	}

	/**
	 * 新增或修改 名片信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入businessCard")
	public R submit(@Valid @RequestBody BusinessCard businessCard) {
		return R.status(businessCardService.saveOrUpdate(businessCard));
	}


	/**
	 * 删除 名片信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(businessCardService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 审核名片
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "审核名片", description = "审核名片")
	public R<Boolean> auditBusinessCard(@Parameter(description = "名片ID") @RequestParam Long cardId,
										@Parameter(description = "审核状态（1-通过，2-拒绝）") @RequestParam String auditStatus,
										@Parameter(description = "审核备注") @RequestParam(required = false) String auditRemark) {
		businessCardService.auditBusinessCard(cardId, auditStatus, auditRemark);
		return R.success();
	}

	/**
	 * 批量审核名片
	 */
	@PostMapping("/batch-audit")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "批量审核名片", description = "批量审核名片")
	public R<Boolean> batchAuditBusinessCards(@Parameter(description = "名片ID列表") @RequestParam String cardIds,
											  @Parameter(description = "审核状态（1-通过，2-拒绝）") @RequestParam String auditStatus,
											  @Parameter(description = "审核备注") @RequestParam(required = false) String auditRemark) {
		boolean result = businessCardService.batchAuditBusinessCards(Func.toLongList(cardIds), auditStatus, auditRemark);
		return R.data(result);
	}

	/**
	 * 修改名片发布状态
	 */
	@PostMapping("/{id}/publish-status")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "修改名片发布状态", description = "修改名片发布状态")
	public R<Boolean> updatePublishStatus(@Parameter(description = "名片ID") @PathVariable Long id,
										  @Parameter(description = "发布状态（0-下架，1-上架）") @RequestParam String publishStatus) {
		boolean result = businessCardService.updatePublishStatus(id, publishStatus);
		return R.data(result);
	}

	/**
	 * 批量修改名片发布状态
	 */
	@PostMapping("/batch-publish-status")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "批量修改名片发布状态", description = "批量修改名片发布状态")
	public R<Boolean> batchUpdatePublishStatus(@Parameter(description = "名片ID列表") @RequestParam String cardIds,
											   @Parameter(description = "发布状态（0-下架，1-上架）") @RequestParam String publishStatus) {
		boolean result = businessCardService.batchUpdatePublishStatus(Func.toLongList(cardIds), publishStatus);
		return R.data(result);
	}

	/**
	 * 批量上架名片
	 */
	@PostMapping("/batch-online")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "批量上架名片", description = "批量将名片设置为上架状态")
	public R<Boolean> batchOnlineCards(@Parameter(description = "名片ID列表，逗号分隔") @RequestParam String cardIds) {
		boolean result = businessCardService.batchUpdatePublishStatus(Func.toLongList(cardIds), PublishStatusEnum.PUBLISHED.getValue());
		return R.data(result);
	}

	/**
	 * 批量下架名片
	 */
	@PostMapping("/batch-offline")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "批量下架名片", description = "批量将名片设置为下架状态")
	public R<Boolean> batchOfflineCards(@Parameter(description = "名片ID列表，逗号分隔") @RequestParam String cardIds) {
		boolean result = businessCardService.batchUpdatePublishStatus(Func.toLongList(cardIds), PublishStatusEnum.DRAFT.getValue());
		return R.data(result);
	}

}
