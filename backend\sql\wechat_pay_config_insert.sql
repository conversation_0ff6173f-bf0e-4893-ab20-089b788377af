-- 微信支付配置插入脚本
-- 基于提供的商户信息配置

-- 插入微信支付配置
INSERT INTO `urb_wechat_pay_config` (
  `config_name`,
  `app_id`,
  `mch_id`,
  `api_key`,
  `api_v3_key`,
  `cert_path`,
  `cert_serial_no`,
  `private_key_path`,
  `notify_url`,
  `is_sandbox`,
  `is_enabled`,
  `is_default`,
  `priority`,
  `remark`,
  `create_time`,
  `create_user`,
  `update_time`,
  `update_user`,
  `tenant_id`
) VALUES (
  '默认商户配置',
  'wx5f0591468a438c48',
  '1722243412',
  'YOUR_API_KEY_32_CHARACTERS_HERE',  -- 需要在微信商户平台的API安全中设置32位API密钥
  'asdqweqwezxc12312312313233215AA2',
  'classpath:cert/wechat/apiclient_cert.pem',
  '2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E',
  'classpath:cert/wechat/apiclient_key.pem',  -- 需要提供私钥文件
  'https://wechat.langchuanxinxi.cn/blade-chat/pay/notify',   -- 支付回调地址
  0,  -- 非沙箱环境
  1,  -- 启用
  1,  -- 设为默认配置
  100, -- 优先级
  '生产环境微信支付配置 - 小程序ID: wx5f0591468a438c48, 商户号: 1722243412',
  NOW(),
  1,  -- 创建用户ID
  NOW(),
  1,  -- 更新用户ID
  '000000'  -- 默认租户ID
);



-- 验证插入结果
SELECT
  id,
  config_name,
  app_id,
  mch_id,
  LEFT(api_key, 8) as api_key_preview,
  LEFT(api_v3_key, 8) as api_v3_key_preview,
  cert_path,
  cert_serial_no,
  private_key_path,
  notify_url,
  is_sandbox,
  is_enabled,
  is_default,
  priority,
  remark,
  create_time,
  tenant_id
FROM `urb_wechat_pay_config`
WHERE app_id = 'wx5f0591468a438c48' AND mch_id = '1722243412'
ORDER BY priority DESC, create_time DESC;

-- 显示配置统计
SELECT
  '=== 微信支付配置插入完成 ===' as status,
  NOW() as insert_time;

SELECT
  COUNT(*) as total_configs,
  SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_configs,
  SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as default_configs,
  SUM(CASE WHEN is_sandbox = 1 THEN 1 ELSE 0 END) as sandbox_configs
FROM `urb_wechat_pay_config`
WHERE app_id = 'wx5f0591468a438c48';
