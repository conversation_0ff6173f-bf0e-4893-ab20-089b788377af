/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.service.impl;


import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.mapper.InstitutionTypeMapper;
import org.springblade.business.institution.service.IInstitutionTypeService;
import org.springblade.business.institution.vo.InstitutionTypeVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 机构分类表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class InstitutionTypeServiceImpl extends BaseServiceImpl<InstitutionTypeMapper, InstitutionType> implements IInstitutionTypeService {

	@Override
	public IPage<InstitutionTypeVO> selectInstitutionTypePage(IPage<InstitutionTypeVO> page, InstitutionTypeVO institutionType) {
		return page.setRecords(baseMapper.selectInstitutionTypePage(page, institutionType));
	}

}
