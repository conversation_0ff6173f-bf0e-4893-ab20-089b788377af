
package org.springblade.business.post.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 消息创建DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "消息创建DTO")
public class MessageCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "接收消息的用户ID", required = true)
    private Long userId;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型（对应消息模板的typeCode）", required = true)
    private String messageType;

    /**
     * 模板参数
     */
    @Schema(description = "消息模板填充参数", required = true)
    private Map<String, Object> templateParams;

    /**
     * 关联ID
     */
    @Schema(description = "关联业务ID（如订单ID、通知ID等）")
    private Long relatedId;
}
