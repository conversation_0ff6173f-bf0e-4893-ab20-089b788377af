<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.UrbJobOfferMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="jobOfferResultMap" type="org.springblade.business.post.entity.JobOffer">
        <result column="id" property="id"/>
        <result column="post_id" property="postId"/>
        <result column="recruit_type" property="recruitType"/>
        <result column="workplace" property="workplace"/>
        <result column="job_title" property="jobTitle"/>
        <result column="headcount" property="headcount"/>
        <result column="salary" property="salary"/>
        <result column="job_keywords" property="jobKeywords"/>
        <result column="job_description" property="jobDescription"/>
        <result column="company_name" property="companyName"/>
    </resultMap>
    <delete id="deleteByPostId">
            DELETE FROM urb_job_offer WHERE post_id = #{postId}
    </delete>


    <select id="selectJobOfferPage" resultMap="jobOfferResultMap">
        select * from urb_job_offer where is_deleted = 0
    </select>

</mapper>
