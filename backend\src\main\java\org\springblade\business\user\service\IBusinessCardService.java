/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 名片信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IBusinessCardService extends BaseService<BusinessCard> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param businessCard
	 * @return
	 */
	IPage<BusinessCardVO> selectBusinessCardPage(IPage<BusinessCardVO> page, BusinessCardVO businessCard);

	BusinessCardVO getDetail(BusinessCardVO businessCard);


	List<BusinessCard> listByUserId(Long userId);

	/**
	 * 审核名片
	 *
	 * @param cardId 名片ID
	 * @param auditStatus 审核状态（1-通过，2-拒绝）
	 * @param auditRemark 审核备注
	 * @return 是否成功
	 */
	Boolean auditBusinessCard(Long cardId, String auditStatus, String auditRemark);

	/**
	 * 批量审核名片
	 *
	 * @param cardIds 名片ID列表
	 * @param auditStatus 审核状态（1-通过，2-拒绝）
	 * @param auditRemark 审核备注
	 * @return 是否成功
	 */
	Boolean batchAuditBusinessCards(List<Long> cardIds, String auditStatus, String auditRemark);

	/**
	 * 修改名片发布状态
	 *
	 * @param id 名片ID
	 * @param publishStatus 发布状态（0-下架，1-上架）
	 * @return 是否成功
	 */
	Boolean updatePublishStatus(Long id, String publishStatus);

	/**
	 * 批量修改名片发布状态
	 *
	 * @param cardIds 名片ID列表
	 * @param publishStatus 发布状态（0-下架，1-上架）
	 * @return 是否成功
	 */
	Boolean batchUpdatePublishStatus(List<Long> cardIds, String publishStatus);
}
